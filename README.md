## Description
ESG Backend - Nestjs Framework

## Installation
```bash
$ npm install
```

## Running the app
```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Test
```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Prisma
```bash
# migrate tables for local
$ npx prisma migrate dev

# migrate tables for local with specific name and create only before migrate dev
$ npx prisma migrate dev --create-only --name "migration_name"
$ npx prisma migrate dev

# migrate tables for production
$ npx prisma migrate deploy

# generate client, but firstly stop nestjs server
$ npx prisma generate
```
