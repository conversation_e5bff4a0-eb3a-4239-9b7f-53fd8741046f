APP_NAME="T4EARTH"
APP_ENV=local
APP_DEBUG=true
APP_HOST=127.0.0.1
APP_PORT=3000
APP_API_URL=http://127.0.0.1:3000/api
APP_PUBLIC_URL=http://127.0.0.1:3000/public

TZ=UTC

DATABASE_URL="mysql://root:password@127.0.0.1:3306/esg?timezone=Z&connection_limit=5"

CORS_ORIGINS="http://127.0.0.1:3000; https://test.25proje.tech"

AWS_ENDPOINT=https://storage.example.com
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=t4earth
AWS_USE_PATH_STYLE_ENDPOINT=true

UPLOAD_MAX_FILE_SIZE=10000000 # 10 mb

MAIL_HOST=smtp.example.com
MAIL_PORT=465
MAIL_USER=<EMAIL>
MAIL_PASSWORD=password
MAIL_FROM=<EMAIL>

FRONT_URL="http://127.0.0.1:3001"

CARBON_URL="http://127.0.0.1:8000"
SUPER_ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_PASSWORD=password