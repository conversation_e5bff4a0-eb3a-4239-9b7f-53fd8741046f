/*
  Warnings:

  - You are about to drop the `organization_stakeholder_defaults` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `organization_stakeholder_users` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `organization_stakeholders` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `opinion_helps` DROP FOREIGN KEY `opinion_helps_stakeholder_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_stakeholder_users` DROP FOREIGN KEY `organization_stakeholder_users_created_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_stakeholder_users` DROP FOREIGN KEY `organization_stakeholder_users_stakeholder_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_stakeholders` DROP FOREIGN KEY `organization_stakeholders_created_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_stakeholders` DROP FOREIGN KEY `organization_stakeholders_default_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_stakeholders` DROP FOREIGN KEY `organization_stakeholders_organization_id_fkey`;

-- DropIndex
DROP INDEX `opinion_helps_stakeholder_user_id_fkey` ON `opinion_helps`;

-- DropIndex
DROP INDEX `organization_stakeholder_users_created_user_id_fkey` ON `organization_stakeholder_users`;

-- DropIndex
DROP INDEX `organization_stakeholder_users_stakeholder_id_fkey` ON `organization_stakeholder_users`;

-- DropIndex
DROP INDEX `organization_stakeholders_created_user_id_fkey` ON `organization_stakeholders`;

-- DropIndex
DROP INDEX `organization_stakeholders_default_id_fkey` ON `organization_stakeholders`;

-- DropIndex
DROP INDEX `organization_stakeholders_organization_id_fkey` ON `organization_stakeholders`;

-- DropTable
RENAME TABLE `organization_stakeholder_defaults` TO `stakeholder_defaults`;

-- DropTable
RENAME TABLE `organization_stakeholder_users` TO `stakeholder_users`;

-- DropTable
RENAME TABLE `organization_stakeholders` TO `stakeholders`;

ALTER TABLE `stakeholders`
    DROP COLUMN `comm_method`,
    DROP COLUMN `comm_frequency`,
    DROP COLUMN `comm_frequency_text`;

-- CreateTable
CREATE TABLE `stakeholder_communications` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `stakeholder_id` INTEGER UNSIGNED NOT NULL,
    `method_type` TINYINT UNSIGNED NULL,
    `frequency_type` TINYINT UNSIGNED NULL,
    `frequency_text` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `stakeholders` ADD CONSTRAINT `stakeholders_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `stakeholders` ADD CONSTRAINT `stakeholders_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `stakeholders` ADD CONSTRAINT `stakeholders_default_id_fkey` FOREIGN KEY (`default_id`) REFERENCES `stakeholder_defaults`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `stakeholder_communications` ADD CONSTRAINT `stakeholder_communications_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `stakeholder_communications` ADD CONSTRAINT `stakeholder_communications_stakeholder_id_fkey` FOREIGN KEY (`stakeholder_id`) REFERENCES `stakeholders`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `stakeholder_users` ADD CONSTRAINT `stakeholder_users_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `stakeholder_users` ADD CONSTRAINT `stakeholder_users_stakeholder_id_fkey` FOREIGN KEY (`stakeholder_id`) REFERENCES `stakeholders`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `opinion_helps` ADD CONSTRAINT `opinion_helps_stakeholder_user_id_fkey` FOREIGN KEY (`stakeholder_user_id`) REFERENCES `stakeholder_users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
