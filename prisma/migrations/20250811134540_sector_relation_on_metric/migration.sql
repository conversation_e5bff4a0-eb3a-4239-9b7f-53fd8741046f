-- DropForeign<PERSON>ey
ALTER TABLE `metrics` DROP FOREIGN KEY `metrics_strategy_detail_id_fkey`;

-- DropIndex
DROP INDEX `metrics_strategy_detail_id_fkey` ON `metrics`;

-- AlterTable
ALTER TABLE `metrics` ADD COLUMN `is_activity` BO<PERSON>EAN NOT NULL DEFAULT false AFTER `note`,
    MODIFY `strategy_detail_id` INTEGER UNSIGNED NULL;

-- AddFore<PERSON><PERSON>ey
ALTER TABLE `metrics` ADD CONSTRAINT `metrics_strategy_detail_id_fkey` FOREIGN KEY (`strategy_detail_id`) REFERENCES `strategy_details`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
