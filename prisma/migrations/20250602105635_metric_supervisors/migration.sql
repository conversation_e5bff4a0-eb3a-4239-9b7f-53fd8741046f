/*
  Warnings:

  - You are about to drop the column `metric_default_id` on the `industries` table. All the data in the column will be lost.
  - You are about to drop the column `metric_default_id` on the `industry_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `helped_email` on the `metric_helps` table. All the data in the column will be lost.
  - You are about to drop the column `token` on the `metric_helps` table. All the data in the column will be lost.
  - You are about to drop the column `token_expired_at` on the `metric_helps` table. All the data in the column will be lost.
  - Made the column `helped_user_id` on table `metric_helps` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `industries` DROP FOREIGN KEY `industries_metric_default_id_fkey`;

-- DropForeignKey
ALTER TABLE `industry_defaults` DROP FOREIGN KEY `industry_defaults_metric_default_id_fkey`;

-- DropForeignKey
ALTER TABLE `metric_helps` DROP FOREIGN KEY `metric_helps_helped_user_id_fkey`;

-- DropIndex
DROP INDEX `industries_metric_default_id_fkey` ON `industries`;

-- DropIndex
DROP INDEX `industry_defaults_metric_default_id_fkey` ON `industry_defaults`;

-- DropIndex
DROP INDEX `metric_helps_helped_user_id_fkey` ON `metric_helps`;

-- DropIndex
DROP INDEX `metric_helps_token_key` ON `metric_helps`;

-- AlterTable
ALTER TABLE `industries`
    DROP COLUMN `metric_default_id`,
    ADD COLUMN `metric_default_ids` JSON NULL AFTER `report_id`;

-- AlterTable
ALTER TABLE `industry_defaults`
    DROP COLUMN `metric_default_id`,
    ADD COLUMN `metric_default_ids` JSON NULL AFTER `id`;

-- AlterTable
ALTER TABLE `metric_defaults` ADD COLUMN `note` TEXT NULL AFTER `unit_type`;

-- AlterTable
ALTER TABLE `metric_helps`
    DROP COLUMN `helped_email`,
    DROP COLUMN `token`,
    DROP COLUMN `token_expired_at`,
    MODIFY `helped_user_id` INTEGER UNSIGNED NOT NULL;

-- CreateTable
CREATE TABLE `metric_supervisors` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `metric_id` INTEGER UNSIGNED NOT NULL,
    `email` VARCHAR(100) NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `goal_name` VARCHAR(191) NULL,
    `token` VARCHAR(191) NOT NULL,
    `token_expired_at` DATETIME(3) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    UNIQUE INDEX `metric_supervisors_token_key`(`token`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `metric_supervisor_performances` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `supervisor_id` INTEGER UNSIGNED NOT NULL,
    `performance_id` INTEGER UNSIGNED NOT NULL,
    `value` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `metric_helps` ADD CONSTRAINT `metric_helps_helped_user_id_fkey` FOREIGN KEY (`helped_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_supervisors` ADD CONSTRAINT `metric_supervisors_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_supervisors` ADD CONSTRAINT `metric_supervisors_metric_id_fkey` FOREIGN KEY (`metric_id`) REFERENCES `metrics`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_supervisor_performances` ADD CONSTRAINT `metric_supervisor_performances_supervisor_id_fkey` FOREIGN KEY (`supervisor_id`) REFERENCES `metric_supervisors`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_supervisor_performances` ADD CONSTRAINT `metric_supervisor_performances_performance_id_fkey` FOREIGN KEY (`performance_id`) REFERENCES `metric_performances`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
