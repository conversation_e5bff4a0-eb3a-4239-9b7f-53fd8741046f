-- DropFore<PERSON><PERSON>ey
ALTER TABLE `strategy_reports` DROP FOREIGN KEY `strategy_reports_financial_report_id_fkey`;

-- DropForeignKey
ALTER TABLE `strategy_reports` DROP FOREIGN KEY `strategy_reports_impact_report_id_fkey`;

-- DropIndex
DROP INDEX `strategy_reports_financial_report_id_fkey` ON `strategy_reports`;

-- DropIndex
DROP INDEX `strategy_reports_impact_report_id_fkey` ON `strategy_reports`;

-- AlterTable
ALTER TABLE `strategy_reports` MODIFY `financial_report_id` INTEGER UNSIGNED NULL,
    MODIFY `impact_report_id` INTEGER UNSIGNED NULL;

-- AddForeignKey
ALTER TABLE `strategy_reports` ADD CONSTRAINT `strategy_reports_financial_report_id_fkey` FOREIGN KEY (`financial_report_id`) REFERENCES `financial_reports`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddFore<PERSON><PERSON>ey
ALTER TABLE `strategy_reports` ADD CONSTRAINT `strategy_reports_impact_report_id_fkey` FOREI<PERSON>N KEY (`impact_report_id`) REFERENCES `impact_reports`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
