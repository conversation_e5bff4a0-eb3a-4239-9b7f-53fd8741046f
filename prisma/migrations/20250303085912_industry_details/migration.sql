/*
  Warnings:

  - You are about to drop the column `focus_area` on the `industries` table. All the data in the column will be lost.
  - You are about to drop the column `priority_issue` on the `industries` table. All the data in the column will be lost.
  - You are about to drop the column `source` on the `industries` table. All the data in the column will be lost.
  - You are about to drop the column `focus_area` on the `industry_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `priority_issue` on the `industry_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `source` on the `industry_defaults` table. All the data in the column will be lost.
  - Made the column `sector_ids` on table `industries` required. This step will fail if there are existing NULL values in that column.
  - Made the column `sector_ids` on table `industry_defaults` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `industries`
    DROP COLUMN `focus_area`,
    DROP COLUMN `priority_issue`,
    DROP COLUMN `source`,
    MODIFY `sector_ids` JSON NOT NULL AFTER `report_id`,
    ADD COLUMN `source_ids` JSON NULL AFTER `sector_ids`,
    ADD COLUMN `source_text` VARCHAR(191) NULL AFTER `source_ids`;

-- AlterTable
ALTER TABLE `industry_defaults`
    DROP COLUMN `focus_area`,
    DROP COLUMN `priority_issue`,
    DROP COLUMN `source`,
    MODIFY `sector_ids` JSON NOT NULL AFTER `id`,
    ADD COLUMN `source_ids` JSON NULL AFTER `sector_ids`,
    ADD COLUMN `source_text` VARCHAR(191) NULL AFTER `source_ids`;

-- CreateTable
CREATE TABLE `industry_details` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `industry_id` INTEGER UNSIGNED NOT NULL,
    `focus_area` VARCHAR(191) NOT NULL,
    `priority_issue` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `industry_default_details` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `default_id` INTEGER UNSIGNED NOT NULL,
    `focus_area` VARCHAR(191) NOT NULL,
    `priority_issue` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `industry_details` ADD CONSTRAINT `industry_details_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `industry_details` ADD CONSTRAINT `industry_details_industry_id_fkey` FOREIGN KEY (`industry_id`) REFERENCES `industries`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `industry_default_details` ADD CONSTRAINT `industry_default_details_default_id_fkey` FOREIGN KEY (`default_id`) REFERENCES `industry_defaults`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
