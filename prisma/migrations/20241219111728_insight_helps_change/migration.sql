-- AlterTable
ALTER TABLE `industry_help_answers`
    MODIFY `financial_question_answer` TINYINT UNSIGNED NULL,
    MODIFY `impact_question_answer` TINYINT UNSIGNED NULL;

-- AlterTable
ALTER TABLE `industry_helps`
    ADD COLUMN `is_financial` BOOLEAN NOT NULL DEFAULT false AFTER `report_id`,
    ADD COLUMN `is_impact` BOOLEAN NOT NULL DEFAULT false AFTER `is_financial`;

-- AlterTable
ALTER TABLE `opinion_help_answers`
    MODIFY `financial_question_answer` TINYINT UNSIGNED NULL,
    MODIFY `impact_question_answer` TINYINT UNSIGNED NULL;

-- AlterTable
ALTER TABLE `opinion_helps`
    ADD COLUMN `is_financial` BOOLEAN NOT NULL DEFAULT false AFTER `report_id`,
    ADD COLUMN `is_impact` BOOLEAN NOT NULL DEFAULT false AFTER `is_financial`;

-- AlterTable
ALTER TABLE `tendency_help_answers`
    MODIFY `financial_question_answer` TINYINT UNSIGNED NULL,
    M<PERSON><PERSON>Y `impact_question_answer` TINYINT UNSIGNED NULL;

-- AlterTable
ALTER TABLE `tendency_helps`
    ADD COLUMN `is_financial` BOOLEAN NOT NULL DEFAULT false AFTER `report_id`,
    ADD COLUMN `is_impact` BOOLEAN NOT NULL DEFAULT false AFTER `is_financial`;
