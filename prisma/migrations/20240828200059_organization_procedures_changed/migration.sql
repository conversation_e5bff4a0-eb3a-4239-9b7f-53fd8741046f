/*
  Warnings:

  - You are about to drop the column `url` on the `organization_procedures` table. All the data in the column will be lost.
  - Added the required column `category_id` to the `organization_procedures` table without a default value. This is not possible if the table is not empty.
  - Added the required column `urls` to the `organization_procedures` table without a default value. This is not possible if the table is not empty.
  - Made the column `slug` on table `procedures` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `organization_procedures` DROP COLUMN `url`,
    ADD COLUMN `category_id` INTEGER UNSIGNED NOT NULL AFTER `procedure_id`,
    ADD COLUMN `suggestion_id` INTEGER UNSIGNED NULL AFTER `category_id`,
    ADD COLUMN `urls` JSON NOT NULL AFTER `description`;

-- AlterTable
ALTER TABLE `procedures` MODIFY `slug` VARCHAR (191) NOT NULL;

-- AddForeignKey
ALTER TABLE `organization_procedures`
    ADD CONSTRAINT `organization_procedures_category_id_fkey` FOREIGN KEY (`category_id`) REFERENCES `procedure_categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `organization_procedures`
    ADD CONSTRAINT `organization_procedures_suggestion_id_fkey` FOREIGN KEY (`suggestion_id`) REFERENCES `procedure_suggestions` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
