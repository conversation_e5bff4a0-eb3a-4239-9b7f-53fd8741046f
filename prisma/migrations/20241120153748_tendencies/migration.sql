-- AlterTable
ALTER TABLE `financial_reports`
    ADD COLUMN `tendency_report_id` INTEGER UNSIGNED NULL AFTER `industry_report_id`;

-- AlterTable
ALTER TABLE `financials`
    ADD COLUMN `tendency_id` INTEGER UNSIGNED NULL AFTER `industry_id`,
    M<PERSON>IFY `sub_priority_issue` VARCHAR(191) NULL,
    MODIFY `description` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `tendencies` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `focus_area` VARCHAR(191) NOT NULL,
    `priority_issue` VARCHAR(191) NOT NULL,
    `source` VARCHAR(191) NOT NULL,
    `period_type` TINYINT UNSIGNED NOT NULL,
    `is_default` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `tendency_reports` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `financial_threshold` TINYINT UNSIGNED NULL,
    `impact_threshold` TINYINT UNSIGNED NULL,
    `is_completed` BOOLEAN NOT NULL DEFAULT false,
    `expired_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `tendency_defaults` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `focus_area` VARCHAR(191) NOT NULL,
    `priority_issue` VARCHAR(191) NOT NULL,
    `source` VARCHAR(191) NOT NULL,
    `period_type` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `tendency_helps` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `helped_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `tendency_help_answers` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `tendency_id` INTEGER UNSIGNED NOT NULL,
    `help_id` INTEGER UNSIGNED NOT NULL,
    `financial_question_answer` TINYINT UNSIGNED NOT NULL,
    `impact_question_answer` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `tendencies` ADD CONSTRAINT `tendencies_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tendencies` ADD CONSTRAINT `tendencies_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `tendency_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tendency_reports` ADD CONSTRAINT `tendency_reports_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tendency_reports` ADD CONSTRAINT `tendency_reports_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tendency_helps` ADD CONSTRAINT `tendency_helps_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tendency_helps` ADD CONSTRAINT `tendency_helps_helped_user_id_fkey` FOREIGN KEY (`helped_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tendency_helps` ADD CONSTRAINT `tendency_helps_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `tendency_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tendency_help_answers` ADD CONSTRAINT `tendency_help_answers_tendency_id_fkey` FOREIGN KEY (`tendency_id`) REFERENCES `tendencies`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `tendency_help_answers` ADD CONSTRAINT `tendency_help_answers_help_id_fkey` FOREIGN KEY (`help_id`) REFERENCES `tendency_helps`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financials` ADD CONSTRAINT `financials_tendency_id_fkey` FOREIGN KEY (`tendency_id`) REFERENCES `tendencies`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_reports` ADD CONSTRAINT `financial_reports_tendency_report_id_fkey` FOREIGN KEY (`tendency_report_id`) REFERENCES `tendency_reports`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
