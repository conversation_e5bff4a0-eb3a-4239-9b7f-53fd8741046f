/*
  Warnings:

  - Made the column `size` on table `file_manager` required. This step will fail if there are existing NULL values in that column.
  - Made the column `code` on table `financial_reports` required. This step will fail if there are existing NULL values in that column.
  - Made the column `code` on table `impact_reports` required. This step will fail if there are existing NULL values in that column.
  - Made the column `code` on table `industry_reports` required. This step will fail if there are existing NULL values in that column.
  - Made the column `code` on table `opinion_reports` required. This step will fail if there are existing NULL values in that column.
  - Made the column `category_button` on table `procedures` required. This step will fail if there are existing NULL values in that column.
  - Made the column `policy_button` on table `procedures` required. This step will fail if there are existing NULL values in that column.
  - Made the column `sub_priority_issue` on table `tendencies` required. This step will fail if there are existing NULL values in that column.
  - Made the column `sub_priority_issue` on table `tendency_defaults` required. This step will fail if there are existing NULL values in that column.
  - Made the column `code` on table `tendency_reports` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `file_manager` MODIFY `size` INTEGER UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `financial_reports` MODIFY `code` SMALLINT UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `impact_reports` MODIFY `code` SMALLINT UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `industry_reports` MODIFY `code` SMALLINT UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `opinion_reports` MODIFY `code` SMALLINT UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `procedures` MODIFY `category_button` VARCHAR(191) NOT NULL,
    MODIFY `policy_button` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `tendencies` MODIFY `sub_priority_issue` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `tendency_defaults` MODIFY `sub_priority_issue` VARCHAR(191) NOT NULL;

-- AlterTable
ALTER TABLE `tendency_reports` MODIFY `code` SMALLINT UNSIGNED NOT NULL;
