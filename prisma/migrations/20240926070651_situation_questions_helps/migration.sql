-- CreateTable
CREATE TABLE `situation_questions_helps` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `helped_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `question_id` INTEGER UNSIGNED NOT NULL,
    `help_type` TINYINT UNSIGNED NOT NULL,
    `status` TINYINT UNSIGNED NOT NULL DEFAULT 0,
    `description` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `situation_questions_helps` ADD CONSTRAINT `situation_questions_helps_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `situation_questions_helps` ADD CONSTRAINT `situation_questions_helps_helped_user_id_fkey` FOREIGN KEY (`helped_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `situation_questions_helps` ADD CONSTRAINT `situation_questions_helps_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `situation_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `situation_questions_helps` ADD CONSTRAINT `situation_questions_helps_question_id_fkey` FOREIGN KEY (`question_id`) REFERENCES `situation_questions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
