/*
  Warnings:

  - You are about to drop the column `status` on the `industry_helps` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `opinion_helps` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `tendency_helps` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `industry_helps` DROP COLUMN `status`,
    ADD COLUMN `financial_status` TINYINT NOT NULL DEFAULT 0 AFTER `is_impact`,
    ADD COLUMN `impact_status` TINYINT NOT NULL DEFAULT 0 AFTER `financial_status`;

-- AlterTable
ALTER TABLE `opinion_helps` DROP COLUMN `status`,
    ADD COLUMN `financial_status` TINYINT NOT NULL DEFAULT 0 AFTER `is_impact`,
    ADD COLUMN `impact_status` TINYINT NOT NULL DEFAULT 0 AFTER `financial_status`;

-- AlterTable
ALTER TABLE `tendency_helps` DROP COLUMN `status`,
    ADD COLUMN `financial_status` TINYINT NOT NULL DEFAULT 0 AFTER `is_impact`,
    ADD COLUMN `impact_status` TINYINT NOT NULL DEFAULT 0 AFTER `financial_status`;
