-- CreateTable
CREATE TABLE `strategies` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `updated_user_id` INTEGER UNSIGNED NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `strategy_focus_area` VARCHAR(191) NULL,
    `description` VARCHAR(3000) NULL,
    `short_term` VARCHAR(3000) NULL,
    `medium_term` VARCHAR(3000) NULL,
    `long_term` VARCHAR(3000) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `strategy_details` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `strategy_id` INTEGER UNSIGNED NOT NULL,
    `priority_issue` VARCHAR(191) NOT NULL,
    `focus_areas` JSON NOT NULL,
    `financial_category_types` JSON NOT NULL,
    `impact_category_types` JSON NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `strategy_reports` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `financial_report_id` INTEGER UNSIGNED NOT NULL,
    `impact_report_id` INTEGER UNSIGNED NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `code` SMALLINT UNSIGNED NOT NULL,
    `is_road_map` BOOLEAN NOT NULL DEFAULT false,
    `is_completed` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `strategy_helps` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `helped_user_id` INTEGER UNSIGNED NOT NULL,
    `strategy_id` INTEGER UNSIGNED NOT NULL,
    `help_type` TINYINT UNSIGNED NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `strategy_focus_area` VARCHAR(191) NULL,
    `description` VARCHAR(3000) NULL,
    `short_term` VARCHAR(3000) NULL,
    `medium_term` VARCHAR(3000) NULL,
    `long_term` VARCHAR(3000) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `strategy_terms` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `min_value` TINYINT UNSIGNED NOT NULL,
    `max_value` TINYINT UNSIGNED NOT NULL,
    `type` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `strategies` ADD CONSTRAINT `strategies_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategies` ADD CONSTRAINT `strategies_updated_user_id_fkey` FOREIGN KEY (`updated_user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategies` ADD CONSTRAINT `strategies_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `strategy_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_details` ADD CONSTRAINT `strategy_details_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_details` ADD CONSTRAINT `strategy_details_strategy_id_fkey` FOREIGN KEY (`strategy_id`) REFERENCES `strategies`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_reports` ADD CONSTRAINT `strategy_reports_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_reports` ADD CONSTRAINT `strategy_reports_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_reports` ADD CONSTRAINT `strategy_reports_financial_report_id_fkey` FOREIGN KEY (`financial_report_id`) REFERENCES `financial_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_reports` ADD CONSTRAINT `strategy_reports_impact_report_id_fkey` FOREIGN KEY (`impact_report_id`) REFERENCES `impact_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_helps` ADD CONSTRAINT `strategy_helps_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_helps` ADD CONSTRAINT `strategy_helps_helped_user_id_fkey` FOREIGN KEY (`helped_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_helps` ADD CONSTRAINT `strategy_helps_strategy_id_fkey` FOREIGN KEY (`strategy_id`) REFERENCES `strategies`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_terms` ADD CONSTRAINT `strategy_terms_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_terms` ADD CONSTRAINT `strategy_terms_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `strategy_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
