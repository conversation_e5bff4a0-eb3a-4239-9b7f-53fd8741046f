/*
  Warnings:

  - Added the required column `created_user_id` to the `organization_stakeholder_users` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `organization_stakeholder_users`
    ADD COLUMN `created_user_id` INTEGER UNSIGNED NOT NULL AFTER `id`;

-- AddForeignKey
ALTER TABLE `organization_stakeholder_users`
    ADD CONSTRAINT `organization_stakeholder_users_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
