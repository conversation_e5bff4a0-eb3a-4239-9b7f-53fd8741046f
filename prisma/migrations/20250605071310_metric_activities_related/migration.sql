/*
  Warnings:

  - You are about to drop the column `status` on the `metric_helps` table. All the data in the column will be lost.
  - Added the required column `period_type` to the `metric_supervisors` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `metric_activities`
    ADD COLUMN `related_id` INTEGER UNSIGNED NULL AFTER `activity_type`,
    ADD COLUMN `value` VARCHAR(191) NULL AFTER `related_id`;

-- AlterTable
ALTER TABLE `metric_helps` DROP COLUMN `status`;

-- AlterTable
ALTER TABLE `metric_supervisors`
    ADD COLUMN `period_type` TINYINT UNSIGNED NOT NULL AFTER `profile_id`;
