-- AlterTable
ALTER TABLE `capitals` ADD COLUMN `updated_user_id` INTEGER UNSIGNED NULL AFTER `created_user_id`;

-- AlterTable
ALTER TABLE `chains` ADD COLUMN `updated_user_id` INTEGER UNSIGNED NULL AFTER `created_user_id`;

-- AddForeignKey
ALTER TABLE `chains` ADD CONSTRAINT `chains_updated_user_id_fkey` FOREIGN KEY (`updated_user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `capitals` ADD CONSTRAINT `capitals_updated_user_id_fkey` FOREIGN KEY (`updated_user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
