/*
  Warnings:

  - You are about to drop the column `department` on the `metric_supervisors` table. All the data in the column will be lost.
  - You are about to drop the column `email` on the `metric_supervisors` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `metric_supervisors` table. All the data in the column will be lost.
  - You are about to drop the column `status` on the `metric_supervisors` table. All the data in the column will be lost.
  - You are about to drop the column `surname` on the `metric_supervisors` table. All the data in the column will be lost.
  - You are about to drop the column `token` on the `metric_supervisors` table. All the data in the column will be lost.
  - You are about to drop the column `token_expired_at` on the `metric_supervisors` table. All the data in the column will be lost.
  - You are about to drop the column `updated_user_id` on the `metrics` table. All the data in the column will be lost.
  - Added the required column `profile_id` to the `metric_supervisors` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `metrics` DROP FOREIGN KEY `metrics_updated_user_id_fkey`;

-- DropIndex
DROP INDEX `metric_supervisors_token_key` ON `metric_supervisors`;

-- DropIndex
DROP INDEX `metrics_updated_user_id_fkey` ON `metrics`;

-- AlterTable
ALTER TABLE `metric_supervisors`
    DROP COLUMN `department`,
    DROP COLUMN `email`,
    DROP COLUMN `name`,
    DROP COLUMN `status`,
    DROP COLUMN `surname`,
    DROP COLUMN `token`,
    DROP COLUMN `token_expired_at`,
    ADD COLUMN `profile_id` INTEGER UNSIGNED NOT NULL AFTER `metric_id`;

-- AlterTable
ALTER TABLE `metrics` DROP COLUMN `updated_user_id`;

-- CreateTable
CREATE TABLE `metric_supervisor_profiles` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `department` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `metric_supervisors` ADD CONSTRAINT `metric_supervisors_profile_id_fkey` FOREIGN KEY (`profile_id`) REFERENCES `metric_supervisor_profiles`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_supervisor_profiles` ADD CONSTRAINT `metric_supervisor_profiles_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_supervisor_profiles` ADD CONSTRAINT `metric_supervisor_profiles_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
