/*
  Warnings:

  - You are about to drop the column `financial_category_description` on the `strategy_detail_categories` table. All the data in the column will be lost.
  - You are about to drop the column `financial_category_type` on the `strategy_detail_categories` table. All the data in the column will be lost.
  - You are about to drop the column `financial_period_type` on the `strategy_detail_categories` table. All the data in the column will be lost.
  - You are about to drop the column `impact_category_description` on the `strategy_detail_categories` table. All the data in the column will be lost.
  - You are about to drop the column `impact_category_type` on the `strategy_detail_categories` table. All the data in the column will be lost.
  - You are about to drop the column `impact_reality_type` on the `strategy_detail_categories` table. All the data in the column will be lost.
  - You are about to alter the column `image` on the `users` table. The data in that column could be lost. The data in that column will be cast from `VarChar(300)` to `VarChar(191)`.

*/
-- AlterTable
ALTER TABLE `industries`
    ADD COLUMN `metric_default_id` INTEGER UNSIGNED NULL AFTER `report_id`;

-- AlterTable
ALTER TABLE `industry_defaults`
    ADD COLUMN `metric_default_id` INTEGER UNSIGNED NULL AFTER `id`;

-- AlterTable
ALTER TABLE `strategy_detail_categories`
    DROP COLUMN `financial_category_description`,
    DROP COLUMN `financial_category_type`,
    DROP COLUMN `financial_period_type`,
    DROP COLUMN `impact_category_description`,
    DROP COLUMN `impact_category_type`,
    DROP COLUMN `impact_reality_type`,
    ADD COLUMN `financial_detail_id` INTEGER UNSIGNED NULL AFTER `detail_id`,
    ADD COLUMN `impact_detail_id` INTEGER UNSIGNED NULL AFTER `financial_detail_id`;

-- AlterTable
ALTER TABLE `strategy_details`
    ADD COLUMN `financial_ids` JSON NULL AFTER `strategy_id`,
    ADD COLUMN `impact_ids` JSON NULL AFTER `financial_ids`;

-- AlterTable
ALTER TABLE `users` MODIFY `image` VARCHAR(191) NULL;

-- CreateTable
CREATE TABLE `metrics` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `updated_user_id` INTEGER UNSIGNED NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `strategy_detail_id` INTEGER UNSIGNED NOT NULL,
    `metric_name` VARCHAR(191) NULL,
    `goal_name` VARCHAR(191) NULL,
    `category_type` TINYINT UNSIGNED NULL,
    `unit_type` TINYINT UNSIGNED NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `metric_reports` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `governance_report_id` INTEGER UNSIGNED NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `code` SMALLINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `metric_defaults` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `metric_name` VARCHAR(191) NOT NULL,
    `category_type` TINYINT UNSIGNED NOT NULL,
    `unit_type` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `metric_performances` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `metric_id` INTEGER UNSIGNED NOT NULL,
    `year` SMALLINT UNSIGNED NOT NULL,
    `months` JSON NOT NULL,
    `value` VARCHAR(191) NOT NULL,
    `performance_type` TINYINT UNSIGNED NOT NULL,
    `is_based` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `metric_helps` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `helped_user_id` INTEGER UNSIGNED NOT NULL,
    `metric_id` INTEGER UNSIGNED NOT NULL,
    `help_type` TINYINT UNSIGNED NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `metric_name` VARCHAR(191) NULL,
    `goal_name` VARCHAR(191) NULL,
    `category_type` TINYINT UNSIGNED NULL,
    `unit_type` TINYINT UNSIGNED NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `metric_help_performances` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `help_id` INTEGER UNSIGNED NOT NULL,
    `performance_id` INTEGER UNSIGNED NOT NULL,
    `value` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `industries` ADD CONSTRAINT `industries_metric_default_id_fkey` FOREIGN KEY (`metric_default_id`) REFERENCES `metric_defaults`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `industry_defaults` ADD CONSTRAINT `industry_defaults_metric_default_id_fkey` FOREIGN KEY (`metric_default_id`) REFERENCES `metric_defaults`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_detail_categories` ADD CONSTRAINT `strategy_detail_categories_financial_detail_id_fkey` FOREIGN KEY (`financial_detail_id`) REFERENCES `financial_details`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_detail_categories` ADD CONSTRAINT `strategy_detail_categories_impact_detail_id_fkey` FOREIGN KEY (`impact_detail_id`) REFERENCES `impact_details`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metrics` ADD CONSTRAINT `metrics_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metrics` ADD CONSTRAINT `metrics_updated_user_id_fkey` FOREIGN KEY (`updated_user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metrics` ADD CONSTRAINT `metrics_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `metric_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metrics` ADD CONSTRAINT `metrics_strategy_detail_id_fkey` FOREIGN KEY (`strategy_detail_id`) REFERENCES `strategy_details`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_reports` ADD CONSTRAINT `metric_reports_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_reports` ADD CONSTRAINT `metric_reports_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_reports` ADD CONSTRAINT `metric_reports_governance_report_id_fkey` FOREIGN KEY (`governance_report_id`) REFERENCES `governance_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_performances` ADD CONSTRAINT `metric_performances_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_performances` ADD CONSTRAINT `metric_performances_metric_id_fkey` FOREIGN KEY (`metric_id`) REFERENCES `metrics`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_helps` ADD CONSTRAINT `metric_helps_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_helps` ADD CONSTRAINT `metric_helps_helped_user_id_fkey` FOREIGN KEY (`helped_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_helps` ADD CONSTRAINT `metric_helps_metric_id_fkey` FOREIGN KEY (`metric_id`) REFERENCES `metrics`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_help_performances` ADD CONSTRAINT `metric_help_performances_help_id_fkey` FOREIGN KEY (`help_id`) REFERENCES `metric_helps`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_help_performances` ADD CONSTRAINT `metric_help_performances_performance_id_fkey` FOREIGN KEY (`performance_id`) REFERENCES `metric_performances`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
