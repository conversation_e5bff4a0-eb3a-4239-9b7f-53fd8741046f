-- CreateTable
CREATE TABLE `organization_carbon_sessions` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `carbon_organization_id` INTEGER UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `organization_carbon_sessions_organization_id_key`(`organization_id`),
    UNIQUE INDEX `organization_carbon_sessions_carbon_organization_id_key`(`carbon_organization_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `organization_carbon_sessions` ADD CONSTRAINT `organization_carbon_sessions_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
