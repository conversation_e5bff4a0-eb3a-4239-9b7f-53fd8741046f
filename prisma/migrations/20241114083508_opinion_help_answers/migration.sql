/*
  Warnings:

  - You are about to drop the column `financial_question_answer` on the `opinion_helps` table. All the data in the column will be lost.
  - You are about to drop the column `impact_question_answer` on the `opinion_helps` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `opinion_helps` DROP COLUMN `financial_question_answer`,
    DROP COLUMN `impact_question_answer`;

-- AlterTable
ALTER TABLE `opinion_reports` MODIFY `expired_at` DATETIME(3) NULL;

-- CreateTable
CREATE TABLE `opinion_help_answers` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `opinion_id` INTEGER UNSIGNED NOT NULL,
    `help_id` INTEGER UNSIGNED NOT NULL,
    `financial_question_answer` TINYINT UNSIGNED NULL,
    `impact_question_answer` TINYINT UNSIGNED NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `opinion_help_answers` ADD CONSTRAINT `opinion_help_answers_opinion_id_fkey` FOREIGN KEY (`opinion_id`) REFERENCES `opinions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `opinion_help_answers` ADD CONSTRAINT `opinion_help_answers_help_id_fkey` FOREIGN KEY (`help_id`) REFERENCES `opinion_helps`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
