/*
  Warnings:

  - You are about to drop the column `financial_question_answer` on the `industry_helps` table. All the data in the column will be lost.
  - You are about to drop the column `impact_question_answer` on the `industry_helps` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `industry_helps` DROP COLUMN `financial_question_answer`,
    DROP COLUMN `impact_question_answer`;

-- CreateTable
CREATE TABLE `industry_help_answers` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `industry_id` INTEGER UNSIGNED NOT NULL,
    `help_id` INTEGER UNSIGNED NOT NULL,
    `financial_question_answer` TINYINT UNSIGNED NOT NULL,
    `impact_question_answer` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `industry_help_answers` ADD CONSTRAINT `industry_help_answers_industry_id_fkey` FOREIGN KEY (`industry_id`) REFERENCES `industries`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `industry_help_answers` ADD CONSTRAINT `industry_help_answers_help_id_fkey` FOREIGN KEY (`help_id`) REFERENCES `industry_helps`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
