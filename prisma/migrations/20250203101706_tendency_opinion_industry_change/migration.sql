/*
  Warnings:

  - You are about to drop the column `sub_priority_issue` on the `industries` table. All the data in the column will be lost.
  - You are about to drop the column `sub_priority_issue` on the `industry_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `period_type` on the `tendencies` table. All the data in the column will be lost.
  - You are about to drop the column `priority_issue` on the `tendencies` table. All the data in the column will be lost.
  - You are about to drop the column `sub_priority_issue` on the `tendencies` table. All the data in the column will be lost.
  - You are about to drop the column `period_type` on the `tendency_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `priority_issue` on the `tendency_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `sub_priority_issue` on the `tendency_defaults` table. All the data in the column will be lost.
  - Added the required column `description` to the `tendencies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `topic` to the `tendencies` table without a default value. This is not possible if the table is not empty.
  - Added the required column `description` to the `tendency_defaults` table without a default value. This is not possible if the table is not empty.
  - Added the required column `topic` to the `tendency_defaults` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `industries`
    DROP COLUMN `sub_priority_issue`,
    ADD COLUMN `sector_ids` JSON NULL AFTER `source`;

-- AlterTable
ALTER TABLE `industry_defaults`
    DROP COLUMN `sub_priority_issue`,
    ADD COLUMN `sector_ids` JSON NULL AFTER `source`;

-- AlterTable
ALTER TABLE `opinion_defaults`
    ADD COLUMN `minor_priority_issue` VARCHAR(191) NULL AFTER `sub_priority_issue`;

-- AlterTable
ALTER TABLE `opinions`
    ADD COLUMN `minor_priority_issue` VARCHAR(191) NULL AFTER `sub_priority_issue`;

-- AlterTable
ALTER TABLE `tendencies`
    DROP COLUMN `period_type`,
    CHANGE `priority_issue` `topic` VARCHAR(191) NOT NULL,
    CHANGE `sub_priority_issue` `description` VARCHAR(3000) NOT NULL AFTER `source`;

-- AlterTable
ALTER TABLE `tendency_defaults`
    DROP COLUMN `period_type`,
    CHANGE `priority_issue` `topic` VARCHAR(191) NOT NULL,
    CHANGE `sub_priority_issue` `description` VARCHAR(3000) NOT NULL AFTER `source`;
