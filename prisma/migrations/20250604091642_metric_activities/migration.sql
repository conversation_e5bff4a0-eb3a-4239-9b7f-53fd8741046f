/*
  Warnings:

  - You are about to drop the column `goal_name` on the `metric_helps` table. All the data in the column will be lost.
  - You are about to drop the column `goal_name` on the `metric_supervisors` table. All the data in the column will be lost.
  - You are about to drop the `metric_help_performances` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `metric_supervisor_performances` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `department` to the `metric_supervisors` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name` to the `metric_supervisors` table without a default value. This is not possible if the table is not empty.
  - Added the required column `surname` to the `metric_supervisors` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `metric_help_performances` DROP FOREIGN KEY `metric_help_performances_help_id_fkey`;

-- DropForeignKey
ALTER TABLE `metric_help_performances` DROP FOREIGN KEY `metric_help_performances_performance_id_fkey`;

-- DropForeignKey
ALTER TABLE `metric_supervisor_performances` DROP FOREIGN KEY `metric_supervisor_performances_performance_id_fkey`;

-- DropForeignKey
ALTER TABLE `metric_supervisor_performances` DROP FOREIGN KEY `metric_supervisor_performances_supervisor_id_fkey`;

-- AlterTable
ALTER TABLE `metric_helps` DROP COLUMN `goal_name`;

-- AlterTable
ALTER TABLE `metric_supervisors`
    DROP COLUMN `goal_name`,
    ADD COLUMN `name` VARCHAR(50) NOT NULL AFTER `metric_id`,
    ADD COLUMN `surname` VARCHAR(50) NOT NULL AFTER `name`,
    ADD COLUMN `department` VARCHAR(191) NOT NULL AFTER `email`;

-- DropTable
DROP TABLE `metric_help_performances`;

-- DropTable
DROP TABLE `metric_supervisor_performances`;

-- CreateTable
CREATE TABLE `metric_activities` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `metric_id` INTEGER UNSIGNED NOT NULL,
    `activity_type` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `metric_activities` ADD CONSTRAINT `metric_activities_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `metric_activities` ADD CONSTRAINT `metric_activities_metric_id_fkey` FOREIGN KEY (`metric_id`) REFERENCES `metrics`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
