/*
  Warnings:

  - You are about to drop the `situation_questions_helps` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `situation_questions_helps` DROP FOREIGN KEY `situation_questions_helps_created_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `situation_questions_helps` DROP FOREIGN KEY `situation_questions_helps_helped_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `situation_questions_helps` DROP FOREIGN KEY `situation_questions_helps_question_id_fkey`;

-- DropForeignKey
ALTER TABLE `situation_questions_helps` DROP FOREIGN KEY `situation_questions_helps_report_id_fkey`;

-- DropTable
DROP TABLE `situation_questions_helps`;

-- CreateTable
CREATE TABLE `situation_helps` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `helped_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `question_id` INTEGER UNSIGNED NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `is_yes` BOOLEAN NULL,
    `description` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `situation_helps` ADD CONSTRAINT `situation_helps_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `situation_helps` ADD CONSTRAINT `situation_helps_helped_user_id_fkey` FOREIGN KEY (`helped_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `situation_helps` ADD CONSTRAINT `situation_helps_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `situation_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `situation_helps` ADD CONSTRAINT `situation_helps_question_id_fkey` FOREIGN KEY (`question_id`) REFERENCES `situation_questions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
