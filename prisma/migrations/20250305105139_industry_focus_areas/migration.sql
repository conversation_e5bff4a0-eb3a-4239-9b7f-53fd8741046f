/*
  Warnings:

  - You are about to drop the column `focus_area` on the `industry_default_details` table. All the data in the column will be lost.
  - You are about to drop the column `focus_area` on the `industry_details` table. All the data in the column will be lost.
  - You are about to drop the column `industry_id` on the `industry_help_answers` table. All the data in the column will be lost.
  - Added the required column `focus_areas` to the `industry_default_details` table without a default value. This is not possible if the table is not empty.
  - Added the required column `focus_areas` to the `industry_details` table without a default value. This is not possible if the table is not empty.
  - Added the required column `detail_id` to the `industry_help_answers` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `industry_help_answers` DROP FOREIGN KEY `industry_help_answers_industry_id_fkey`;

-- DropIndex
DROP INDEX `industry_help_answers_industry_id_fkey` ON `industry_help_answers`;

-- AlterTable
ALTER TABLE `industry_default_details`
    CHANGE `focus_area` `focus_areas` JSON NOT NULL AFTER `priority_issue`;

-- AlterTable
ALTER TABLE `industry_details`
    CHANGE `focus_area` `focus_areas` JSON NOT NULL AFTER `priority_issue`;

-- AlterTable
ALTER TABLE `industry_help_answers`
    DROP COLUMN `industry_id`,
    ADD COLUMN `detail_id` INTEGER UNSIGNED NOT NULL AFTER `help_id`;

-- AddForeignKey
ALTER TABLE `industry_help_answers` ADD CONSTRAINT `industry_help_answers_detail_id_fkey` FOREIGN KEY (`detail_id`) REFERENCES `industry_details`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
