/*
  Warnings:

  - You are about to drop the `charts` table. All the data in the table will be lost.
  - You are about to create a new `chart_templates` table.

*/
-- DropIndex
DROP INDEX `charts_model_type_model_id_idx` ON `charts`;

-- DropTable
DROP TABLE `charts`;

-- CreateTable
CREATE TABLE `chart_templates` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `model_type` TINYINT UNSIGNED NOT NULL,
    `title` VARCHAR(191) NOT NULL,
    `x_axis` TINYINT UNSIGNED NOT NULL,
    `y_axis` TINYINT UNSIGNED NOT NULL,
    `x_axis_label` VARCHAR(191) NOT NULL,
    `y_axis_label` VARCHAR(191) NOT NULL,
    `graph_type` TINYINT UNSIGNED NOT NULL,
    `filter_key` TINYINT UNSIGNED NULL,
    `filter_type` TINYINT UNSIGNED NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    INDEX `chart_templates_model_type_idx`(`model_type`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `charts` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `template_id` INTEGER UNSIGNED NOT NULL,
    `model_type` TINYINT UNSIGNED NOT NULL,
    `default_id` INTEGER UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    INDEX `charts_model_type_default_id_idx`(`model_type`, `default_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `charts` ADD CONSTRAINT `charts_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `chart_templates`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `charts` ADD CONSTRAINT `charts_default_id_fkey` FOREIGN KEY (`default_id`) REFERENCES `metric_defaults`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
