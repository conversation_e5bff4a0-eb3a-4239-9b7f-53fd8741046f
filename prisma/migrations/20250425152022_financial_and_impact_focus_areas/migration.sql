/*
  Warnings:

  - You are about to alter the column `min_value` on the `financial_priorities` table. The data in that column could be lost. The data in that column will be cast from `UnsignedTinyInt` to `Double`.
  - You are about to alter the column `max_value` on the `financial_priorities` table. The data in that column could be lost. The data in that column will be cast from `UnsignedTinyInt` to `Double`.
  - You are about to drop the column `focus_area` on the `financials` table. All the data in the column will be lost.
  - You are about to alter the column `min_value` on the `impact_priorities` table. The data in that column could be lost. The data in that column will be cast from `UnsignedTinyInt` to `Double`.
  - You are about to alter the column `max_value` on the `impact_priorities` table. The data in that column could be lost. The data in that column will be cast from `UnsignedTinyInt` to `Double`.
  - You are about to drop the column `focus_area` on the `impacts` table. All the data in the column will be lost.
  - Added the required column `focus_areas` to the `financials` table without a default value. This is not possible if the table is not empty.
  - Added the required column `focus_areas` to the `impacts` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `financial_details`
    MODIFY `category_description` VARCHAR(191) NULL,
    MODIFY `category_type` TINYINT UNSIGNED NULL,
    MODIFY `period_type` TINYINT UNSIGNED NULL;

-- AlterTable
ALTER TABLE `financial_priorities`
    MODIFY `min_value` DOUBLE NOT NULL,
    MODIFY `max_value` DOUBLE NOT NULL;

-- AlterTable
ALTER TABLE `financials`
    CHANGE `focus_area` `focus_areas` JSON NOT NULL;

-- AlterTable
ALTER TABLE `impact_details`
    MODIFY `category_description` VARCHAR(191) NULL,
    MODIFY `category_type` TINYINT UNSIGNED NULL,
    MODIFY `reality_type` TINYINT UNSIGNED NULL;

-- AlterTable
ALTER TABLE `impact_priorities`
    MODIFY `min_value` DOUBLE NOT NULL,
    MODIFY `max_value` DOUBLE NOT NULL;

-- AlterTable
ALTER TABLE `impacts`
    CHANGE `focus_area` `focus_areas` JSON NOT NULL;
