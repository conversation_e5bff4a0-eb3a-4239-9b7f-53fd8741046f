/*
  Warnings:

  - You are about to drop the column `category_type` on the `metric_helps` table. All the data in the column will be lost.
  - You are about to drop the column `metric_name` on the `metric_helps` table. All the data in the column will be lost.
  - You are about to drop the column `unit_type` on the `metric_helps` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[token]` on the table `metric_helps` will be added. If there are existing duplicate values, this will fail.
  - Made the column `financial_ids` on table `strategy_details` required. This step will fail if there are existing NULL values in that column.
  - Made the column `impact_ids` on table `strategy_details` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `metric_helps` DROP FOREIGN KEY `metric_helps_helped_user_id_fkey`;

-- DropIndex
DROP INDEX `metric_helps_helped_user_id_fkey` ON `metric_helps`;

-- AlterTable
ALTER TABLE `metric_helps`
    DROP COLUMN `category_type`,
    DROP COLUMN `metric_name`,
    DROP COLUMN `unit_type`,
    ADD COLUMN `helped_email` VARCHAR(100) NULL AFTER `helped_user_id`,
    ADD COLUMN `token` VARCHAR(191) NULL AFTER `goal_name`,
    ADD COLUMN `token_expired_at` DATETIME(3) NULL AFTER `token`,
    MODIFY `helped_user_id` INTEGER UNSIGNED NULL;

-- AlterTable
ALTER TABLE `strategy_details`
    MODIFY `financial_ids` JSON NOT NULL,
    MODIFY `impact_ids` JSON NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `metric_helps_token_key` ON `metric_helps`(`token`);

-- AddForeignKey
ALTER TABLE `metric_helps` ADD CONSTRAINT `metric_helps_helped_user_id_fkey` FOREIGN KEY (`helped_user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
