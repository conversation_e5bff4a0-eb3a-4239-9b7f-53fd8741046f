/*
  Warnings:

  - You are about to drop the column `financial_question_answer` on the `opinions` table. All the data in the column will be lost.
  - You are about to drop the column `impact_question_answer` on the `opinions` table. All the data in the column will be lost.
  - Made the column `financial_question_answer` on table `opinion_help_answers` required. This step will fail if there are existing NULL values in that column.
  - Made the column `impact_question_answer` on table `opinion_help_answers` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `opinion_help_answers` MODIFY `financial_question_answer` TINYINT UNSIGNED NOT NULL,
    MODIFY `impact_question_answer` TINYINT UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `opinions` DROP COLUMN `financial_question_answer`,
    DROP COLUMN `impact_question_answer`;

-- CreateTable
CREATE TABLE `financials` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `opinion_id` INTEGER UNSIGNED NULL,
    `focus_area` VARCHAR(191) NOT NULL,
    `priority_issue` VARCHAR(191) NOT NULL,
    `sub_priority_issue` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `financial_details` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `financial_id` INTEGER UNSIGNED NOT NULL,
    `category_description` VARCHAR(191) NOT NULL,
    `category_type` TINYINT UNSIGNED NOT NULL,
    `period_type` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `financial_reports` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `opinion_report_id` INTEGER UNSIGNED NULL,
    `name` VARCHAR(191) NOT NULL,
    `is_completed` BOOLEAN NOT NULL DEFAULT false,
    `expired_at` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `financial_helps` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `helped_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `financial_help_answers` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `help_id` INTEGER UNSIGNED NOT NULL,
    `detail_id` INTEGER UNSIGNED NOT NULL,
    `severity` TINYINT UNSIGNED NOT NULL,
    `possibility` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `financials` ADD CONSTRAINT `financials_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financials` ADD CONSTRAINT `financials_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `financial_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financials` ADD CONSTRAINT `financials_opinion_id_fkey` FOREIGN KEY (`opinion_id`) REFERENCES `opinions`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_details` ADD CONSTRAINT `financial_details_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_details` ADD CONSTRAINT `financial_details_financial_id_fkey` FOREIGN KEY (`financial_id`) REFERENCES `financials`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_reports` ADD CONSTRAINT `financial_reports_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_reports` ADD CONSTRAINT `financial_reports_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_reports` ADD CONSTRAINT `financial_reports_opinion_report_id_fkey` FOREIGN KEY (`opinion_report_id`) REFERENCES `opinion_reports`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_helps` ADD CONSTRAINT `financial_helps_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_helps` ADD CONSTRAINT `financial_helps_helped_user_id_fkey` FOREIGN KEY (`helped_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_helps` ADD CONSTRAINT `financial_helps_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `financial_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_help_answers` ADD CONSTRAINT `financial_help_answers_help_id_fkey` FOREIGN KEY (`help_id`) REFERENCES `financial_helps`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `financial_help_answers` ADD CONSTRAINT `financial_help_answers_detail_id_fkey` FOREIGN KEY (`detail_id`) REFERENCES `financial_details`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
