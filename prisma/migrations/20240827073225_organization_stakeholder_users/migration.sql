-- CreateTable
CREATE TABLE `organization_stakeholder_users` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `stakeholder_id` INTEGER UNSIGNED NOT NULL,
    `name` <PERSON><PERSON><PERSON><PERSON>(191) NOT NULL,
    `surname` <PERSON><PERSON><PERSON><PERSON>(191) NOT NULL,
    `email` VARCHAR(100) NOT NULL,
    `phone` VARCHAR(50) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `organization_stakeholder_users` ADD CONSTRAINT `organization_stakeholder_users_stakeholder_id_fkey` FOREIGN KEY (`stakeholder_id`) REFERENCES `organization_stakeholders`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
