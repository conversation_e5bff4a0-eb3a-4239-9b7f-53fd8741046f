-- AlterTable
ALTER TABLE `financial_reports` ADD COLUMN `chain_report_id` INTEGER UNSIGNED NULL AFTER `organization_id`;

-- AlterTable
ALTER TABLE `impact_reports` ADD COLUMN `chain_report_id` INTEGER UNSIGNED NULL AFTER `organization_id`;

-- AddForeignKey
ALTER TABLE `financial_reports` ADD CONSTRAINT `financial_reports_chain_report_id_fkey` FOREIGN KEY (`chain_report_id`) REFERENCES `chain_reports`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE `impact_reports` ADD CONSTRAINT `impact_reports_chain_report_id_fkey` FOREIGN KEY (`chain_report_id`) REFERENCES `chain_reports`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
