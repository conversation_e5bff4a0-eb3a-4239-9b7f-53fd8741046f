/*
  Warnings:

  - You are about to drop the column `age` on the `governance_member_profiles` table. All the data in the column will be lost.
  - You are about to drop the column `email` on the `governance_members` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `governance_members` table. All the data in the column will be lost.
  - You are about to drop the column `surname` on the `governance_members` table. All the data in the column will be lost.
  - You are about to drop the column `token` on the `governance_members` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[token]` on the table `governance_member_profiles` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `email` to the `governance_member_profiles` table without a default value. This is not possible if the table is not empty.
  - Added the required column `name` to the `governance_member_profiles` table without a default value. This is not possible if the table is not empty.
  - Added the required column `surname` to the `governance_member_profiles` table without a default value. This is not possible if the table is not empty.
  - Added the required column `token` to the `governance_member_profiles` table without a default value. This is not possible if the table is not empty.
  - Made the column `profile_id` on table `governance_members` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `governance_member_profiles` DROP FOREIGN KEY `governance_member_profiles_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `governance_members` DROP FOREIGN KEY `governance_members_profile_id_fkey`;

-- DropIndex
DROP INDEX `governance_member_profiles_user_id_fkey` ON `governance_member_profiles`;

-- DropIndex
DROP INDEX `governance_members_profile_id_fkey` ON `governance_members`;

-- DropIndex
DROP INDEX `governance_members_token_key` ON `governance_members`;

-- AlterTable
ALTER TABLE `governance_member_profiles`
    DROP COLUMN `age`,
    ADD COLUMN `name` VARCHAR(50) NOT NULL AFTER `organization_id`,
    ADD COLUMN `surname` VARCHAR(50) NOT NULL AFTER `name`,
    ADD COLUMN `email` VARCHAR(100) NOT NULL AFTER `surname`,
    ADD COLUMN `birth_date` DATE NULL AFTER `experience_text`,
    ADD COLUMN `token` VARCHAR(191) NOT NULL AFTER `birth_date`,
    MODIFY `user_id` INTEGER UNSIGNED NULL;

-- AlterTable
ALTER TABLE `governance_members`
    DROP COLUMN `email`,
    DROP COLUMN `name`,
    DROP COLUMN `surname`,
    DROP COLUMN `token`,
    MODIFY `profile_id` INTEGER UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `permissions`
    ADD COLUMN `is_visible` BOOLEAN NOT NULL DEFAULT true AFTER `name`;

-- CreateIndex
CREATE UNIQUE INDEX `governance_member_profiles_token_key` ON `governance_member_profiles`(`token`);

-- AddForeignKey
ALTER TABLE `governance_members` ADD CONSTRAINT `governance_members_profile_id_fkey` FOREIGN KEY (`profile_id`) REFERENCES `governance_member_profiles`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_member_profiles` ADD CONSTRAINT `governance_member_profiles_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
