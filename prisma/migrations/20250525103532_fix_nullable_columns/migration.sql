/*
  Warnings:

  - Made the column `type` on table `chain_defaults` required. This step will fail if there are existing NULL values in that column.
  - Made the column `type` on table `chains` required. This step will fail if there are existing NULL values in that column.
  - Made the column `chain_report_id` on table `financial_reports` required. This step will fail if there are existing NULL values in that column.
  - Made the column `chain_report_id` on table `impact_reports` required. This step will fail if there are existing NULL values in that column.
  - Made the column `source_ids` on table `sectors` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE `financial_reports` DROP FOREIGN KEY `financial_reports_chain_report_id_fkey`;

-- DropForeignKey
ALTER TABLE `impact_reports` DROP FOREIGN KEY `impact_reports_chain_report_id_fkey`;

-- DropIndex
DROP INDEX `financial_reports_chain_report_id_fkey` ON `financial_reports`;

-- DropIndex
DROP INDEX `impact_reports_chain_report_id_fkey` ON `impact_reports`;

-- AlterTable
ALTER TABLE `chain_defaults` MODIFY `type` TINYINT UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `chains` MODIFY `type` TINYINT UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `financial_reports` MODIFY `chain_report_id` INTEGER UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `governance_member_profiles` MODIFY `token` VARCHAR(191) NULL,
    MODIFY `token_expired_at` DATETIME(3) NULL;

-- AlterTable
ALTER TABLE `impact_reports` MODIFY `chain_report_id` INTEGER UNSIGNED NOT NULL;

-- AlterTable
ALTER TABLE `sectors` MODIFY `source_ids` JSON NOT NULL;

-- AddForeignKey
ALTER TABLE `financial_reports` ADD CONSTRAINT `financial_reports_chain_report_id_fkey` FOREIGN KEY (`chain_report_id`) REFERENCES `chain_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `impact_reports` ADD CONSTRAINT `impact_reports_chain_report_id_fkey` FOREIGN KEY (`chain_report_id`) REFERENCES `chain_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
