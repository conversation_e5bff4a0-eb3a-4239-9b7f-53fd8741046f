/*
  Warnings:

  - Added the required column `category_id` to the `situation_questions` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `situation_questions` DROP FOREIGN KEY `situation_questions_type_id_fkey`;

-- AlterTable
ALTER TABLE `situation_questions`
    ADD COLUMN `category_id` INTEGER UNSIGNED NOT NULL AFTER `id`,
    ADD COLUMN `info` VARCHAR(500) NULL AFTER `suggestion`,
    MODIFY `type_id` INTEGER UNSIGNED NULL AFTER `category_id`;

-- CreateTable
CREATE TABLE `situation_question_categories`
(
    `id`         INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `name`       VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `situation_questions`
    ADD CONSTRAINT `situation_questions_category_id_fkey` FOREIGN KEY (`category_id`) REFERENCES `situation_question_categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `situation_questions`
    ADD CONSTRAINT `situation_questions_type_id_fkey` FOREIGN KEY (`type_id`) REFERENCES `situation_question_types` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
