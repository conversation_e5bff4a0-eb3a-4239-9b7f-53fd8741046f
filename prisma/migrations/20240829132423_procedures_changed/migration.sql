/*
  Warnings:

  - You are about to drop the `organization_procedures` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `procedure_suggestions` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE `organization_procedures` DROP FOREIGN KEY `organization_procedures_category_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_procedures` DROP FOREIGN KEY `organization_procedures_created_user_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_procedures` DROP FOREIGN KEY `organization_procedures_organization_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_procedures` DROP FOREIGN KEY `organization_procedures_procedure_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_procedures` DROP FOREIGN KEY `organization_procedures_suggestion_id_fkey`;

-- DropForeignKey
ALTER TABLE `procedure_suggestions` DROP FOREIGN KEY `procedure_suggestions_category_id_fkey`;

-- DropIndex
DROP INDEX `organization_settings_key_key` ON `organization_settings`;

-- DropTable
DROP TABLE `organization_procedures`;

-- DropTable
DROP TABLE `procedure_suggestions`;

-- CreateTable
CREATE TABLE `organization_procedure_categories` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `procedure_id` INTEGER UNSIGNED NOT NULL,
    `category_id` INTEGER UNSIGNED NULL,
    `name` VARCHAR(191) NOT NULL,
    `order` TINYINT UNSIGNED NOT NULL,
    `is_deleted` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `organization_procedure_policies` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `organization_procedure_category_id` INTEGER UNSIGNED NOT NULL,
    `policy_id` INTEGER UNSIGNED NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(500) NULL,
    `order` TINYINT UNSIGNED NOT NULL,
    `year` SMALLINT UNSIGNED NOT NULL,
    `urls` JSON NOT NULL,
    `is_deleted` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `procedure_policies` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `category_id` INTEGER UNSIGNED NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `order` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `organization_procedure_categories` ADD CONSTRAINT `organization_procedure_categories_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `organization_procedure_categories` ADD CONSTRAINT `organization_procedure_categories_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `organization_procedure_categories` ADD CONSTRAINT `organization_procedure_categories_procedure_id_fkey` FOREIGN KEY (`procedure_id`) REFERENCES `procedures`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `organization_procedure_categories` ADD CONSTRAINT `organization_procedure_categories_category_id_fkey` FOREIGN KEY (`category_id`) REFERENCES `procedure_categories`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `organization_procedure_policies` ADD CONSTRAINT `organization_procedure_policies_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `organization_procedure_policies` ADD CONSTRAINT `organization_procedure_policies_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `organization_procedure_policies` ADD CONSTRAINT `organization_procedure_policies_organization_procedure_cate_fkey` FOREIGN KEY (`organization_procedure_category_id`) REFERENCES `organization_procedure_categories`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `organization_procedure_policies` ADD CONSTRAINT `organization_procedure_policies_policy_id_fkey` FOREIGN KEY (`policy_id`) REFERENCES `procedure_policies`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `procedure_policies` ADD CONSTRAINT `procedure_policies_category_id_fkey` FOREIGN KEY (`category_id`) REFERENCES `procedure_categories`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
