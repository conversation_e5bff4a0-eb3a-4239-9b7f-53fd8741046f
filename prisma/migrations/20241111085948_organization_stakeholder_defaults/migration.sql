-- AlterTable
ALTER TABLE `organization_stakeholders`
    ADD COLUMN `default_id` INTEGER UNSIGNED NULL AFTER `organization_id`;

-- CreateTable
CREATE TABLE `organization_stakeholder_defaults`
(
    `id`         INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `name`       VARCHAR(191) NOT NULL,
    `type`       TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `organization_stakeholders`
    ADD CONSTRAINT `organization_stakeholders_default_id_fkey` FOREIGN KEY (`default_id`) REFERENCES `organization_stakeholder_defaults` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
