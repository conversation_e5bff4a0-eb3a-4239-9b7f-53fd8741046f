/*
  Warnings:

  - You are about to drop the column `input` on the `chain_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `output` on the `chain_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `value` on the `chain_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `input` on the `chain_helps` table. All the data in the column will be lost.
  - You are about to drop the column `output` on the `chain_helps` table. All the data in the column will be lost.
  - You are about to drop the column `value` on the `chain_helps` table. All the data in the column will be lost.
  - You are about to drop the column `input` on the `chains` table. All the data in the column will be lost.
  - You are about to drop the column `output` on the `chains` table. All the data in the column will be lost.
  - You are about to drop the column `value` on the `chains` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `chain_defaults`
    DROP COLUMN `input`,
    DROP COLUMN `output`,
    CHANGE `value` `description` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `chain_helps`
    DROP COLUMN `input`,
    DROP COLUMN `output`,
    CHANGE `value` `description` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `chains`
    DROP COLUMN `input`,
    DROP COLUMN `output`,
    CHANGE `value` `description` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `financials`
    ADD COLUMN `source` VARCHAR(191) NULL AFTER `description`;

-- AlterTable
ALTER TABLE `organization_procedure_policies`
    ADD COLUMN `responsible_full_name` VARCHAR(191) NULL AFTER `description`,
    ADD COLUMN `responsible_title` VARCHAR(191) NULL AFTER `responsible_full_name`;
