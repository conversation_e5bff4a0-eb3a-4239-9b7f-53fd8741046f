-- CreateTable
CREATE TABLE `governances` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `parent_id` INTEGER UNSIGNED NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` VARCHAR(3000) NULL,
    `meeting_frequency_type` TINYINT UNSIGNED NULL,
    `priority_issues` JSON NULL,
    `order` TINYINT UNSIGNED NOT NULL,
    `is_default` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `governance_reports` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `strategy_report_id` INTEGER UNSIGNED NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `code` SMALLINT UNSIGNED NOT NULL,
    `is_completed` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `governance_members` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `governance_id` INTEGER UNSIGNED NOT NULL,
    `profile_id` INTEGER UNSIGNED NULL,
    `name` VARCHAR(50) NOT NULL,
    `surname` VARCHAR(50) NOT NULL,
    `email` VARCHAR(100) NOT NULL,
    `department` VARCHAR(191) NOT NULL,
    `is_lead` BOOLEAN NOT NULL DEFAULT false,
    `token` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    UNIQUE INDEX `governance_members_email_key`(`email`),
    UNIQUE INDEX `governance_members_token_key`(`token`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `governance_member_profiles` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `age` TINYINT UNSIGNED NULL,
    `gender_type` TINYINT UNSIGNED NULL,
    `education_type` TINYINT UNSIGNED NULL,
    `experience_year` TINYINT UNSIGNED NULL,
    `experience_type` TINYINT UNSIGNED NULL,
    `experience_text` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `governance_meetings` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `governance_id` INTEGER UNSIGNED NOT NULL,
    `member_ids` JSON NULL,
    `file_ids` JSON NULL,
    `meeting_date` DATETIME(3) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `governances` ADD CONSTRAINT `governances_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governances` ADD CONSTRAINT `governances_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `governance_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governances` ADD CONSTRAINT `governances_parent_id_fkey` FOREIGN KEY (`parent_id`) REFERENCES `governances`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_reports` ADD CONSTRAINT `governance_reports_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_reports` ADD CONSTRAINT `governance_reports_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_reports` ADD CONSTRAINT `governance_reports_strategy_report_id_fkey` FOREIGN KEY (`strategy_report_id`) REFERENCES `strategy_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_members` ADD CONSTRAINT `governance_members_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_members` ADD CONSTRAINT `governance_members_governance_id_fkey` FOREIGN KEY (`governance_id`) REFERENCES `governances`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_members` ADD CONSTRAINT `governance_members_profile_id_fkey` FOREIGN KEY (`profile_id`) REFERENCES `governance_member_profiles`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_member_profiles` ADD CONSTRAINT `governance_member_profiles_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_member_profiles` ADD CONSTRAINT `governance_member_profiles_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_meetings` ADD CONSTRAINT `governance_meetings_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `governance_meetings` ADD CONSTRAINT `governance_meetings_governance_id_fkey` FOREIGN KEY (`governance_id`) REFERENCES `governances`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
