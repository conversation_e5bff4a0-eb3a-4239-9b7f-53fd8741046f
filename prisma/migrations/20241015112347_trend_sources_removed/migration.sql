/*
  Warnings:

  - You are about to drop the column `source_id` on the `trend_defaults` table. All the data in the column will be lost.
  - You are about to drop the column `source_id` on the `trends` table. All the data in the column will be lost.
  - You are about to drop the `trend_sources` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `source` to the `trend_defaults` table without a default value. This is not possible if the table is not empty.
  - Added the required column `source` to the `trends` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `trend_defaults` DROP FOREIGN KEY `trend_defaults_source_id_fkey`;

-- DropForeignKey
ALTER TABLE `trends` DROP FOREIGN KEY `trends_source_id_fkey`;

-- AlterTable
ALTER TABLE `trend_defaults` DROP COLUMN `source_id`,
    ADD COLUMN `source` VARCHAR(191) NOT NULL AFTER `name`;

-- AlterTable
ALTER TABLE `trends` DROP COLUMN `source_id`,
    ADD COLUMN `source` VARCHAR(191) NOT NULL AFTER `name`;

-- DropTable
DROP TABLE `trend_sources`;
