-- AlterTable
ALTER TABLE `organizations`
    ADD COLUMN `sector_id` INTEGER UNSIGNED NULL AFTER `image`;

-- CreateTable
CREATE TABLE `organization_sectors`
(
    `id`         INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `name`       VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `organizations`
    ADD CONSTRAINT `organizations_sector_id_fkey` FOREIGN KEY (`sector_id`) REFERENCES `organization_sectors` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;
