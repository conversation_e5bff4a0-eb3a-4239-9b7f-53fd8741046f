/*
  Warnings:

  - You are about to drop the `organization_currencies` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `currency_id` to the `organization_countries` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE `organization_currencies` DROP FOREIGN KEY `organization_currencies_currency_id_fkey`;

-- DropForeignKey
ALTER TABLE `organization_currencies` DROP FOREIGN KEY `organization_currencies_organization_id_fkey`;

-- AlterTable
ALTER TABLE `organization_countries`
    ADD COLUMN `currency_id` INTEGER UNSIGNED NOT NULL AFTER `country_id`;

-- DropTable
DROP TABLE `organization_currencies`;

-- AddForeignKey
ALTER TABLE `organization_countries`
    ADD CONSTRAINT `organization_countries_currency_id_fkey` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
