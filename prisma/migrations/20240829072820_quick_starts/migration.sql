-- AlterTable
ALTER TABLE `users`
    ADD COLUMN `show_quick_start` BO<PERSON>EAN NOT NULL DEFAULT true AFTER `is_active`;

-- CreateTable
CREATE TABLE `user_quick_starts`
(
    `id`         INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id`    INTEGER UNSIGNED NOT NULL,
    `quick_type` TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `quick_starts`
(
    `id`         INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `front_slug` VARCHAR(191) NOT NULL,
    `quick_type` TINYINT UNSIGNED NOT NULL,
    `order`      TINYINT UNSIGNED NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP (3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `user_quick_starts`
    ADD CONSTRAINT `user_quick_starts_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
