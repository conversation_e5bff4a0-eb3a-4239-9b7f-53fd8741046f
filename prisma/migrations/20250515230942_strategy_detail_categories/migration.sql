/*
  Warnings:

  - You are about to drop the column `financial_category_types` on the `strategy_details` table. All the data in the column will be lost.
  - You are about to drop the column `impact_category_types` on the `strategy_details` table. All the data in the column will be lost.
  - Added the required column `minor_priority_issues` to the `strategy_details` table without a default value. This is not possible if the table is not empty.
  - Added the required column `sub_priority_issues` to the `strategy_details` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `strategy_details`
    DROP COLUMN `financial_category_types`,
    DROP COLUMN `impact_category_types`,
    ADD COLUMN `sub_priority_issues` JSON NOT NULL AFTER `priority_issue`,
    ADD COLUMN `minor_priority_issues` JSON NOT NULL AFTER `sub_priority_issues`;

-- CreateTable
CREATE TABLE `strategy_detail_categories` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `detail_id` INTEGER UNSIGNED NOT NULL,
    `financial_category_description` VARCHAR(191) NULL,
    `financial_category_type` TINYINT UNSIGNED NULL,
    `financial_period_type` TINYINT UNSIGNED NULL,
    `impact_category_description` VARCHAR(191) NULL,
    `impact_category_type` TINYINT UNSIGNED NULL,
    `impact_reality_type` TINYINT UNSIGNED NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `strategy_detail_categories` ADD CONSTRAINT `strategy_detail_categories_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `strategy_detail_categories` ADD CONSTRAINT `strategy_detail_categories_detail_id_fkey` FOREIGN KEY (`detail_id`) REFERENCES `strategy_details`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
