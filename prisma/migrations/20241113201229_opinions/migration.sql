-- CreateTable
CREATE TABLE `opinions` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `focus_area` VARCHAR(191) NOT NULL,
    `priority_issue` VARCHAR(191) NOT NULL,
    `sub_priority_issue` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `financial_question` VARCHAR(191) NOT NULL,
    `impact_question` VARCHAR(191) NOT NULL,
    `financial_question_answer` TINYINT UNSIGNED NULL,
    `impact_question_answer` TINYINT UNSIGNED NULL,
    `is_default` BOOLEAN NOT NULL DEFAULT false,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `opinion_reports` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `organization_id` INTEGER UNSIGNED NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `financial_threshold` TINYINT UNSIGNED NULL,
    `impact_threshold` TINYINT UNSIGNED NULL,
    `is_completed` BOOLEAN NOT NULL DEFAULT false,
    `expired_at` DATETIME(3) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `opinion_defaults` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `focus_area` VARCHAR(191) NOT NULL,
    `priority_issue` VARCHAR(191) NOT NULL,
    `sub_priority_issue` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `financial_question` VARCHAR(191) NOT NULL,
    `impact_question` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `opinion_helps` (
    `id` INTEGER UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_user_id` INTEGER UNSIGNED NOT NULL,
    `stakeholder_user_id` INTEGER UNSIGNED NOT NULL,
    `report_id` INTEGER UNSIGNED NOT NULL,
    `status` TINYINT NOT NULL DEFAULT 0,
    `financial_question_answer` TINYINT UNSIGNED NULL,
    `impact_question_answer` TINYINT UNSIGNED NULL,
    `token` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `deleted_at` DATETIME(3) NULL,

    UNIQUE INDEX `opinion_helps_token_key`(`token`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `opinions` ADD CONSTRAINT `opinions_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `opinions` ADD CONSTRAINT `opinions_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `opinion_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `opinion_reports` ADD CONSTRAINT `opinion_reports_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `opinion_reports` ADD CONSTRAINT `opinion_reports_organization_id_fkey` FOREIGN KEY (`organization_id`) REFERENCES `organizations`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `opinion_helps` ADD CONSTRAINT `opinion_helps_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `opinion_helps` ADD CONSTRAINT `opinion_helps_stakeholder_user_id_fkey` FOREIGN KEY (`stakeholder_user_id`) REFERENCES `organization_stakeholder_users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `opinion_helps` ADD CONSTRAINT `opinion_helps_report_id_fkey` FOREIGN KEY (`report_id`) REFERENCES `opinion_reports`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
