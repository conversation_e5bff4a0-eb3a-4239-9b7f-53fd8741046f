/*
  Warnings:

  - Added the required column `created_user_id` to the `trends` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE `trends`
    ADD COLUMN `created_user_id` INTEGER UNSIGNED NOT NULL AFTER `id`;

ALTER TABLE `trends`
    MODIFY `report_id` INTEGER UNSIGNED NOT NULL AFTER `created_user_id`;

-- AddForeignKey
ALTER TABLE `trends`
    ADD CONSTRAINT `trends_created_user_id_fkey` FOREIGN KEY (`created_user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
