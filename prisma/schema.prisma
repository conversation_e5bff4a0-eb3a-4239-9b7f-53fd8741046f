generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  name           String    @map("name") @db.<PERSON><PERSON><PERSON><PERSON>(50)
  surname        String    @map("surname") @db.Var<PERSON><PERSON>(50)
  email          String    @unique @map("email") @db.VarChar(100)
  password       String    @map("password")
  phone          String?   @map("phone") @db.VarChar(50)
  image          String?   @map("image")
  description    String?   @map("description") @db.Text
  timezone       Int?      @map("timezone")
  lastActivity   DateTime? @map("last_activity")
  isOtp          Boolean   @default(false) @map("is_otp")
  isActive       Boolean   @default(false) @map("is_active")
  isAdmin        <PERSON>   @default(false) @map("is_admin")
  showQuickStart Boolean   @default(true) @map("show_quick_start")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  agreements                       UserAgreement[]
  userOrganizations                UserOrganization[]
  sessions                         UserSession[]
  createdPendings                  UserPending[]
  userActivation                   UserActivation[]
  userForgots                      UserForgot[]
  userPermissions                  UserPermission[]
  quickStarts                      UserQuickStart[]
  createdProcedureCategories       OrganizationProcedureCategory[]
  createdProcedurePolicies         OrganizationProcedurePolicy[]
  createdStakeholders              Stakeholder[]
  createdStakeholderCommunications StakeholderCommunication[]
  createdStakeholderUsers          StakeholderUser[]
  createdFiles                     FileManager[]
  createdActivities                Activity[]
  notifications                    Notification[]
  createdSituationReports          SituationReport[]
  createdSituationHelps            SituationHelp[]                 @relation(name: "createdUser")
  helpedSituationHelps             SituationHelp[]                 @relation(name: "helpedUser")
  createdTrends                    Trend[]
  createdTrendReports              TrendReport[]
  createdTrendHelps                TrendHelp[]                     @relation(name: "createdUser")
  helpedTrendHelps                 TrendHelp[]                     @relation(name: "helpedUser")
  createdRisks                     Risk[]
  createdRiskReports               RiskReport[]
  createdRiskHelps                 RiskHelp[]                      @relation(name: "createdUser")
  helpedRiskHelps                  RiskHelp[]                      @relation(name: "helpedUser")
  createdChains                    Chain[]                         @relation(name: "createdUser")
  updatedChains                    Chain[]                         @relation(name: "updatedUser")
  createdChainReports              ChainReport[]
  createdChainHelps                ChainHelp[]                     @relation(name: "createdUser")
  helpedChainHelps                 ChainHelp[]                     @relation(name: "helpedUser")
  createdCapitals                  Capital[]                       @relation(name: "createdUser")
  updatedCapitals                  Capital[]                       @relation(name: "updatedUser")
  createdCapitalReports            CapitalReport[]
  createdCapitalHelps              CapitalHelp[]                   @relation(name: "createdUser")
  helpedCapitalHelps               CapitalHelp[]                   @relation(name: "helpedUser")
  createdOpinions                  Opinion[]
  createdOpinionReports            OpinionReport[]
  createdOpinionHelps              OpinionHelp[]
  createdIndustries                Industry[]
  createdIndustryDetails           IndustryDetail[]
  createdIndustryReports           IndustryReport[]
  createdIndustryHelps             IndustryHelp[]                  @relation(name: "createdUser")
  helpedIndustryHelps              IndustryHelp[]                  @relation(name: "helpedUser")
  createdTendencies                Tendency[]
  createdTendencyReports           TendencyReport[]
  createdTendencyHelps             TendencyHelp[]                  @relation(name: "createdUser")
  helpedTendencyHelps              TendencyHelp[]                  @relation(name: "helpedUser")
  createdFinancials                Financial[]
  createdFinancialDetails          FinancialDetail[]
  createdFinancialReports          FinancialReport[]
  createdFinancialHelps            FinancialHelp[]                 @relation(name: "createdUser")
  helpedFinancialHelps             FinancialHelp[]                 @relation(name: "helpedUser")
  createdFinancialPriorities       FinancialPriority[]
  createdFinancialReviews          FinancialReview[]               @relation(name: "createdUser")
  reviewerFinancialReviews         FinancialReview[]               @relation(name: "reviewerUser")
  createdFinancialSignificances    FinancialSignificance[]
  createdImpacts                   Impact[]
  createdImpactDetails             ImpactDetail[]
  createdImpactReports             ImpactReport[]
  createdImpactHelps               ImpactHelp[]                    @relation(name: "createdUser")
  helpedImpactHelps                ImpactHelp[]                    @relation(name: "helpedUser")
  createdImpactPriorities          ImpactPriority[]
  createdImpactReviews             ImpactReview[]                  @relation(name: "createdUser")
  reviewerImpactReviews            ImpactReview[]                  @relation(name: "reviewerUser")
  createdImpactSignificances       ImpactSignificance[]
  createdStrategies                Strategy[]                      @relation(name: "createdUser")
  updatedStrategies                Strategy[]                      @relation(name: "updatedUser")
  createdStrategyDetails           StrategyDetail[]
  createdStrategyDetailCategories  StrategyDetailCategory[]
  createdStrategyReports           StrategyReport[]
  createdStrategyHelps             StrategyHelp[]                  @relation(name: "createdUser")
  helpedStrategyHelps              StrategyHelp[]                  @relation(name: "helpedUser")
  createdStrategyTerms             StrategyTerm[]
  createdGovernances               Governance[]
  createdGovernanceReports         GovernanceReport[]
  createdGovernanceMembers         GovernanceMember[]
  governanceMemberProfiles         GovernanceMemberProfile[]
  createdGovernanceMeetings        GovernanceMeeting[]
  createdMetrics                   Metric[]                        @relation(name: "createdUser")
  createdMetricReports             MetricReport[]
  createdMetricPerformances        MetricPerformance[]
  createdMetricHelps               MetricHelp[]                    @relation(name: "createdUser")
  helpedMetricHelps                MetricHelp[]                    @relation(name: "helpedUser")
  createdMetricSupervisors         MetricSupervisor[]
  metricSupervisorProfiles         MetricSupervisorProfile[]
  createdMetricActivities          MetricActivity[]

  @@map("users")
}

model UserAgreement {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  userId        Int       @map("user_id") @db.UnsignedInt
  personalData  Boolean   @default(false) @map("personal_data")
  kvkk          Boolean   @default(false) @map("kvkk")
  userAgreement Boolean   @default(false) @map("user_agreement")
  privacyPolicy Boolean   @default(false) @map("privacy_policy")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  user User @relation(fields: [userId], references: [id])

  @@map("user_agreements")
}

model UserOrganization {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  userId         Int       @map("user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  roleId         Int       @map("role_id") @db.UnsignedInt
  title          String?   @map("title") @db.VarChar(100)
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  user         User         @relation(fields: [userId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id])
  role         Role         @relation(fields: [roleId], references: [id])

  @@map("user_organizations")
}

model UserPending {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  roleId         Int?      @map("role_id") @db.UnsignedInt
  type           Int       @map("type") @db.UnsignedTinyInt
  name           String?   @map("name") @db.VarChar(50)
  surname        String?   @map("surname") @db.VarChar(50)
  phone          String?   @map("phone") @db.VarChar(50)
  email          String    @map("email") @db.VarChar(100)
  permissionIds  Json      @default("[]") @map("permission_ids")
  token          String    @unique @map("token")
  code           String?   @map("code") @db.VarChar(6)
  tokenExpiredAt DateTime? @map("token_expired_at")
  codeExpiredAt  DateTime? @map("code_expired_at")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  createdUser  User         @relation(fields: [createdUserId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id])
  role         Role?        @relation(fields: [roleId], references: [id])

  @@map("user_pendings")
}

model UserActivation {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  userId    Int       @map("user_id") @db.UnsignedInt
  token     String    @unique @map("token")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  user User @relation(fields: [userId], references: [id])

  @@map("user_activations")
}

model UserForgot {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  userId    Int       @map("user_id") @db.UnsignedInt
  token     String    @unique @map("token")
  expiredAt DateTime  @map("expired_at")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  user User @relation(fields: [userId], references: [id])

  @@map("user_forgots")
}

model UserPermission {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  userId         Int       @map("user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  permissionId   Int       @map("permission_id") @db.UnsignedInt
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  user         User         @relation(fields: [userId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id])
  permission   Permission   @relation(fields: [permissionId], references: [id])

  @@map("user_permissions")
}

model UserSession {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  userId         Int       @map("user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  jwtId          String    @unique @map("jwt_id")
  payload        Json?     @map("payload")
  expiredAt      DateTime  @default(now()) @map("expired_at")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  user         User         @relation(fields: [userId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id])

  @@map("user_sessions")
}

model UserQuickStart {
  id        Int      @id @default(autoincrement()) @db.UnsignedInt
  userId    Int      @map("user_id") @db.UnsignedInt
  quickType Int      @map("quick_type") @db.UnsignedTinyInt
  createdAt DateTime @default(now()) @map("created_at")

  user User @relation(fields: [userId], references: [id])

  @@map("user_quick_starts")
}

model Organization {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  name             String    @map("name")
  address          String?   @map("address") @db.Text
  image            String?   @map("image")
  employeeNumber   Int?      @map("employee_number") @db.UnsignedInt
  materialType     Int       @map("material_type") @db.UnsignedTinyInt
  organizationType Int       @map("organization_type") @db.UnsignedTinyInt
  affiliateType    Int?      @map("affiliate_type") @db.UnsignedTinyInt
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  userOrganizations        UserOrganization[]
  pendingUsers             UserPending[]
  userPermissions          UserPermission[]
  userSessions             UserSession[]
  relationOrganizations    OrganizationRelation[]          @relation(name: "mainOrganization")
  mainOrganizations        OrganizationRelation[]          @relation(name: "relationOrganization")
  carbonSession            OrganizationCarbonSession?
  countries                OrganizationCountry[]
  settings                 OrganizationSetting[]
  procedureCategories      OrganizationProcedureCategory[]
  procedurePolicies        OrganizationProcedurePolicy[]
  sectors                  OrganizationSector[]
  stakeholders             Stakeholder[]
  files                    FileManager[]
  activities               Activity[]
  situationReports         SituationReport[]
  trendReports             TrendReport[]
  riskReports              RiskReport[]
  chainReports             ChainReport[]
  capitalReports           CapitalReport[]
  opinionReports           OpinionReport[]
  industryReports          IndustryReport[]
  tendencyReports          TendencyReport[]
  financialReports         FinancialReport[]
  impactReports            ImpactReport[]
  strategyReports          StrategyReport[]
  governanceReports        GovernanceReport[]
  governanceMemberProfiles GovernanceMemberProfile[]
  metricReports            MetricReport[]
  metricSupervisorProfiles MetricSupervisorProfile[]

  @@map("organizations")
}

model OrganizationCarbonSession {
  id                   Int      @id @default(autoincrement()) @db.UnsignedInt
  organizationId       Int      @unique @map("organization_id") @db.UnsignedInt
  carbonOrganizationId Int      @unique @map("carbon_organization_id") @db.UnsignedInt
  createdAt            DateTime @default(now()) @map("created_at")

  organization Organization @relation(fields: [organizationId], references: [id])

  @@map("organization_carbon_sessions")
}

model OrganizationRelation {
  id                     Int       @id @default(autoincrement()) @db.UnsignedInt
  mainOrganizationId     Int       @map("main_organization_id") @db.UnsignedInt
  relationOrganizationId Int       @map("relation_organization_id") @db.UnsignedInt
  relationType           Int       @map("relation_type") @db.UnsignedTinyInt
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")
  deletedAt              DateTime? @map("deleted_at")

  mainOrganization     Organization @relation(name: "mainOrganization", fields: [mainOrganizationId], references: [id])
  relationOrganization Organization @relation(name: "relationOrganization", fields: [relationOrganizationId], references: [id])

  @@map("organization_relations")
}

model OrganizationCountry {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  countryId      Int       @map("country_id") @db.UnsignedInt
  currencyId     Int       @map("currency_id") @db.UnsignedInt
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  organization Organization @relation(fields: [organizationId], references: [id])
  country      Country      @relation(fields: [countryId], references: [id])
  currency     Currency     @relation(fields: [currencyId], references: [id])

  @@map("organization_countries")
}

model OrganizationSetting {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  key            String    @map("key") @db.VarChar(50)
  value          String    @map("value")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  organization Organization @relation(fields: [organizationId], references: [id])

  @@map("organization_settings")
}

model OrganizationProcedureCategory {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  procedureId    Int       @map("procedure_id") @db.UnsignedInt
  categoryId     Int?      @map("category_id") @db.UnsignedInt
  name           String    @map("name")
  description    String?   @map("description") @db.Text
  year           Int?      @map("year") @db.UnsignedSmallInt
  order          Int       @map("order") @db.UnsignedTinyInt
  isDeleted      Boolean   @default(false) @map("is_deleted")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  createdUser                   User                          @relation(fields: [createdUserId], references: [id])
  organization                  Organization                  @relation(fields: [organizationId], references: [id])
  procedure                     Procedure                     @relation(fields: [procedureId], references: [id])
  category                      ProcedureCategory?            @relation(fields: [categoryId], references: [id])
  organizationProcedurePolicies OrganizationProcedurePolicy[]

  @@map("organization_procedure_categories")
}

model OrganizationProcedurePolicy {
  id                              Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId                   Int       @map("created_user_id") @db.UnsignedInt
  organizationId                  Int       @map("organization_id") @db.UnsignedInt
  organizationProcedureCategoryId Int       @map("organization_procedure_category_id") @db.UnsignedInt
  policyId                        Int?      @map("policy_id") @db.UnsignedInt
  name                            String    @map("name")
  description                     String?   @map("description") @db.Text
  responsibleFullName             String?   @map("responsible_full_name")
  responsibleTitle                String?   @map("responsible_title")
  year                            Int?      @map("year") @db.UnsignedSmallInt
  fileIds                         Json?     @map("file_ids")
  order                           Int       @map("order") @db.UnsignedTinyInt
  isDeleted                       Boolean   @default(false) @map("is_deleted")
  createdAt                       DateTime  @default(now()) @map("created_at")
  updatedAt                       DateTime  @updatedAt @map("updated_at")
  deletedAt                       DateTime? @map("deleted_at")

  createdUser                   User                          @relation(fields: [createdUserId], references: [id])
  organization                  Organization                  @relation(fields: [organizationId], references: [id])
  organizationProcedureCategory OrganizationProcedureCategory @relation(fields: [organizationProcedureCategoryId], references: [id])
  policy                        ProcedurePolicy?              @relation(fields: [policyId], references: [id])

  @@map("organization_procedure_policies")
}

model OrganizationSector {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  sectorId       Int       @map("sector_id") @db.UnsignedInt
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  organization Organization @relation(fields: [organizationId], references: [id])
  sector       Sector       @relation(fields: [sectorId], references: [id])

  @@map("organization_sectors")
}

model Sector {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  sourceIds Json      @map("source_ids")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  organizationSectors OrganizationSector[]
  metricDefaults      MetricDefault[]

  @@map("sectors")
}

model SectorSource {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("sector_sources")
}

model Role {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  key       String    @unique @map("key")
  name      String    @map("name")
  color     String    @map("color") @db.VarChar(20)
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  userOrganizations UserOrganization[]
  userPendings      UserPending[]

  @@map("roles")
}

model Permission {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  key       String    @unique @map("key")
  name      String    @map("name")
  isVisible Boolean   @default(true) @map("is_visible")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  userPermissions UserPermission[]

  @@map("permissions")
}

model Country {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  organizationCountries OrganizationCountry[]

  @@map("countries")
}

model Currency {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  order     Int       @map("order") @db.UnsignedTinyInt
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  organizationCountries OrganizationCountry[]

  @@map("currencies")
}

model Procedure {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  name           String    @map("name")
  slug           String    @unique @map("slug")
  categoryButton String    @map("category_button")
  policyButton   String    @map("policy_button")
  order          Int       @map("order") @db.UnsignedTinyInt
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  categories                      ProcedureCategory[]
  organizationProcedureCategories OrganizationProcedureCategory[]

  @@map("procedures")
}

model ProcedureCategory {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  procedureId Int       @map("procedure_id") @db.UnsignedInt
  name        String    @map("name")
  order       Int       @map("order") @db.UnsignedTinyInt
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  procedure                       Procedure                       @relation(fields: [procedureId], references: [id])
  organizationProcedureCategories OrganizationProcedureCategory[]
  policilies                      ProcedurePolicy[]

  @@map("procedure_categories")
}

model ProcedurePolicy {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  categoryId Int       @map("category_id") @db.UnsignedInt
  name       String    @map("name")
  order      Int       @map("order") @db.UnsignedTinyInt
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at")

  category                      ProcedureCategory             @relation(fields: [categoryId], references: [id])
  organizationProcedurePolicies OrganizationProcedurePolicy[]

  @@map("procedure_policies")
}

model Stakeholder {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  defaultId      Int?      @map("default_id") @db.UnsignedInt
  name           String    @map("name")
  type           Int       @map("type") @db.UnsignedTinyInt
  expectation    String?   @map("expectation")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  organization     Organization               @relation(fields: [organizationId], references: [id])
  createdUser      User                       @relation(fields: [createdUserId], references: [id])
  default          StakeholderDefault?        @relation(fields: [defaultId], references: [id])
  communications   StakeholderCommunication[]
  stakeholderUsers StakeholderUser[]

  @@map("stakeholders")
}

model StakeholderCommunication {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  stakeholderId Int       @map("stakeholder_id") @db.UnsignedInt
  methodType    Int?      @map("method_type") @db.UnsignedTinyInt
  methodText    String?   @map("method_text")
  frequencyType Int?      @map("frequency_type") @db.UnsignedTinyInt
  frequencyText String?   @map("frequency_text")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User        @relation(fields: [createdUserId], references: [id])
  stakeholder Stakeholder @relation(fields: [stakeholderId], references: [id])

  @@map("stakeholder_communications")
}

model StakeholderUser {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  stakeholderId Int       @map("stakeholder_id") @db.UnsignedInt
  name          String    @map("name")
  surname       String    @map("surname")
  email         String    @map("email") @db.VarChar(100)
  title         String?   @map("title")
  companyName   String?   @map("company_name")
  phone         String?   @map("phone") @db.VarChar(50)
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser  User          @relation(fields: [createdUserId], references: [id])
  stakeholder  Stakeholder   @relation(fields: [stakeholderId], references: [id])
  opinionHelps OpinionHelp[]

  @@map("stakeholder_users")
}

model StakeholderDefault {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  type      Int       @map("type") @db.UnsignedTinyInt
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  stakeholders Stakeholder[]

  @@map("stakeholder_defaults")
}

model QuickStart {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  frontSlug String    @map("front_slug")
  quickType Int       @map("quick_type") @db.UnsignedTinyInt
  order     Int       @map("order") @db.UnsignedTinyInt
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("quick_starts")
}

model FileManager {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  name           String    @map("name")
  url            String    @unique @map("url")
  type           Int       @map("type") @db.UnsignedTinyInt
  size           Int       @map("size") @db.UnsignedInt
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  createdUser  User         @relation(fields: [createdUserId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id])

  @@map("file_manager")
}

model Activity {
  id              Int      @id @default(autoincrement()) @db.UnsignedInt
  createdUserId   Int      @map("created_user_id") @db.UnsignedInt
  organizationId  Int      @map("organization_id") @db.UnsignedInt
  activityType    Int      @map("activity_type") @db.UnsignedTinyInt
  targetUserIds   Json?    @map("target_user_ids")
  effectedUserIds Json?    @map("effected_user_ids")
  payload         Json?    @map("payload")
  createdAt       DateTime @default(now()) @map("created_at")

  createdUser   User           @relation(fields: [createdUserId], references: [id])
  organization  Organization   @relation(fields: [organizationId], references: [id])
  notifications Notification[]

  @@map("activities")
}

model Notification {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  userId     Int      @map("user_id") @db.UnsignedInt
  activityId Int      @map("activity_id") @db.UnsignedInt
  isRead     Boolean  @default(false) @map("is_read")
  createdAt  DateTime @default(now()) @map("created_at")

  user     User     @relation(fields: [userId], references: [id])
  activity Activity @relation(fields: [activityId], references: [id])

  @@map("notifications")
}

model SituationReport {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  name           String    @map("name")
  isCompleted    Boolean   @default(false) @map("is_completed")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  createdUser  User                    @relation(fields: [createdUserId], references: [id])
  organization Organization            @relation(fields: [organizationId], references: [id])
  answers      SituationReportAnswer[]
  helps        SituationHelp[]

  @@map("situation_reports")
}

model SituationReportAnswer {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  reportId    Int       @map("report_id") @db.UnsignedInt
  questionId  Int       @map("question_id") @db.UnsignedInt
  answerType  Int?      @map("answer_type") @db.UnsignedTinyInt
  description String?   @map("description") @db.Text
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  report   SituationReport   @relation(fields: [reportId], references: [id])
  question SituationQuestion @relation(fields: [questionId], references: [id])

  @@map("situation_report_answers")
}

model SituationQuestion {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  categoryId Int       @map("category_id") @db.UnsignedInt
  typeId     Int?      @map("type_id") @db.UnsignedInt
  name       String    @map("name") @db.Text
  suggestion String    @map("suggestion") @db.Text
  info       String?   @map("info") @db.Text
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at")

  category      SituationQuestionCategory @relation(fields: [categoryId], references: [id])
  type          SituationQuestionType?    @relation(fields: [typeId], references: [id])
  reportAnswers SituationReportAnswer[]
  helps         SituationHelp[]

  @@map("situation_questions")
}

model SituationQuestionCategory {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  situationQuestions SituationQuestion[]

  @@map("situation_question_categories")
}

model SituationQuestionType {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  situationQuestions SituationQuestion[]

  @@map("situation_question_types")
}

model SituationHelp {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId  Int       @map("helped_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  questionId    Int       @map("question_id") @db.UnsignedInt
  status        Int       @default(0) @map("status") @db.TinyInt
  answerType    Int?      @map("answer_type") @db.UnsignedTinyInt
  description   String?   @map("description") @db.Text
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User              @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User              @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  report      SituationReport   @relation(fields: [reportId], references: [id])
  question    SituationQuestion @relation(fields: [questionId], references: [id])

  @@map("situation_helps")
}

model Trend {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  name          String    @map("name")
  source        String    @map("source")
  termId        Int       @map("term_id") @db.UnsignedInt
  categoryId    Int       @map("category_id") @db.UnsignedInt
  severity      Int?      @map("severity") @db.UnsignedTinyInt
  possibility   Int?      @map("possibility") @db.UnsignedTinyInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User          @relation(fields: [createdUserId], references: [id])
  report      TrendReport   @relation(fields: [reportId], references: [id])
  term        TrendTerm     @relation(fields: [termId], references: [id])
  category    TrendCategory @relation(fields: [categoryId], references: [id])
  helps       TrendHelp[]
  risks       Risk[]

  @@map("trends")
}

model TrendReport {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  name           String    @map("name")
  threshold      Int?      @map("threshold") @db.UnsignedTinyInt
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  createdUser  User         @relation(fields: [createdUserId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id])
  trends       Trend[]
  riskReports  RiskReport[]

  @@map("trend_reports")
}

model TrendDefault {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  name       String    @map("name")
  source     String    @map("source")
  termId     Int       @map("term_id") @db.UnsignedInt
  categoryId Int       @map("category_id") @db.UnsignedInt
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at")

  term     TrendTerm     @relation(fields: [termId], references: [id])
  category TrendCategory @relation(fields: [categoryId], references: [id])

  @@map("trend_defaults")
}

model TrendTerm {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  trends        Trend[]
  trendDefaults TrendDefault[]

  @@map("trend_terms")
}

model TrendCategory {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  trends        Trend[]
  trendDefaults TrendDefault[]

  @@map("trend_categories")
}

model TrendHelp {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId  Int       @map("helped_user_id") @db.UnsignedInt
  trendId       Int       @map("trend_id") @db.UnsignedInt
  status        Int       @default(0) @map("status") @db.TinyInt
  severity      Int?      @map("severity") @db.UnsignedTinyInt
  possibility   Int?      @map("possibility") @db.UnsignedTinyInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User  @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User  @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  trend       Trend @relation(fields: [trendId], references: [id])

  @@map("trend_helps")
}

model Risk {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  trendId       Int       @map("trend_id") @db.UnsignedInt
  riskText      String?   @map("risk_text")
  opportunity   String?   @map("opportunity")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User       @relation(fields: [createdUserId], references: [id])
  report      RiskReport @relation(fields: [reportId], references: [id])
  trend       Trend      @relation(fields: [trendId], references: [id])
  helps       RiskHelp[]

  @@map("risks")
}

model RiskReport {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  trendReportId  Int       @map("trend_report_id") @db.UnsignedInt
  name           String    @map("name")
  isCompleted    Boolean   @default(false) @map("is_completed")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  createdUser  User         @relation(fields: [createdUserId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id])
  trendReport  TrendReport  @relation(fields: [trendReportId], references: [id])
  risks        Risk[]

  @@map("risk_reports")
}

model RiskHelp {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId  Int       @map("helped_user_id") @db.UnsignedInt
  riskId        Int       @map("risk_id") @db.UnsignedInt
  status        Int       @default(0) @map("status") @db.TinyInt
  riskText      String?   @map("risk_text")
  opportunity   String?   @map("opportunity")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  risk        Risk @relation(fields: [riskId], references: [id])

  @@map("risk_helps")
}

model Chain {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  updatedUserId Int?      @map("updated_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  name          String    @map("name")
  description   String?   @map("description") @db.Text
  type          Int       @map("type") @db.UnsignedTinyInt
  order         Int       @map("order") @db.UnsignedTinyInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User        @relation(name: "createdUser", fields: [createdUserId], references: [id])
  updatedUser User?       @relation(name: "updatedUser", fields: [updatedUserId], references: [id])
  report      ChainReport @relation(fields: [reportId], references: [id])
  helps       ChainHelp[]

  @@map("chains")
}

model ChainReport {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  name           String    @map("name")
  isCompleted    Boolean   @default(false) @map("is_completed")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  createdUser      User              @relation(fields: [createdUserId], references: [id])
  organization     Organization      @relation(fields: [organizationId], references: [id])
  chains           Chain[]
  financialReports FinancialReport[]
  impactReports    ImpactReport[]

  @@map("chain_reports")
}

model ChainDefault {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  name        String    @map("name")
  description String?   @map("description") @db.Text
  type        Int       @map("type") @db.UnsignedTinyInt
  order       Int       @map("order") @db.UnsignedTinyInt
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  @@map("chain_defaults")
}

model ChainHelp {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId  Int       @map("helped_user_id") @db.UnsignedInt
  chainId       Int       @map("chain_id") @db.UnsignedInt
  status        Int       @default(0) @map("status") @db.TinyInt
  description   String?   @map("description") @db.Text
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User  @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User  @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  chain       Chain @relation(fields: [chainId], references: [id])

  @@map("chain_helps")
}

model Capital {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  updatedUserId Int?      @map("updated_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  name          String    @map("name")
  input         String?   @map("input") @db.Text
  output        String?   @map("output") @db.Text
  value         String?   @map("value") @db.Text
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User          @relation(name: "createdUser", fields: [createdUserId], references: [id])
  updatedUser User?         @relation(name: "updatedUser", fields: [updatedUserId], references: [id])
  report      CapitalReport @relation(fields: [reportId], references: [id])
  helps       CapitalHelp[]

  @@map("capitals")
}

model CapitalReport {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId  Int       @map("created_user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  name           String    @map("name")
  isCompleted    Boolean   @default(false) @map("is_completed")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  createdUser  User         @relation(fields: [createdUserId], references: [id])
  organization Organization @relation(fields: [organizationId], references: [id])
  capitals     Capital[]

  @@map("capital_reports")
}

model CapitalDefault {
  id        Int       @id @default(autoincrement()) @db.UnsignedInt
  name      String    @map("name")
  input     String?   @map("input") @db.Text
  output    String?   @map("output") @db.Text
  value     String?   @map("value") @db.Text
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("capital_defaults")
}

model CapitalHelp {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId  Int       @map("helped_user_id") @db.UnsignedInt
  capitalId     Int       @map("capital_id") @db.UnsignedInt
  status        Int       @default(0) @map("status") @db.TinyInt
  input         String?   @map("input") @db.Text
  output        String?   @map("output") @db.Text
  value         String?   @map("value") @db.Text
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User    @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User    @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  capital     Capital @relation(fields: [capitalId], references: [id])

  @@map("capital_helps")
}

model Opinion {
  id                 Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId      Int       @map("created_user_id") @db.UnsignedInt
  reportId           Int       @map("report_id") @db.UnsignedInt
  focusArea          String    @map("focus_area") @db.Text
  priorityIssue      String    @map("priority_issue") @db.Text
  subPriorityIssue   String    @map("sub_priority_issue") @db.Text
  minorPriorityIssue String?   @map("minor_priority_issue") @db.Text
  description        String?   @map("description") @db.Text
  isSelected         Boolean   @default(false) @map("is_selected")
  isDefault          Boolean   @default(false) @map("is_default")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  createdUser User                @relation(fields: [createdUserId], references: [id])
  report      OpinionReport       @relation(fields: [reportId], references: [id])
  helpAnswers OpinionHelpAnswer[]
  financials  Financial[]
  impacts     Impact[]

  @@map("opinions")
}

model OpinionReport {
  id                 Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId      Int       @map("created_user_id") @db.UnsignedInt
  organizationId     Int       @map("organization_id") @db.UnsignedInt
  name               String    @map("name")
  code               Int       @map("code") @db.UnsignedSmallInt
  financialThreshold Int?      @map("financial_threshold") @db.UnsignedTinyInt
  impactThreshold    Int?      @map("impact_threshold") @db.UnsignedTinyInt
  isCompleted        Boolean   @default(false) @map("is_completed")
  isReady            Boolean   @default(false) @map("is_ready")
  expiredAt          DateTime? @map("expired_at")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  createdUser      User              @relation(fields: [createdUserId], references: [id])
  organization     Organization      @relation(fields: [organizationId], references: [id])
  opinions         Opinion[]
  helps            OpinionHelp[]
  financialReports FinancialReport[]
  impactReports    ImpactReport[]

  @@map("opinion_reports")
}

model OpinionDefault {
  id                 Int       @id @default(autoincrement()) @db.UnsignedInt
  focusArea          String    @map("focus_area") @db.Text
  priorityIssue      String    @map("priority_issue") @db.Text
  subPriorityIssue   String    @map("sub_priority_issue") @db.Text
  minorPriorityIssue String?   @map("minor_priority_issue") @db.Text
  description        String?   @map("description") @db.Text
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  @@map("opinion_defaults")
}

model OpinionHelp {
  id                Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId     Int       @map("created_user_id") @db.UnsignedInt
  stakeholderUserId Int       @map("stakeholder_user_id") @db.UnsignedInt
  reportId          Int       @map("report_id") @db.UnsignedInt
  isFinancial       Boolean   @default(false) @map("is_financial")
  isImpact          Boolean   @default(false) @map("is_impact")
  financialStatus   Int       @default(0) @map("financial_status") @db.TinyInt
  impactStatus      Int       @default(0) @map("impact_status") @db.TinyInt
  token             String    @unique @map("token")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  createdUser     User                @relation(fields: [createdUserId], references: [id])
  stakeholderUser StakeholderUser     @relation(fields: [stakeholderUserId], references: [id])
  report          OpinionReport       @relation(fields: [reportId], references: [id])
  answers         OpinionHelpAnswer[]

  @@map("opinion_helps")
}

model OpinionHelpAnswer {
  id                      Int       @id @default(autoincrement()) @db.UnsignedInt
  opinionId               Int       @map("opinion_id") @db.UnsignedInt
  helpId                  Int       @map("help_id") @db.UnsignedInt
  financialQuestionAnswer Int?      @map("financial_question_answer") @db.UnsignedTinyInt
  impactQuestionAnswer    Int?      @map("impact_question_answer") @db.UnsignedTinyInt
  createdAt               DateTime  @default(now()) @map("created_at")
  updatedAt               DateTime  @updatedAt @map("updated_at")
  deletedAt               DateTime? @map("deleted_at")

  opinion Opinion     @relation(fields: [opinionId], references: [id])
  help    OpinionHelp @relation(fields: [helpId], references: [id])

  @@map("opinion_help_answers")
}

model Industry {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  sectorIds     Json      @map("sector_ids")
  sourceIds     Json?     @map("source_ids")
  sourceText    String?   @map("source_text")
  description   String    @map("description") @db.Text
  isDefault     Boolean   @default(false) @map("is_default")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User             @relation(fields: [createdUserId], references: [id])
  report      IndustryReport   @relation(fields: [reportId], references: [id])
  financials  Financial[]
  impacts     Impact[]
  details     IndustryDetail[]

  @@map("industries")
}

model IndustryDetail {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId    Int       @map("created_user_id") @db.UnsignedInt
  industryId       Int       @map("industry_id") @db.UnsignedInt
  priorityIssue    String    @map("priority_issue") @db.Text
  focusAreas       Json      @map("focus_areas")
  metricDefaultIds Json?     @map("metric_default_ids")
  isSelected       Boolean   @default(false) @map("is_selected")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  createdUser User                 @relation(fields: [createdUserId], references: [id])
  industry    Industry             @relation(fields: [industryId], references: [id])
  helpAnswers IndustryHelpAnswer[]

  @@map("industry_details")
}

model IndustryReport {
  id                 Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId      Int       @map("created_user_id") @db.UnsignedInt
  organizationId     Int       @map("organization_id") @db.UnsignedInt
  name               String    @map("name")
  code               Int       @map("code") @db.UnsignedSmallInt
  financialThreshold Int?      @map("financial_threshold") @db.UnsignedTinyInt
  impactThreshold    Int?      @map("impact_threshold") @db.UnsignedTinyInt
  isCompleted        Boolean   @default(false) @map("is_completed")
  isReady            Boolean   @default(false) @map("is_ready")
  expiredAt          DateTime? @map("expired_at")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  createdUser      User              @relation(fields: [createdUserId], references: [id])
  organization     Organization      @relation(fields: [organizationId], references: [id])
  industries       Industry[]
  helps            IndustryHelp[]
  financialReports FinancialReport[]
  impactReports    ImpactReport[]

  @@map("industry_reports")
}

model IndustryDefault {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  sectorIds   Json      @map("sector_ids")
  sourceIds   Json?     @map("source_ids")
  sourceText  String?   @map("source_text")
  description String    @map("description") @db.Text
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  details IndustryDefaultDetail[]

  @@map("industry_defaults")
}

model IndustryDefaultDetail {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  defaultId        Int       @map("default_id") @db.UnsignedInt
  priorityIssue    String    @map("priority_issue") @db.Text
  focusAreas       Json      @map("focus_areas")
  metricDefaultIds Json?     @map("metric_default_ids")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  default IndustryDefault @relation(fields: [defaultId], references: [id])

  @@map("industry_default_details")
}

model IndustryHelp {
  id              Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId   Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId    Int       @map("helped_user_id") @db.UnsignedInt
  reportId        Int       @map("report_id") @db.UnsignedInt
  isFinancial     Boolean   @default(false) @map("is_financial")
  isImpact        Boolean   @default(false) @map("is_impact")
  financialStatus Int       @default(0) @map("financial_status") @db.TinyInt
  impactStatus    Int       @default(0) @map("impact_status") @db.TinyInt
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  deletedAt       DateTime? @map("deleted_at")

  createdUser User                 @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User                 @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  report      IndustryReport       @relation(fields: [reportId], references: [id])
  answers     IndustryHelpAnswer[]

  @@map("industry_helps")
}

model IndustryHelpAnswer {
  id                      Int       @id @default(autoincrement()) @db.UnsignedInt
  helpId                  Int       @map("help_id") @db.UnsignedInt
  detailId                Int       @map("detail_id") @db.UnsignedInt
  financialQuestionAnswer Int?      @map("financial_question_answer") @db.UnsignedTinyInt
  impactQuestionAnswer    Int?      @map("impact_question_answer") @db.UnsignedTinyInt
  createdAt               DateTime  @default(now()) @map("created_at")
  updatedAt               DateTime  @updatedAt @map("updated_at")
  deletedAt               DateTime? @map("deleted_at")

  help   IndustryHelp   @relation(fields: [helpId], references: [id])
  detail IndustryDetail @relation(fields: [detailId], references: [id])

  @@map("industry_help_answers")
}

model Tendency {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  focusArea     String    @map("focus_area") @db.Text
  topic         String    @map("topic") @db.Text
  source        String    @map("source") @db.Text
  description   String    @map("description") @db.Text
  isSelected    Boolean   @default(false) @map("is_selected")
  isDefault     Boolean   @default(false) @map("is_default")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User                 @relation(fields: [createdUserId], references: [id])
  report      TendencyReport       @relation(fields: [reportId], references: [id])
  helpAnswers TendencyHelpAnswer[]
  financials  Financial[]
  impacts     Impact[]

  @@map("tendencies")
}

model TendencyReport {
  id                 Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId      Int       @map("created_user_id") @db.UnsignedInt
  organizationId     Int       @map("organization_id") @db.UnsignedInt
  name               String    @map("name")
  code               Int       @map("code") @db.UnsignedSmallInt
  financialThreshold Int?      @map("financial_threshold") @db.UnsignedTinyInt
  impactThreshold    Int?      @map("impact_threshold") @db.UnsignedTinyInt
  isCompleted        Boolean   @default(false) @map("is_completed")
  isReady            Boolean   @default(false) @map("is_ready")
  expiredAt          DateTime? @map("expired_at")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  createdUser      User              @relation(fields: [createdUserId], references: [id])
  organization     Organization      @relation(fields: [organizationId], references: [id])
  tendencies       Tendency[]
  helps            TendencyHelp[]
  financialReports FinancialReport[]
  impactReports    ImpactReport[]

  @@map("tendency_reports")
}

model TendencyDefault {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  focusArea   String    @map("focus_area") @db.Text
  topic       String    @map("topic") @db.Text
  source      String    @map("source") @db.Text
  description String    @map("description") @db.Text
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  @@map("tendency_defaults")
}

model TendencyHelp {
  id              Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId   Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId    Int       @map("helped_user_id") @db.UnsignedInt
  reportId        Int       @map("report_id") @db.UnsignedInt
  isFinancial     Boolean   @default(false) @map("is_financial")
  isImpact        Boolean   @default(false) @map("is_impact")
  financialStatus Int       @default(0) @map("financial_status") @db.TinyInt
  impactStatus    Int       @default(0) @map("impact_status") @db.TinyInt
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  deletedAt       DateTime? @map("deleted_at")

  createdUser User                 @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User                 @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  report      TendencyReport       @relation(fields: [reportId], references: [id])
  answers     TendencyHelpAnswer[]

  @@map("tendency_helps")
}

model TendencyHelpAnswer {
  id                      Int       @id @default(autoincrement()) @db.UnsignedInt
  tendencyId              Int       @map("tendency_id") @db.UnsignedInt
  helpId                  Int       @map("help_id") @db.UnsignedInt
  financialQuestionAnswer Int?      @map("financial_question_answer") @db.UnsignedTinyInt
  impactQuestionAnswer    Int?      @map("impact_question_answer") @db.UnsignedTinyInt
  createdAt               DateTime  @default(now()) @map("created_at")
  updatedAt               DateTime  @updatedAt @map("updated_at")
  deletedAt               DateTime? @map("deleted_at")

  tendency Tendency     @relation(fields: [tendencyId], references: [id])
  help     TendencyHelp @relation(fields: [helpId], references: [id])

  @@map("tendency_help_answers")
}

model Financial {
  id                 Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId      Int       @map("created_user_id") @db.UnsignedInt
  reportId           Int       @map("report_id") @db.UnsignedInt
  opinionId          Int?      @map("opinion_id") @db.UnsignedInt
  industryId         Int?      @map("industry_id") @db.UnsignedInt
  industryDetailId   Int?      @map("industry_detail_id") @db.UnsignedInt
  tendencyId         Int?      @map("tendency_id") @db.UnsignedInt
  focusAreas         Json      @map("focus_areas")
  priorityIssue      String    @map("priority_issue") @db.Text
  subPriorityIssue   String?   @map("sub_priority_issue") @db.Text
  minorPriorityIssue String?   @map("minor_priority_issue") @db.Text
  description        String?   @map("description") @db.Text
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  createdUser User              @relation(fields: [createdUserId], references: [id])
  report      FinancialReport   @relation(fields: [reportId], references: [id])
  opinion     Opinion?          @relation(fields: [opinionId], references: [id])
  industry    Industry?         @relation(fields: [industryId], references: [id])
  tendency    Tendency?         @relation(fields: [tendencyId], references: [id])
  details     FinancialDetail[]

  @@map("financials")
}

model FinancialDetail {
  id                  Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId       Int       @map("created_user_id") @db.UnsignedInt
  financialId         Int       @map("financial_id") @db.UnsignedInt
  categoryDescription String?   @map("category_description") @db.Text
  categoryType        Int?      @map("category_type") @db.UnsignedTinyInt
  periodType          Int?      @map("period_type") @db.UnsignedTinyInt
  chainIds            Json?     @map("chain_ids")
  countryIds          Json?     @map("country_ids")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  deletedAt           DateTime? @map("deleted_at")

  createdUser              User                     @relation(fields: [createdUserId], references: [id])
  financial                Financial                @relation(fields: [financialId], references: [id])
  answers                  FinancialHelpAnswer[]
  strategyDetailCategories StrategyDetailCategory[]

  @@map("financial_details")
}

model FinancialReport {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId    Int       @map("created_user_id") @db.UnsignedInt
  organizationId   Int       @map("organization_id") @db.UnsignedInt
  chainReportId    Int       @map("chain_report_id") @db.UnsignedInt
  opinionReportId  Int?      @map("opinion_report_id") @db.UnsignedInt
  industryReportId Int?      @map("industry_report_id") @db.UnsignedInt
  tendencyReportId Int?      @map("tendency_report_id") @db.UnsignedInt
  name             String    @map("name")
  code             Int       @map("code") @db.UnsignedSmallInt
  isCompleted      Boolean   @default(false) @map("is_completed")
  expiredAt        DateTime? @map("expired_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  createdUser     User                    @relation(fields: [createdUserId], references: [id])
  organization    Organization            @relation(fields: [organizationId], references: [id])
  chainReport     ChainReport?            @relation(fields: [chainReportId], references: [id])
  opinionReport   OpinionReport?          @relation(fields: [opinionReportId], references: [id])
  industryReport  IndustryReport?         @relation(fields: [industryReportId], references: [id])
  tendencyReport  TendencyReport?         @relation(fields: [tendencyReportId], references: [id])
  financials      Financial[]
  helps           FinancialHelp[]
  priorities      FinancialPriority[]
  reviews         FinancialReview[]
  significances   FinancialSignificance[]
  strategyReports StrategyReport[]

  @@map("financial_reports")
}

model FinancialHelp {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId  Int       @map("helped_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  status        Int       @default(0) @map("status") @db.TinyInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User                  @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User                  @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  report      FinancialReport       @relation(fields: [reportId], references: [id])
  answers     FinancialHelpAnswer[]

  @@map("financial_helps")
}

model FinancialHelpAnswer {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  helpId      Int       @map("help_id") @db.UnsignedInt
  detailId    Int       @map("detail_id") @db.UnsignedInt
  severity    Int       @map("severity") @db.UnsignedTinyInt
  possibility Int       @map("possibility") @db.UnsignedTinyInt
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  help   FinancialHelp   @relation(fields: [helpId], references: [id])
  detail FinancialDetail @relation(fields: [detailId], references: [id])

  @@map("financial_help_answers")
}

model FinancialPriority {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  minValue      Float     @map("min_value")
  maxValue      Float     @map("max_value")
  type          Int       @map("type") @db.UnsignedTinyInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User            @relation(fields: [createdUserId], references: [id])
  report      FinancialReport @relation(fields: [reportId], references: [id])

  @@map("financial_priorities")
}

model FinancialReview {
  id                  Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId       Int       @map("created_user_id") @db.UnsignedInt
  reviewerUserId      Int       @map("reviewer_user_id") @db.UnsignedInt
  reportId            Int       @map("report_id") @db.UnsignedInt
  status              Int       @default(0) @map("status") @db.TinyInt
  senderDescription   String?   @map("sender_description")
  reviewerDescription String?   @map("reviewer_description")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  deletedAt           DateTime? @map("deleted_at")

  createdUser  User            @relation(name: "createdUser", fields: [createdUserId], references: [id])
  reviewerUser User            @relation(name: "reviewerUser", fields: [reviewerUserId], references: [id])
  report       FinancialReport @relation(fields: [reportId], references: [id])

  @@map("financial_reviews")
}

model FinancialSignificance {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  name          String    @map("name")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User            @relation(fields: [createdUserId], references: [id])
  report      FinancialReport @relation(fields: [reportId], references: [id])

  @@map("financial_significances")
}

model Impact {
  id                 Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId      Int       @map("created_user_id") @db.UnsignedInt
  reportId           Int       @map("report_id") @db.UnsignedInt
  opinionId          Int?      @map("opinion_id") @db.UnsignedInt
  industryId         Int?      @map("industry_id") @db.UnsignedInt
  industryDetailId   Int?      @map("industry_detail_id") @db.UnsignedInt
  tendencyId         Int?      @map("tendency_id") @db.UnsignedInt
  focusAreas         Json      @map("focus_areas")
  priorityIssue      String    @map("priority_issue") @db.Text
  subPriorityIssue   String?   @map("sub_priority_issue") @db.Text
  minorPriorityIssue String?   @map("minor_priority_issue") @db.Text
  description        String?   @map("description") @db.Text
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  createdUser User           @relation(fields: [createdUserId], references: [id])
  report      ImpactReport   @relation(fields: [reportId], references: [id])
  opinion     Opinion?       @relation(fields: [opinionId], references: [id])
  industry    Industry?      @relation(fields: [industryId], references: [id])
  tendency    Tendency?      @relation(fields: [tendencyId], references: [id])
  details     ImpactDetail[]

  @@map("impacts")
}

model ImpactDetail {
  id                  Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId       Int       @map("created_user_id") @db.UnsignedInt
  impactId            Int       @map("impact_id") @db.UnsignedInt
  categoryDescription String?   @map("category_description") @db.Text
  categoryType        Int?      @map("category_type") @db.UnsignedTinyInt
  realityType         Int?      @map("reality_type") @db.UnsignedTinyInt
  chainIds            Json?     @map("chain_ids")
  countryIds          Json?     @map("country_ids")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  deletedAt           DateTime? @map("deleted_at")

  createdUser              User                     @relation(fields: [createdUserId], references: [id])
  impact                   Impact                   @relation(fields: [impactId], references: [id])
  answers                  ImpactHelpAnswer[]
  strategyDetailCategories StrategyDetailCategory[]

  @@map("impact_details")
}

model ImpactReport {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId    Int       @map("created_user_id") @db.UnsignedInt
  organizationId   Int       @map("organization_id") @db.UnsignedInt
  chainReportId    Int       @map("chain_report_id") @db.UnsignedInt
  opinionReportId  Int?      @map("opinion_report_id") @db.UnsignedInt
  industryReportId Int?      @map("industry_report_id") @db.UnsignedInt
  tendencyReportId Int?      @map("tendency_report_id") @db.UnsignedInt
  name             String    @map("name")
  code             Int       @map("code") @db.UnsignedSmallInt
  isCompleted      Boolean   @default(false) @map("is_completed")
  expiredAt        DateTime? @map("expired_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  createdUser     User                 @relation(fields: [createdUserId], references: [id])
  organization    Organization         @relation(fields: [organizationId], references: [id])
  chainReport     ChainReport?         @relation(fields: [chainReportId], references: [id])
  opinionReport   OpinionReport?       @relation(fields: [opinionReportId], references: [id])
  industryReport  IndustryReport?      @relation(fields: [industryReportId], references: [id])
  tendencyReport  TendencyReport?      @relation(fields: [tendencyReportId], references: [id])
  impacts         Impact[]
  helps           ImpactHelp[]
  priorities      ImpactPriority[]
  reviews         ImpactReview[]
  significances   ImpactSignificance[]
  strategyReports StrategyReport[]

  @@map("impact_reports")
}

model ImpactHelp {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId  Int       @map("helped_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  status        Int       @default(0) @map("status") @db.TinyInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User               @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User               @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  report      ImpactReport       @relation(fields: [reportId], references: [id])
  answers     ImpactHelpAnswer[]

  @@map("impact_helps")
}

model ImpactHelpAnswer {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  helpId      Int       @map("help_id") @db.UnsignedInt
  detailId    Int       @map("detail_id") @db.UnsignedInt
  scale       Int       @map("scale") @db.UnsignedTinyInt
  extent      Int       @map("extent") @db.UnsignedTinyInt
  reparation  Int?      @map("reparation") @db.UnsignedTinyInt
  possibility Int?      @map("possibility") @db.UnsignedTinyInt
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  help   ImpactHelp   @relation(fields: [helpId], references: [id])
  detail ImpactDetail @relation(fields: [detailId], references: [id])

  @@map("impact_help_answers")
}

model ImpactPriority {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  minValue      Float     @map("min_value")
  maxValue      Float     @map("max_value")
  type          Int       @map("type") @db.UnsignedTinyInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User         @relation(fields: [createdUserId], references: [id])
  report      ImpactReport @relation(fields: [reportId], references: [id])

  @@map("impact_priorities")
}

model ImpactReview {
  id                  Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId       Int       @map("created_user_id") @db.UnsignedInt
  reviewerUserId      Int       @map("reviewer_user_id") @db.UnsignedInt
  reportId            Int       @map("report_id") @db.UnsignedInt
  status              Int       @default(0) @map("status") @db.TinyInt
  senderDescription   String?   @map("sender_description")
  reviewerDescription String?   @map("reviewer_description")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  deletedAt           DateTime? @map("deleted_at")

  createdUser  User         @relation(name: "createdUser", fields: [createdUserId], references: [id])
  reviewerUser User         @relation(name: "reviewerUser", fields: [reviewerUserId], references: [id])
  report       ImpactReport @relation(fields: [reportId], references: [id])

  @@map("impact_reviews")
}

model ImpactSignificance {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  name          String    @map("name")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User         @relation(fields: [createdUserId], references: [id])
  report      ImpactReport @relation(fields: [reportId], references: [id])

  @@map("impact_significances")
}

model Strategy {
  id                Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId     Int       @map("created_user_id") @db.UnsignedInt
  updatedUserId     Int?      @map("updated_user_id") @db.UnsignedInt
  reportId          Int       @map("report_id") @db.UnsignedInt
  strategyFocusArea String?   @map("strategy_focus_area") @db.Text
  description       String?   @map("description") @db.Text
  shortTerm         String?   @map("short_term") @db.Text
  mediumTerm        String?   @map("medium_term") @db.Text
  longTerm          String?   @map("long_term") @db.Text
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  createdUser User             @relation("createdUser", fields: [createdUserId], references: [id])
  updatedUser User?            @relation("updatedUser", fields: [updatedUserId], references: [id])
  report      StrategyReport   @relation(fields: [reportId], references: [id])
  details     StrategyDetail[]
  helps       StrategyHelp[]

  @@map("strategies")
}

model StrategyDetail {
  id                  Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId       Int       @map("created_user_id") @db.UnsignedInt
  strategyId          Int       @map("strategy_id") @db.UnsignedInt
  financialIds        Json      @map("financial_ids")
  impactIds           Json      @map("impact_ids")
  priorityIssue       String    @map("priority_issue") @db.Text
  subPriorityIssues   Json      @map("sub_priority_issues")
  minorPriorityIssues Json      @map("minor_priority_issues")
  focusAreas          Json      @map("focus_areas")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  deletedAt           DateTime? @map("deleted_at")

  createdUser User                     @relation(fields: [createdUserId], references: [id])
  strategy    Strategy                 @relation(fields: [strategyId], references: [id])
  categories  StrategyDetailCategory[]
  metrics     Metric[]

  @@map("strategy_details")
}

model StrategyDetailCategory {
  id                Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId     Int       @map("created_user_id") @db.UnsignedInt
  detailId          Int       @map("detail_id") @db.UnsignedInt
  financialDetailId Int?      @map("financial_detail_id") @db.UnsignedInt
  impactDetailId    Int?      @map("impact_detail_id") @db.UnsignedInt
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  createdUser     User             @relation(fields: [createdUserId], references: [id])
  detail          StrategyDetail   @relation(fields: [detailId], references: [id])
  financialDetail FinancialDetail? @relation(fields: [financialDetailId], references: [id])
  impactDetail    ImpactDetail?    @relation(fields: [impactDetailId], references: [id])

  @@map("strategy_detail_categories")
}

model StrategyReport {
  id                Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId     Int       @map("created_user_id") @db.UnsignedInt
  organizationId    Int       @map("organization_id") @db.UnsignedInt
  financialReportId Int?      @map("financial_report_id") @db.UnsignedInt
  impactReportId    Int?      @map("impact_report_id") @db.UnsignedInt
  name              String    @map("name")
  code              Int       @map("code") @db.UnsignedSmallInt
  isRoadMap         Boolean   @default(false) @map("is_road_map")
  isCompleted       Boolean   @default(false) @map("is_completed")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  createdUser       User               @relation(fields: [createdUserId], references: [id])
  organization      Organization       @relation(fields: [organizationId], references: [id])
  financialReport   FinancialReport?   @relation(fields: [financialReportId], references: [id])
  impactReport      ImpactReport?      @relation(fields: [impactReportId], references: [id])
  strategies        Strategy[]
  terms             StrategyTerm[]
  governanceReports GovernanceReport[]

  @@map("strategy_reports")
}

model StrategyHelp {
  id                Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId     Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId      Int       @map("helped_user_id") @db.UnsignedInt
  strategyId        Int       @map("strategy_id") @db.UnsignedInt
  helpType          Int       @map("help_type") @db.UnsignedTinyInt
  status            Int       @default(0) @map("status") @db.TinyInt
  strategyFocusArea String?   @map("strategy_focus_area") @db.Text
  description       String?   @map("description") @db.Text
  shortTerm         String?   @map("short_term") @db.Text
  mediumTerm        String?   @map("medium_term") @db.Text
  longTerm          String?   @map("long_term") @db.Text
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  createdUser User     @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User     @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  strategy    Strategy @relation(fields: [strategyId], references: [id])

  @@map("strategy_helps")
}

model StrategyTerm {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  reportId      Int       @map("report_id") @db.UnsignedInt
  minValue      Float     @map("min_value")
  maxValue      Float     @map("max_value")
  type          Int       @map("type") @db.UnsignedTinyInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User           @relation(fields: [createdUserId], references: [id])
  report      StrategyReport @relation(fields: [reportId], references: [id])

  @@map("strategy_terms")
}

model Governance {
  id                   Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId        Int       @map("created_user_id") @db.UnsignedInt
  reportId             Int       @map("report_id") @db.UnsignedInt
  parentId             Int?      @map("parent_id") @db.UnsignedInt
  name                 String    @map("name")
  description          String?   @map("description") @db.Text
  meetingFrequencyType Int?      @map("meeting_frequency_type") @db.UnsignedTinyInt
  priorityIssues       Json?     @map("priority_issues")
  order                Int       @map("order") @db.UnsignedTinyInt
  isDefault            Boolean   @default(false) @map("is_default")
  isTop                Boolean   @default(false) @map("is_top")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")
  deletedAt            DateTime? @map("deleted_at")

  createdUser    User                @relation(fields: [createdUserId], references: [id])
  report         GovernanceReport    @relation(fields: [reportId], references: [id])
  parent         Governance?         @relation("parent", fields: [parentId], references: [id])
  subGovernances Governance[]        @relation("parent")
  members        GovernanceMember[]
  meetings       GovernanceMeeting[]

  @@map("governances")
}

model GovernanceReport {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId    Int       @map("created_user_id") @db.UnsignedInt
  organizationId   Int       @map("organization_id") @db.UnsignedInt
  strategyReportId Int       @map("strategy_report_id") @db.UnsignedInt
  name             String    @map("name")
  code             Int       @map("code") @db.UnsignedSmallInt
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  createdUser    User           @relation(fields: [createdUserId], references: [id])
  organization   Organization   @relation(fields: [organizationId], references: [id])
  strategyReport StrategyReport @relation(fields: [strategyReportId], references: [id])
  governances    Governance[]
  metricReports  MetricReport[]

  @@map("governance_reports")
}

model GovernanceMember {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  governanceId  Int       @map("governance_id") @db.UnsignedInt
  profileId     Int       @map("profile_id") @db.UnsignedInt
  department    String    @map("department")
  isLead        Boolean   @default(false) @map("is_lead")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User                    @relation(fields: [createdUserId], references: [id])
  governance  Governance              @relation(fields: [governanceId], references: [id])
  profile     GovernanceMemberProfile @relation(fields: [profileId], references: [id])

  @@map("governance_members")
}

model GovernanceMemberProfile {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  userId         Int?      @map("user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  name           String    @map("name") @db.VarChar(50)
  surname        String    @map("surname") @db.VarChar(50)
  email          String    @map("email") @db.VarChar(100)
  genderType     Int?      @map("gender_type") @db.UnsignedTinyInt
  educationType  Int?      @map("education_type") @db.UnsignedTinyInt
  experienceYear Int?      @map("experience_year") @db.UnsignedTinyInt
  experienceType Int?      @map("experience_type") @db.UnsignedTinyInt
  experienceText String?   @map("experience_text")
  birthDate      DateTime? @map("birth_date") @db.Date
  token          String?   @unique @map("token")
  code           String?   @map("code") @db.VarChar(6)
  tokenExpiredAt DateTime? @map("token_expired_at")
  codeExpiredAt  DateTime? @map("code_expired_at")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  user         User?              @relation(fields: [userId], references: [id])
  organization Organization       @relation(fields: [organizationId], references: [id])
  members      GovernanceMember[]

  @@map("governance_member_profiles")
}

model GovernanceMeeting {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId    Int       @map("created_user_id") @db.UnsignedInt
  governanceId     Int       @map("governance_id") @db.UnsignedInt
  memberProfileIds Json?     @map("member_profile_ids")
  fileIds          Json?     @map("file_ids")
  meetingDate      DateTime  @map("meeting_date")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  createdUser User       @relation(fields: [createdUserId], references: [id])
  governance  Governance @relation(fields: [governanceId], references: [id])

  @@map("governance_meetings")
}

model Metric {
  id               Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId    Int       @map("created_user_id") @db.UnsignedInt
  reportId         Int       @map("report_id") @db.UnsignedInt
  carbonReportId   Int?      @map("carbon_report_id") @db.UnsignedInt
  strategyDetailId Int?      @map("strategy_detail_id") @db.UnsignedInt
  defaultId        Int?      @map("default_id") @db.UnsignedInt
  facilityIds      Json?     @map("facility_ids")
  metricName       String?   @map("metric_name")
  goalName         String?   @map("goal_name")
  categoryType     Int?      @map("category_type") @db.UnsignedTinyInt
  unitType         String?   @map("unit_type")
  periodType       Int?      @map("period_type") @db.UnsignedTinyInt
  note             String?   @map("note") @db.Text
  isActivity       Boolean   @default(false) @map("is_activity")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  createdUser    User                @relation(name: "createdUser", fields: [createdUserId], references: [id])
  report         MetricReport        @relation(fields: [reportId], references: [id])
  strategyDetail StrategyDetail?     @relation(fields: [strategyDetailId], references: [id])
  default        MetricDefault?      @relation(fields: [defaultId], references: [id])
  performances   MetricPerformance[]
  helps          MetricHelp[]
  supervisors    MetricSupervisor[]
  activities     MetricActivity[]

  @@map("metrics")
}

model MetricReport {
  id                 Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId      Int       @map("created_user_id") @db.UnsignedInt
  organizationId     Int       @map("organization_id") @db.UnsignedInt
  governanceReportId Int       @map("governance_report_id") @db.UnsignedInt
  name               String    @map("name")
  code               Int       @map("code") @db.UnsignedSmallInt
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  createdUser      User             @relation(fields: [createdUserId], references: [id])
  organization     Organization     @relation(fields: [organizationId], references: [id])
  governanceReport GovernanceReport @relation(fields: [governanceReportId], references: [id])

  metrics Metric[]

  @@map("metric_reports")
}

model MetricDefault {
  id           Int       @id @default(autoincrement()) @db.UnsignedInt
  metricName   String    @map("metric_name") @db.VarChar(300)
  categoryType Int       @map("category_type") @db.UnsignedTinyInt
  unitType     String?   @map("unit_type")
  note         String?   @map("note") @db.Text
  sectorId     Int?      @map("sector_id") @db.UnsignedInt
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at")

  metrics Metric[]
  charts  Chart[]
  sector  Sector?  @relation(fields: [sectorId], references: [id])

  @@map("metric_defaults")
}

model MetricPerformance {
  id              Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId   Int       @map("created_user_id") @db.UnsignedInt
  metricId        Int       @map("metric_id") @db.UnsignedInt
  year            Int       @map("year") @db.UnsignedSmallInt
  months          Json      @map("months")
  value           String?   @map("value")
  performanceType Int       @map("performance_type") @db.UnsignedTinyInt
  isBased         Boolean   @default(false) @map("is_based")
  description     String?   @map("description") @db.Text
  fileIds         Json?     @map("file_ids")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  deletedAt       DateTime? @map("deleted_at")

  createdUser User   @relation(fields: [createdUserId], references: [id])
  metric      Metric @relation(fields: [metricId], references: [id])

  @@map("metric_performances")
}

model MetricHelp {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  helpedUserId  Int       @map("helped_user_id") @db.UnsignedInt
  metricId      Int       @map("metric_id") @db.UnsignedInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User   @relation(name: "createdUser", fields: [createdUserId], references: [id])
  helpedUser  User   @relation(name: "helpedUser", fields: [helpedUserId], references: [id])
  metric      Metric @relation(fields: [metricId], references: [id])

  @@map("metric_helps")
}

model MetricSupervisor {
  id            Int       @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int       @map("created_user_id") @db.UnsignedInt
  metricId      Int       @map("metric_id") @db.UnsignedInt
  profileId     Int       @map("profile_id") @db.UnsignedInt
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  createdUser User                    @relation(fields: [createdUserId], references: [id])
  metric      Metric                  @relation(fields: [metricId], references: [id])
  profile     MetricSupervisorProfile @relation(fields: [profileId], references: [id])

  @@map("metric_supervisors")
}

model MetricSupervisorProfile {
  id             Int       @id @default(autoincrement()) @db.UnsignedInt
  userId         Int       @map("user_id") @db.UnsignedInt
  organizationId Int       @map("organization_id") @db.UnsignedInt
  department     String    @map("department")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  user         User               @relation(fields: [userId], references: [id])
  organization Organization       @relation(fields: [organizationId], references: [id])
  supervisors  MetricSupervisor[]

  @@map("metric_supervisor_profiles")
}

model MetricActivity {
  id            Int      @id @default(autoincrement()) @db.UnsignedInt
  createdUserId Int      @map("created_user_id") @db.UnsignedInt
  metricId      Int      @map("metric_id") @db.UnsignedInt
  activityType  Int      @map("activity_type") @db.UnsignedTinyInt
  relatedId     Int?     @map("related_id") @db.UnsignedInt
  payload       Json?    @map("payload")
  createdAt     DateTime @default(now()) @map("created_at")

  createdUser User   @relation(fields: [createdUserId], references: [id])
  metric      Metric @relation(fields: [metricId], references: [id])

  @@map("metric_activities")
}

model ChartTemplate {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  modelType  Int       @map("model_type") @db.UnsignedTinyInt
  title      String    @map("title")
  xAxis      Int       @map("x_axis") @db.UnsignedTinyInt
  yAxis      Int       @map("y_axis") @db.UnsignedTinyInt
  xAxisLabel String    @map("x_axis_label")
  yAxisLabel String    @map("y_axis_label")
  graphType  Int       @map("graph_type") @db.UnsignedTinyInt
  filterKey  Int?      @map("filter_key") @db.UnsignedTinyInt
  filterType Int?      @map("filter_type") @db.UnsignedTinyInt
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at")

  charts Chart[]

  @@map("chart_templates")
}

model Chart {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  templateId Int       @map("template_id") @db.UnsignedInt
  defaultId  Int       @map("default_id") @db.UnsignedInt
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at")

  template      ChartTemplate  @relation(fields: [templateId], references: [id])
  metricDefault MetricDefault? @relation(fields: [defaultId], references: [id])

  @@map("charts")
}
