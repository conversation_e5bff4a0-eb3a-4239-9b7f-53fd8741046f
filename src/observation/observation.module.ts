import { Module } from '@nestjs/common';
import { CommonModule } from '../common/common.module';
import { ObservationMetricService } from './services/observation-metric.service';
import { ObservationMetricValidation } from './validations/observation-metric.validation';
import { ObservationMetricController } from './controllers/observation-metric.controller';
import { DesignModule } from '../design/design.module';

@Module({
  imports: [DesignModule, CommonModule],
  controllers: [ObservationMetricController],
  providers: [ObservationMetricService, ObservationMetricValidation],
  exports: [],
})
export class ObservationModule {}
