import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { MetricPerformanceTypeEnum } from '../../design/enums/metric-performance-type.enum';
import { MetricCategoryTypeEnum } from '../../design/enums/metric-category-type.enum';
import { UpdateObservationMetricPerformanceBaseDto } from '../dtos/metric/update-observation-metric-performance-base.dto';
import { DeleteObservationMetricPerformanceDto } from '../dtos/metric/delete-observation-metric-performance.dto';
import { UpdateObservationMetricPerformanceDto } from '../dtos/metric/update-observation-metric-performance.dto';
import { CreateObservationMetricPerformanceDto } from '../dtos/metric/create-observation-metric-performance.dto';

@Injectable()
export class ObservationMetricValidation {
  constructor(private prismaService: PrismaService) {}

  async createPerformance(
    user: UserDto,
    dto: CreateObservationMetricPerformanceDto,
  ) {
    const metric = await this.prismaService.metric.findFirstOrThrow({
      where: {
        id: dto.metricId,
        report: { organizationId: user.organizationId },
      },
      include: { performances: true },
    });
    if (dto.performanceType == MetricPerformanceTypeEnum.METRIC) {
      if (!dto.months?.length) {
        throw new BadRequestException('Metrik performansında ay girilmelidir.');
      }
    } else {
      const isExist = metric.performances.some(
        (p) => p.performanceType == dto.performanceType,
      );
      if (isExist) {
        throw new BadRequestException('İlgili hedef yılı zaten eklenmiştir.');
      }
    }
    if (dto.months?.length > 0) {
      if (Math.min(...dto.months) < 1 || Math.max(...dto.months) > 12) {
        throw new BadRequestException('Geçersiz ay verisi.');
      }
    }
    if (metric.categoryType == MetricCategoryTypeEnum.QUANTITATIVE) {
      if (dto.value && isNaN(Number(dto.value))) {
        throw new BadRequestException('Değer sayısal olmalıdır.');
      }
    }
    if (dto.fileIds) {
      const fileCount = await this.prismaService.fileManager.count({
        where: {
          id: { in: dto.fileIds },
          organizationId: user.organizationId,
        },
      });
      if (fileCount != dto.fileIds.length) {
        throw new BadRequestException('Geçersiz dosyalar.');
      }
    }
  }

  async updatePerformance(
    user: UserDto,
    dto: UpdateObservationMetricPerformanceDto,
  ) {
    const performance =
      await this.prismaService.metricPerformance.findFirstOrThrow({
        where: {
          id: dto.performanceId,
          metric: { report: { organizationId: user.organizationId } },
        },
        include: { metric: true },
      });
    if (performance.performanceType == MetricPerformanceTypeEnum.METRIC) {
      if (dto.months && dto.months.length == 0) {
        throw new BadRequestException('Metrik performansında ay silinemez.');
      }
    }
    if (dto.months?.length > 0) {
      if (Math.min(...dto.months) < 1 || Math.max(...dto.months) > 12) {
        throw new BadRequestException('Geçersiz ay verisi.');
      }
    }
    if (
      performance.metric.categoryType == MetricCategoryTypeEnum.QUANTITATIVE
    ) {
      if (dto.value && isNaN(Number(dto.value))) {
        throw new BadRequestException('Değer sayısal olmalıdır.');
      }
    }
    if (dto.fileIds) {
      const fileCount = await this.prismaService.fileManager.count({
        where: {
          id: { in: dto.fileIds },
          organizationId: user.organizationId,
        },
      });
      if (fileCount != dto.fileIds.length) {
        throw new BadRequestException('Geçersiz dosyalar.');
      }
    }
  }

  async updatePerformanceBase(
    user: UserDto,
    dto: UpdateObservationMetricPerformanceBaseDto,
  ) {
    await this.prismaService.metric.findFirstOrThrow({
      where: {
        id: dto.metricId,
        report: { organizationId: user.organizationId },
      },
    });
    await this.prismaService.metricPerformance.findFirstOrThrow({
      where: {
        metricId: dto.metricId,
        year: dto.year,
        performanceType: MetricPerformanceTypeEnum.METRIC,
      },
    });
  }

  async deletePerformance(
    user: UserDto,
    dto: DeleteObservationMetricPerformanceDto,
  ) {
    await this.prismaService.metricPerformance.findFirstOrThrow({
      where: {
        id: dto.performanceId,
        metric: { report: { organizationId: user.organizationId } },
      },
    });
  }
}
