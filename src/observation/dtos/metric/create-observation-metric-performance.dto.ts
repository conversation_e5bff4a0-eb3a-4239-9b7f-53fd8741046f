import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayUnique,
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  MinLength,
} from 'class-validator';
import { MetricPerformanceTypeEnum } from '../../../design/enums/metric-performance-type.enum';

export class CreateObservationMetricPerformanceDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  metricId: number;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1900)
  @Max(2100)
  year: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  months?: number[];

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  value?: string;

  @ApiProperty({ type: Number, enum: MetricPerformanceTypeEnum })
  @IsEnum(MetricPerformanceTypeEnum)
  performanceType: MetricPerformanceTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  fileIds?: number[];
}
