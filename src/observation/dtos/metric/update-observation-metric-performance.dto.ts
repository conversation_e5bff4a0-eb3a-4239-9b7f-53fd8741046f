import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayUnique,
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  MinLength,
} from 'class-validator';

export class UpdateObservationMetricPerformanceDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  performanceId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1900)
  @Max(2100)
  @IsOptional()
  year?: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  months?: number[];

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  value?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  fileIds?: number[];
}
