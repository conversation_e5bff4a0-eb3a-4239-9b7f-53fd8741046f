import { Body, Controller, Delete, Post, Put } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ObservationMetricService } from '../services/observation-metric.service';
import { ObservationMetricValidation } from '../validations/observation-metric.validation';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { User } from '../../common/decorators/auth.decorator';
import { UserDto } from '../../common/dtos/user.dto';
import { CreateObservationMetricPerformanceDto } from '../dtos/metric/create-observation-metric-performance.dto';
import { UpdateObservationMetricPerformanceDto } from '../dtos/metric/update-observation-metric-performance.dto';
import { UpdateObservationMetricPerformanceBaseDto } from '../dtos/metric/update-observation-metric-performance-base.dto';
import { DeleteObservationMetricPerformanceDto } from '../dtos/metric/delete-observation-metric-performance.dto';

@ApiTags('Observations/Metrics')
@Controller('observations/metrics')
export class ObservationMetricController {
  constructor(
    private observationMetricService: ObservationMetricService,
    private observationMetricValidation: ObservationMetricValidation,
  ) {}

  @Post('performances')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createPerformance(
    @User() user: UserDto,
    @Body() dto: CreateObservationMetricPerformanceDto,
  ): Promise<GeneralResponseDto> {
    await this.observationMetricValidation.createPerformance(user, dto);
    await this.observationMetricService.createPerformance(user, dto);
    return new GeneralResponseDto();
  }

  @Put('performances')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updatePerformance(
    @User() user: UserDto,
    @Body() dto: UpdateObservationMetricPerformanceDto,
  ): Promise<GeneralResponseDto> {
    await this.observationMetricValidation.updatePerformance(user, dto);
    await this.observationMetricService.updatePerformance(user, dto);
    return new GeneralResponseDto();
  }

  @Put('performances/base')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updatePerformanceBase(
    @User() user: UserDto,
    @Body() dto: UpdateObservationMetricPerformanceBaseDto,
  ): Promise<GeneralResponseDto> {
    await this.observationMetricValidation.updatePerformanceBase(user, dto);
    await this.observationMetricService.updatePerformanceBase(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('performances')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deletePerformance(
    @User() user: UserDto,
    @Body() dto: DeleteObservationMetricPerformanceDto,
  ): Promise<GeneralResponseDto> {
    await this.observationMetricValidation.deletePerformance(user, dto);
    await this.observationMetricService.deletePerformance(user, dto);
    return new GeneralResponseDto();
  }
}
