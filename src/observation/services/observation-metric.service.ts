import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { MetricPerformanceTypeEnum } from '../../design/enums/metric-performance-type.enum';
import { MetricActivityTypeEnum } from '../../design/enums/metric-activity-type.enum';
import { MetricService } from '../../design/services/metric.service';
import { CreateObservationMetricPerformanceDto } from '../dtos/metric/create-observation-metric-performance.dto';
import { UpdateObservationMetricPerformanceDto } from '../dtos/metric/update-observation-metric-performance.dto';
import { UpdateObservationMetricPerformanceBaseDto } from '../dtos/metric/update-observation-metric-performance-base.dto';
import { DeleteObservationMetricPerformanceDto } from '../dtos/metric/delete-observation-metric-performance.dto';

@Injectable()
export class ObservationMetricService {
  constructor(
    private prismaService: PrismaService,
    private metricService: MetricService,
  ) {}

  async createPerformance(
    user: UserDto,
    dto: CreateObservationMetricPerformanceDto,
  ) {
    let isBased = false;
    if (dto.performanceType == MetricPerformanceTypeEnum.METRIC) {
      isBased = !!(await this.prismaService.metricPerformance.findFirst({
        where: {
          metricId: dto.metricId,
          year: dto.year,
          performanceType: MetricPerformanceTypeEnum.METRIC,
          isBased: true,
        },
      }));
    }
    await this.prismaService.$transaction(async (tx) => {
      const performance = await tx.metricPerformance.create({
        data: {
          createdUserId: user.id,
          metricId: dto.metricId,
          year: dto.year,
          months: dto.months,
          value: dto.value,
          performanceType: dto.performanceType,
          isBased: isBased,
          description: dto.description,
          fileIds: dto.fileIds,
        },
      });
      await this.metricService.createActivity(
        user,
        {
          metricId: performance.metricId,
          activityType: MetricActivityTypeEnum.PERFORMANCE_CREATE,
          relatedId: performance.id,
          payload: {
            year: performance.year,
            months: performance.months,
            value: performance.value,
            description: performance.description,
            fileIds: performance.fileIds,
          },
        },
        { tx: tx },
      );
    });
  }

  async updatePerformance(
    user: UserDto,
    dto: UpdateObservationMetricPerformanceDto,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      const performance = await tx.metricPerformance.update({
        where: { id: dto.performanceId },
        data: {
          year: dto.year,
          months: dto.months,
          value: dto.value,
          description: dto.description,
          fileIds: dto.fileIds,
        },
      });
      await this.metricService.createActivity(
        user,
        {
          metricId: performance.metricId,
          activityType: MetricActivityTypeEnum.PERFORMANCE_UPDATE,
          relatedId: performance.id,
          payload: {
            year: performance.year,
            months: performance.months,
            value: performance.value,
            description: performance.description,
            fileIds: performance.fileIds,
          },
        },
        { tx: tx },
      );
    });
  }

  async updatePerformanceBase(
    user: UserDto,
    dto: UpdateObservationMetricPerformanceBaseDto,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.metricPerformance.updateMany({
        where: {
          metricId: dto.metricId,
          year: dto.year,
          isBased: false,
        },
        data: { isBased: true },
      });
      await tx.metricPerformance.updateMany({
        where: {
          metricId: dto.metricId,
          year: { not: dto.year },
          isBased: true,
        },
        data: { isBased: false },
      });
      await this.metricService.createActivity(
        user,
        {
          metricId: dto.metricId,
          activityType: MetricActivityTypeEnum.PERFORMANCE_BASE,
          payload: { year: dto.year },
        },
        { tx: tx },
      );
    });
  }

  async deletePerformance(
    user: UserDto,
    dto: DeleteObservationMetricPerformanceDto,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      const performance = await tx.metricPerformance.delete({
        where: {
          id: dto.performanceId,
          metric: { report: { organizationId: user.organizationId } },
        },
      });
      await this.metricService.createActivity(
        user,
        {
          metricId: performance.metricId,
          activityType: MetricActivityTypeEnum.PERFORMANCE_DELETE,
          relatedId: performance.id,
        },
        { tx: tx },
      );
    });
  }
}
