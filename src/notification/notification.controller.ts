import { Body, Controller, Get, Put, Query } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { NotificationService } from './notification.service';
import { User } from '../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../common/dtos/general-response.dto';
import { UserDto } from '../common/dtos/user.dto';
import { NotificationDto } from './dtos/notification.dto';
import { ReadNotificationDto } from './dtos/read-notification.dto';
import { GetNotificationDto } from './dtos/get-notification.dto';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationController {
  constructor(private notificationService: NotificationService) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto<NotificationDto> })
  async getNotifications(
    @User() user: UserDto,
    @Query() dto: GetNotificationDto,
  ): Promise<GeneralResponseDto> {
    const { totalNotificationCount, notifications } =
      await this.notificationService.getNotifications(user, dto);
    return new GeneralResponseDto<NotificationDto>()
      .setPaginationTotalCount(totalNotificationCount)
      .setData(notifications);
  }

  @Put('read')
  @ApiBearerAuth()
  @ApiBody({ type: ReadNotificationDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async readNotifications(
    @User() user: UserDto,
    @Body() dto: ReadNotificationDto,
  ): Promise<GeneralResponseDto> {
    await this.notificationService.readNotifications(user, dto);
    return new GeneralResponseDto();
  }
}
