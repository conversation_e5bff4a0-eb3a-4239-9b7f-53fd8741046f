import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { NotificationDto } from './notification.dto';
import { Type } from 'class-transformer';

export class NotificationGatewayDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  unreadCount: number;

  @ApiProperty({ type: NotificationDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NotificationDto)
  notifications: NotificationDto[];
}
