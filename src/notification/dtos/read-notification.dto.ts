import { <PERSON><PERSON><PERSON><PERSON>, IsBoolean, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ReadNotificationDto {
  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  readAll?: boolean;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  notificationIds?: number[];
}
