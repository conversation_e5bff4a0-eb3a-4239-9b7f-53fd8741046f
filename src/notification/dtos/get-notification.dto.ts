import { ApiProperty } from '@nestjs/swagger';
import {
  isArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsO<PERSON>al,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ActivityEnum } from '../../activity/enums/activity.enum';

export class GetNotificationDto {
  @ApiProperty({ type: Number, required: false, description: 'starts with 0' })
  @Transform(({ value }) => +value)
  @IsNumber({ maxDecimalPlaces: 0 })
  @IsOptional()
  @Min(0)
  page?: number;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber({ maxDecimalPlaces: 0 })
  @IsOptional()
  @Min(0)
  @Max(50)
  limit?: number;

  @ApiProperty({
    type: Number,
    isArray: true,
    enum: ActivityEnum,
    required: false,
  })
  @Transform(({ value }) => (isArray(value) ? value.map((v) => +v) : [+value]))
  @IsEnum(ActivityEnum, { each: true })
  @IsOptional()
  activityTypes?: ActivityEnum[];

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value == 'true')
  @IsBoolean()
  @IsOptional()
  isRead?: boolean;
}
