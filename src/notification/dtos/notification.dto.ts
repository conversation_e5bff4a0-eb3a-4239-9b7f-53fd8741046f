import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { ActivityActionDto } from '../../activity/dtos/req/activity-action.dto';

export class NotificationDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  id: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  userId: number;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  isRead: boolean;

  @ApiProperty({ type: String })
  @IsString()
  content: string;

  @ApiProperty({ type: ActivityActionDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ActivityActionDto)
  actions: ActivityActionDto[];

  @ApiProperty({ type: String })
  @IsDateString()
  createdAt: Date;

  @ApiProperty({ type: UserDataDto })
  @ValidateNested({ each: true })
  @Type(() => UserDataDto)
  createdUser: UserDataDto;
}
