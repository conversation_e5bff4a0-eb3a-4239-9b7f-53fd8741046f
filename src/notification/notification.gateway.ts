import { UtilService } from '../common/services/util.service';
import { NotificationGatewayDto } from './dtos/notification-gateway.dto';
import { NotificationDto } from './dtos/notification.dto';
import { SocketIoGateway } from '../common/gateways/socket-io.gateway';
import { Injectable } from '@nestjs/common';

@Injectable()
export class NotificationGateway {
  constructor(
    private socketIoGateway: SocketIoGateway,
    private utilService: UtilService,
  ) {}

  sendNotifications(
    organizationId: number,
    unreadCounts: { [key: number]: number },
    notificationContents: NotificationDto[],
  ): void {
    const groups =
      this.utilService.groupByUserId<NotificationDto>(notificationContents);
    Object.entries(groups).map(([userId, contents]) => {
      const dto: NotificationGatewayDto = {
        unreadCount: unreadCounts[userId] ?? 0,
        notifications: contents,
      };
      this.socketIoGateway.sendEmit(
        this.utilService.socketRoom(+userId, organizationId),
        'notification',
        dto,
      );
    });
  }

  syncBadge(userId: number, organizationId: number, unreadCount: number): void {
    this.socketIoGateway.sendEmit(
      this.utilService.socketRoom(userId, organizationId),
      'notification',
      {
        unreadCount: unreadCount,
        notifications: [],
      } as NotificationGatewayDto,
    );
  }
}
