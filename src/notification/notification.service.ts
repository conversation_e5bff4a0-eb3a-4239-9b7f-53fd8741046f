import { Injectable } from '@nestjs/common';
import { UserDto } from '../common/dtos/user.dto';
import { NotificationGateway } from './notification.gateway';
import { NotificationDto } from './dtos/notification.dto';
import { PrismaService } from '../common/services/prisma.service';
import { UtilService } from '../common/services/util.service';
import { Activity, Prisma, PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { ReadNotificationDto } from './dtos/read-notification.dto';
import { GetNotificationDto } from './dtos/get-notification.dto';
import { ActivityTabType } from '../activity/types/activity-tab.type';
import { ActivityTabEnum } from '../activity/enums/activity-tab.enum';
import { ActivityUtilService } from '../common/services/activity.util.service';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../common/dtos/user-data.dto';

@Injectable()
export class NotificationService {
  constructor(
    private prismaService: PrismaService,
    private notificationGateway: NotificationGateway,
    private activityUtilService: ActivityUtilService,
    private utilService: UtilService,
  ) {}

  async getNotifications(
    user: UserDto,
    dto: GetNotificationDto,
  ): Promise<{
    totalNotificationCount: number;
    notifications: NotificationDto[];
  }> {
    const notificationWhere: Prisma.NotificationWhereInput = {
      userId: user.id,
      isRead: dto.isRead,
      activity: {
        organizationId: user.organizationId,
        activityType: { in: dto.activityTypes },
      },
    };
    const totalNotificationCount = await this.prismaService.notification.count({
      where: notificationWhere,
    });
    const notifications = await this.prismaService.notification.findMany({
      skip: this.utilService.getPaginationSkip(dto.page, dto.limit),
      take: this.utilService.getPaginationTake(dto.limit),
      where: notificationWhere,
      orderBy: { createdAt: 'desc' },
      include: { activity: { include: { createdUser: true } } },
    });
    const notificationContents = await this.createNotificationContent(
      user.organizationId,
      notifications,
    );
    return {
      totalNotificationCount: totalNotificationCount,
      notifications: notificationContents,
    };
  }

  async getUnreadCounts(
    userIds: number[],
    organizationId: number,
    tx?: Omit<PrismaClient, ITXClientDenyList>,
  ): Promise<{ [key: number]: number }> {
    const dbService = tx ?? this.prismaService;
    const unreadGroupByUserId = await dbService.notification.groupBy({
      _count: { _all: true },
      by: ['userId'],
      where: {
        userId: { in: userIds },
        isRead: false,
        activity: {
          organizationId: organizationId,
          activityType: { in: ActivityTabType[ActivityTabEnum.ALL] },
        },
      },
    });
    const unreadNotificationCounts: { [key: number]: number } = {};
    for (const un of unreadGroupByUserId) {
      unreadNotificationCounts[un.userId] = un._count._all;
    }
    const result: { [key: number]: number } = {};
    for (const userId of userIds) {
      result[userId] = unreadNotificationCounts[userId] ?? 0;
    }
    return result;
  }

  async getUnreadCountsByUser(
    user: UserDto,
    tx?: Omit<PrismaClient, ITXClientDenyList>,
  ): Promise<number> {
    return (
      (await this.getUnreadCounts([user.id], user.organizationId, tx))[
        user.id
      ] ?? 0
    );
  }

  async createNotificationContent(
    organizationId: number,
    notifications: Prisma.NotificationGetPayload<{
      include: { activity: { include: { createdUser: true } } };
    }>[],
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ): Promise<NotificationDto[]> {
    const notificationContents: NotificationDto[] = [];
    const activities = notifications.map((n) => {
      n.activity['createdUserInfo'] = plainToInstance(
        UserDataDto,
        n.activity.createdUser,
        { excludeExtraneousValues: true },
      );
      delete n.activity.createdUser;
      return n.activity;
    });
    const activityListData =
      await this.activityUtilService.getDataForActivityList(
        organizationId,
        activities,
        { tx: options?.tx },
      );
    const groups = this.utilService.groupByUserId(notifications);
    Object.entries(groups).map(async ([userId, userNotifications]) => {
      const userActivities = userNotifications.map((n) => n.activity);
      const activityList = await this.activityUtilService.getActivityList(
        +userId,
        organizationId,
        userActivities,
        { tx: options?.tx, data: activityListData },
      );
      userNotifications.map((n) => {
        notificationContents.push({
          id: n.id,
          userId: n.userId,
          isRead: n.isRead,
          content: activityList[n.activity.id].content,
          actions: activityList[n.activity.id].actions,
          createdAt: n.createdAt,
          createdUser: n.activity['createdUserInfo'],
        });
      });
    });
    return notificationContents;
  }

  async createNotifications(
    tx: Omit<PrismaClient, ITXClientDenyList>,
    user: UserDto,
    activities: Activity[],
    options?: { sendNotificationToMe?: boolean },
  ): Promise<NotificationDto[]> {
    const createdAt = new Date();
    const notificationCreateData: Prisma.NotificationCreateManyInput[] = [];
    for (const activity of activities) {
      let effectedUserIds = this.utilService.jsonToNumberList(
        activity.effectedUserIds,
      );
      if (!options?.sendNotificationToMe) {
        effectedUserIds = this.utilService.removeElementFromArr(
          user.id,
          effectedUserIds,
        );
      }
      if (effectedUserIds.length == 0) {
        continue;
      }
      effectedUserIds.map((effectedUserId) => {
        notificationCreateData.push({
          userId: effectedUserId,
          activityId: activity.id,
          createdAt: createdAt,
        });
      });
    }
    await tx.notification.createMany({
      data: notificationCreateData,
    });
    const userIds = [...new Set(notificationCreateData.map((n) => n.userId))];
    const activityIds = [
      ...new Set(notificationCreateData.map((n) => n.activityId)),
    ];
    const notifications = await tx.notification.findMany({
      where: {
        userId: { in: userIds },
        activityId: { in: activityIds },
        createdAt: createdAt,
      },
      include: { activity: { include: { createdUser: true } } },
    });
    const notificationContents = await this.createNotificationContent(
      user.organizationId,
      notifications,
      { tx: tx },
    );
    return notificationContents;
  }

  async sendNotificationGateway(
    tx: Omit<PrismaClient, ITXClientDenyList>,
    organizationId: number,
    notificationContents: NotificationDto[],
  ): Promise<void> {
    const userIds = [...new Set(notificationContents.map((n) => n.userId))];
    const unreadCounts = await this.getUnreadCounts(
      userIds,
      organizationId,
      tx,
    );
    this.notificationGateway.sendNotifications(
      organizationId,
      unreadCounts,
      notificationContents,
    );
  }

  async readNotifications(
    user: UserDto,
    dto: ReadNotificationDto,
  ): Promise<void> {
    await this.prismaService.notification.updateMany({
      where: {
        id: dto.readAll ? undefined : { in: dto.notificationIds },
        userId: user.id,
        isRead: false,
        activity: { organizationId: user.organizationId },
      },
      data: { isRead: true },
    });
    await this.syncNotificationBadges(user);
  }

  async syncNotificationBadges(
    user: UserDto,
    tx?: Omit<PrismaClient, ITXClientDenyList>,
  ): Promise<void> {
    const unreadCount = await this.getUnreadCountsByUser(user, tx);
    this.notificationGateway.syncBadge(
      user.id,
      user.organizationId,
      unreadCount,
    );
  }
}
