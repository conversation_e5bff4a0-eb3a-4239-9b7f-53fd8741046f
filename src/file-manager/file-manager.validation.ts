import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserDto } from '../common/dtos/user.dto';
import { PrismaService } from '../common/services/prisma.service';
import { DeleteFileDto } from './dtos/delete-file.dto';
import { FileManager } from '@prisma/client';
import { CreateFileDto } from './dtos/create-file.dto';
import { fileTypeFromBuffer, FileTypeResult } from 'file-type';

@Injectable()
export class FileManagerValidation {
  private readonly UPLOAD_MAX_FILE_SIZE: number;
  private readonly logger = new Logger(FileManagerValidation.name);

  constructor(
    private prismaService: PrismaService,
    private configService: ConfigService,
  ) {
    this.UPLOAD_MAX_FILE_SIZE = this.configService.getOrThrow<number>(
      'UPLOAD_MAX_FILE_SIZE',
    );
  }

  async createFile(
    user: UserDto,
    dto: CreateFileDto,
    file: Express.Multer.File,
  ): Promise<FileTypeResult> {
    const validExtensions = [
      'jpg',
      'jpeg',
      'png',
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
    ];
    const fileTypeResult = await fileTypeFromBuffer(file.buffer);
    const clientExtension = file.originalname.split('.').pop().toLowerCase();
    if (
      !fileTypeResult ||
      (fileTypeResult.ext != clientExtension &&
        !(fileTypeResult.ext == 'jpg' && clientExtension == 'jpeg'))
    ) {
      this.logger.error(
        `File extension: ${fileTypeResult?.ext} clientExtension: ${clientExtension}`,
      );
      throw new BadRequestException('Dosya uzantısı veya yapısı bozuk.');
    }
    if (!validExtensions.includes(fileTypeResult.ext)) {
      this.logger.error(
        `File extension: ${fileTypeResult?.ext} clientExtension: ${clientExtension}`,
      );
      throw new BadRequestException(
        'Geçerli dosya türleri: ' + validExtensions.join(', '),
      );
    }
    if (file.size > this.UPLOAD_MAX_FILE_SIZE) {
      throw new BadRequestException(
        `Dosya boyutu en fazla ${this.UPLOAD_MAX_FILE_SIZE / 10 ** 6} MB olabilir.`,
      );
    }
    return fileTypeResult;
  }

  async deleteFile(
    user: UserDto,
    dto: DeleteFileDto,
    isOrganization?: boolean,
  ): Promise<FileManager> {
    const fileManager = await this.prismaService.fileManager.findFirstOrThrow({
      where: {
        createdUserId: isOrganization ? undefined : user.id,
        organizationId: user.organizationId,
        url: dto.url,
      },
    });
    return fileManager;
  }
}
