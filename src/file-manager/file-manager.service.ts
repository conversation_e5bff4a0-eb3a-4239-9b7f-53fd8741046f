import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UtilService } from '../common/services/util.service';
import { DeleteObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { PrismaService } from '../common/services/prisma.service';
import { UserDto } from '../common/dtos/user.dto';
import { FileTypeEnum } from './enums/file-type.enum';
import { FileManager } from '@prisma/client';
import { DeleteFileDto } from './dtos/delete-file.dto';
import { FileManagerValidation } from './file-manager.validation';
import { CreateFileDto } from './dtos/create-file.dto';
import { FileTypeResult } from 'file-type';

@Injectable()
export class FileManagerService {
  private readonly s3Client: S3Client;
  private readonly s3Bucket: string;

  constructor(
    private prismaService: PrismaService,
    private configService: ConfigService,
    private utilService: UtilService,
    private fileManagerValidation: FileManagerValidation,
  ) {
    let endpoint = this.configService.getOrThrow<string>('AWS_ENDPOINT');
    if (!endpoint || endpoint == '') {
      endpoint = undefined;
    }
    this.s3Client = new S3Client({
      endpoint: endpoint,
      region: this.configService.getOrThrow('AWS_DEFAULT_REGION'),
      credentials: {
        accessKeyId: this.configService.getOrThrow('AWS_ACCESS_KEY_ID'),
        secretAccessKey: this.configService.getOrThrow('AWS_SECRET_ACCESS_KEY'),
      },
      forcePathStyle:
        this.configService.getOrThrow('AWS_USE_PATH_STYLE_ENDPOINT') === 'true',
    });
    this.s3Bucket = this.configService.getOrThrow('AWS_BUCKET');
  }

  private getFileTypeFromMime(extension: string): FileTypeEnum {
    if (extension == 'jpg' || extension == 'jpeg') {
      return FileTypeEnum.JPEG;
    } else if (extension == 'png') {
      return FileTypeEnum.PNG;
    } else if (extension == 'pdf') {
      return FileTypeEnum.PDF;
    } else if (extension == 'doc' || extension == 'docx') {
      return FileTypeEnum.WORD;
    } else if (extension == 'xls' || extension == 'xlsx') {
      return FileTypeEnum.EXCEL;
    } else if (extension == 'ppt' || extension == 'pptx') {
      return FileTypeEnum.POWER_POINT;
    } else {
      throw new BadRequestException('Beklenmedik dosya türü.');
    }
  }

  async getFileTypes() {
    return this.utilService.enumToArray(FileTypeEnum);
  }

  async createFile(
    user: UserDto,
    dto: CreateFileDto,
    file: Express.Multer.File,
    fileTypeResult: FileTypeResult,
  ): Promise<FileManager> {
    const fileName =
      this.utilService.getRandomString(
        this.utilService.getRandomInteger(30, 50),
      ) +
      '-' +
      `${new Date().getTime()}` +
      '.' +
      fileTypeResult.ext;
    const data = await new Upload({
      client: this.s3Client,
      params: {
        Bucket: this.s3Bucket,
        Key: fileName,
        Body: file.buffer,
        ACL: 'public-read',
        ContentType: fileTypeResult.mime,
        ContentDisposition: 'inline',
      },
    }).done();
    const fileManager = await this.prismaService.fileManager.create({
      data: {
        createdUserId: user.id,
        organizationId: user.organizationId,
        name:
          dto.name?.length > 0
            ? dto.name
            : Buffer.from(file.originalname, 'binary').toString('utf-8'),
        url: data.Location,
        type: this.getFileTypeFromMime(fileTypeResult.ext),
        size: Math.round(file.size / 1024),
      },
    });
    return fileManager;
  }

  async deleteFile(
    user: UserDto,
    dto: DeleteFileDto,
    isOrganization?: boolean,
  ): Promise<void> {
    const fileManager = await this.fileManagerValidation.deleteFile(
      user,
      dto,
      isOrganization,
    );
    await this.prismaService.fileManager.deleteMany({
      where: { id: fileManager.id },
    });
    const command = new DeleteObjectCommand({
      Bucket: this.s3Bucket,
      Key: dto.url.split('/').pop(),
    });
    await this.s3Client.send(command);
  }
}
