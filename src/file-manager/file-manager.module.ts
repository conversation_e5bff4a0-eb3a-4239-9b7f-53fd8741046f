import { Modu<PERSON> } from '@nestjs/common';
import { FileManagerController } from './file-manager.controller';
import { FileManagerService } from './file-manager.service';
import { CommonModule } from '../common/common.module';
import { FileManagerValidation } from './file-manager.validation';

@Module({
  imports: [CommonModule],
  controllers: [FileManagerController],
  providers: [FileManagerService, FileManagerValidation],
  exports: [FileManagerService],
})
export class FileManagerModule {}
