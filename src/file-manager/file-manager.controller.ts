import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { FileManagerService } from './file-manager.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { CreateFileDto } from './dtos/create-file.dto';
import { GeneralResponseDto } from '../common/dtos/general-response.dto';
import { FileManagerValidation } from './file-manager.validation';
import { UserDto } from '../common/dtos/user.dto';
import { User } from '../common/decorators/auth.decorator';
import { DeleteFileDto } from './dtos/delete-file.dto';

@ApiTags('File Managers')
@Controller('file-managers')
export class FileManagerController {
  constructor(
    private fileManagerService: FileManagerService,
    private fileManagerValidation: FileManagerValidation,
  ) {}

  @Get('types')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getFileTypes() {
    const types = await this.fileManagerService.getFileTypes();
    return new GeneralResponseDto().setData(types);
  }

  @Post()
  @ApiBearerAuth()
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({ type: CreateFileDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createFile(
    @User() user: UserDto,
    @Body() dto: CreateFileDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    const fileTypeResult = await this.fileManagerValidation.createFile(
      user,
      dto,
      file,
    );
    const fileManager = await this.fileManagerService.createFile(
      user,
      dto,
      file,
      fileTypeResult,
    );
    return new GeneralResponseDto().setData(fileManager);
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteFileDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteFile(@User() user: UserDto, @Body() dto: DeleteFileDto) {
    await this.fileManagerService.deleteFile(user, dto);
    return new GeneralResponseDto();
  }
}
