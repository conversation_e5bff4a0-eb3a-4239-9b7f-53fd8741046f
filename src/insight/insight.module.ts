import { Modu<PERSON> } from '@nestjs/common';
import { CommonModule } from '../common/common.module';
import { OpinionService } from './services/opinion.service';
import { OpinionValidation } from './validations/opinion.validation';
import { OpinionController } from './controllers/opinion.controller';
import { OrganizationModule } from '../organization/organization.module';
import { MailModule } from '../mail/mail.module';
import { FinancialController } from './controllers/financial.controller';
import { FinancialService } from './services/financial.service';
import { FinancialValidation } from './validations/financial.validation';
import { IndustryService } from './services/industry.service';
import { IndustryValidation } from './validations/industry.validation';
import { IndustryController } from './controllers/industry.controller';
import { TendencyController } from './controllers/tendency.controller';
import { TendencyService } from './services/tendency.service';
import { TendencyValidation } from './validations/tendency.validation';
import { ImpactController } from './controllers/impact.controller';
import { ImpactService } from './services/impact.service';
import { ImpactValidation } from './validations/impact.validation';
import { ActivityModule } from '../activity/activity.module';
import { InsightService } from './services/insight.service';
import { InsightController } from './controllers/insight.controller';
import { InsightValidation } from './validations/insight.validation';
import { FinancialSignificanceController } from './controllers/financial-significance.controller';
import { FinancialSignificanceService } from './services/financial-significance.service';
import { FinancialSignificanceValidation } from './validations/financial-significance.validation';
import { ImpactSignificanceController } from './controllers/impact-significance.controller';
import { ImpactSignificanceService } from './services/impact-significance.service';
import { ImpactSignificanceValidation } from './validations/impact-significance.validation';
import { AnalysisModule } from '../analysis/analysis.module';
import { MaterialityController } from './controllers/materiality.controller';
import { MaterialityService } from './services/materiality.service';
import { MaterialityValidation } from './validations/materiality.validation';

@Module({
  imports: [
    ActivityModule,
    AnalysisModule,
    MailModule,
    OrganizationModule,
    CommonModule,
  ],
  controllers: [
    FinancialController,
    FinancialSignificanceController,
    ImpactController,
    ImpactSignificanceController,
    IndustryController,
    InsightController,
    MaterialityController,
    OpinionController,
    TendencyController,
  ],
  providers: [
    FinancialService,
    FinancialValidation,
    FinancialSignificanceService,
    FinancialSignificanceValidation,
    ImpactService,
    ImpactValidation,
    ImpactSignificanceService,
    ImpactSignificanceValidation,
    IndustryService,
    IndustryValidation,
    InsightService,
    InsightValidation,
    MaterialityService,
    MaterialityValidation,
    OpinionService,
    OpinionValidation,
    TendencyService,
    TendencyValidation,
  ],
  exports: [],
})
export class InsightModule {}
