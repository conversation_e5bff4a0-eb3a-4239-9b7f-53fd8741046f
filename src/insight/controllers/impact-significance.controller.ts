import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { GetImpactSignificanceDto } from '../dtos/impact-significance/get-impact-significance.dto';
import { CreateImpactSignificanceReportDto } from '../dtos/impact-significance/create-impact-significance-report.dto';
import { ImpactSignificanceService } from '../services/impact-significance.service';
import { ImpactSignificanceValidation } from '../validations/impact-significance.validation';
import { GetCurrentImpactSignificanceDto } from '../dtos/impact-significance/get-current-impact-significance.dto';
import { GetImpactSignificanceReportDto } from '../dtos/impact-significance/get-impact-significance-report.dto';
import { GetImpactSignificanceReportUserDto } from '../dtos/impact-significance/get-impact-significance-report-user.dto';

@ApiTags('Insights/Impacts/Significances')
@Controller('insights/impacts/significances')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class ImpactSignificanceController {
  constructor(
    private impactSignificanceValidation: ImpactSignificanceValidation,
    private impactSignificanceService: ImpactSignificanceService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSignificances(
    @User() user: UserDto,
    @Query() dto: GetImpactSignificanceDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.impactSignificanceService.getSignificances(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentSignificances(
    @User() user: UserDto,
    @Query() dto: GetCurrentImpactSignificanceDto,
  ): Promise<GeneralResponseDto> {
    await this.impactSignificanceValidation.getCurrentSignificances(user, dto);
    const result = await this.impactSignificanceService.getCurrentSignificances(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetImpactSignificanceReportDto,
  ): Promise<GeneralResponseDto> {
    await this.impactSignificanceValidation.getReports(user, dto);
    const reports = await this.impactSignificanceService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetImpactSignificanceReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.impactSignificanceValidation.getReportUsers(user, dto);
    const users = await this.impactSignificanceService.getReportUsers(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(users);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(
    @User() user: UserDto,
    @Body() dto: CreateImpactSignificanceReportDto,
  ): Promise<GeneralResponseDto> {
    const report = await this.impactSignificanceValidation.createReport(
      user,
      dto,
    );
    const result = await this.impactSignificanceService.createReport(
      user,
      dto,
      report,
    );
    return new GeneralResponseDto().setData(result);
  }
}
