import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { FinancialValidation } from '../validations/financial.validation';
import { FinancialService } from '../services/financial.service';
import { GetFinancialDto } from '../dtos/financial/get-financial.dto';
import { DeleteFinancialReportDto } from '../dtos/financial/delete-financial-report.dto';
import { UpdateFinancialReportDto } from '../dtos/financial/update-financial-report.dto';
import { CreateFinancialHelpDto } from '../dtos/financial/create-financial-help.dto';
import { CreateFinancialHelpAnswerDto } from '../dtos/financial/create-financial-help-answer.dto';
import { GetFinancialReportDto } from '../dtos/financial/get-financial-report.dto';
import { GetFinancialReportUserDto } from '../dtos/financial/get-financial-report-user.dto';
import { UpdateFinancialDetailDto } from '../dtos/financial/update-financial-detail.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { GetFinancialHelpDetailDto } from '../dtos/financial/get-financial-help-detail.dto';
import { GetCurrentFinancialDto } from '../dtos/financial/get-current-financial.dto';
import { CreateFinancialReportFromOpinionDto } from '../dtos/financial/create-financial-report-from-opinion.dto';
import { UpdateFinancialPriorityDto } from '../dtos/financial/update-financial-priority.dto';
import { CreateFinancialReviewDto } from '../dtos/financial/create-financial-review.dto';
import { CreateFinancialReviewAnswerDto } from '../dtos/financial/create-financial-review-answer.dto';
import { GetFinancialReviewDetailDto } from '../dtos/financial/get-financial-review-detail.dto';
import { CreateFinancialReportFromIndustryDto } from '../dtos/financial/create-financial-report-from-industry.dto';
import { CreateFinancialReportFromTendencyDto } from '../dtos/financial/create-financial-report-from-tendency.dto';
import { ResetFinancialReportDto } from '../dtos/financial/reset-financial-report.dto';

@ApiTags('Insights/Financials')
@Controller('insights/financials')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class FinancialController {
  constructor(
    private financialValidation: FinancialValidation,
    private financialService: FinancialService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getFinancials(
    @User() user: UserDto,
    @Query() dto: GetFinancialDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.financialService.getFinancials(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentFinancials(
    @User() user: UserDto,
    @Query() dto: GetCurrentFinancialDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.getCurrentFinancials(user, dto);
    const result = await this.financialService.getCurrentFinancials(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetFinancialReportDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.getReports(user, dto);
    const reports = await this.financialService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetFinancialReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.getReportUsers(user, dto);
    const users = await this.financialService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps/detail')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getHelpDetail(
    @User() user: UserDto,
    @Query() dto: GetFinancialHelpDetailDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.financialValidation.getHelpDetail(user, dto);
    const result = await this.financialService.getHelpDetail(user, dto, help);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reviews/detail')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReviewDetail(
    @User() user: UserDto,
    @Query() dto: GetFinancialReviewDetailDto,
  ): Promise<GeneralResponseDto> {
    const review = await this.financialValidation.getReviewDetail(user, dto);
    const result = await this.financialService.getReviewDetail(
      user,
      dto,
      review,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/opinions')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportFromOpinion(
    @User() user: UserDto,
    @Body() dto: CreateFinancialReportFromOpinionDto,
  ): Promise<GeneralResponseDto> {
    const { opinionReport, opinions } =
      await this.financialValidation.createReportFromOpinion(user, dto);
    const result = await this.financialService.createReportFromOpinion(
      user,
      dto,
      opinionReport,
      opinions,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/industries')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportFromIndustry(
    @User() user: UserDto,
    @Body() dto: CreateFinancialReportFromIndustryDto,
  ): Promise<GeneralResponseDto> {
    const { industryReport, industries } =
      await this.financialValidation.createReportFromIndustry(user, dto);
    const result = await this.financialService.createReportFromIndustry(
      user,
      dto,
      industryReport,
      industries,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/tendencies')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportFromTendency(
    @User() user: UserDto,
    @Body() dto: CreateFinancialReportFromTendencyDto,
  ): Promise<GeneralResponseDto> {
    const { tendencyReport, tendencies } =
      await this.financialValidation.createReportFromTendency(user, dto);
    const result = await this.financialService.createReportFromTendency(
      user,
      dto,
      tendencyReport,
      tendencies,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelp(
    @User() user: UserDto,
    @Body() dto: CreateFinancialHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.createHelp(user, dto);
    await this.financialService.createHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Post('helps/answers')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelpAnswer(
    @User() user: UserDto,
    @Body() dto: CreateFinancialHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.financialValidation.createHelpAnswer(user, dto);
    await this.financialService.createHelpAnswer(user, dto, help);
    return new GeneralResponseDto();
  }

  @Post('reviews')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReviews(
    @User() user: UserDto,
    @Body() dto: CreateFinancialReviewDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.createReviews(user, dto);
    await this.financialService.createReviews(user, dto);
    return new GeneralResponseDto();
  }

  @Post('reviews/answers')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReviewAnswer(
    @User() user: UserDto,
    @Body() dto: CreateFinancialReviewAnswerDto,
  ): Promise<GeneralResponseDto> {
    const review = await this.financialValidation.createReviewAnswer(user, dto);
    await this.financialService.createReviewAnswer(user, dto, review);
    return new GeneralResponseDto();
  }

  @Put('details')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateFinancialDetail(
    @User() user: UserDto,
    @Body() dto: UpdateFinancialDetailDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.updateFinancialDetail(user, dto);
    await this.financialService.updateFinancialDetail(user, dto);
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateFinancialReportDto,
  ): Promise<GeneralResponseDto> {
    const report = await this.financialValidation.updateReport(user, dto);
    await this.financialService.updateReport(user, dto, report);
    return new GeneralResponseDto();
  }

  @Put('reports/reset')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async resetReport(
    @User() user: UserDto,
    @Body() dto: ResetFinancialReportDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.resetReport(user, dto);
    await this.financialService.resetReport(user, dto);
    return new GeneralResponseDto();
  }

  @Put('priorities')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updatePriority(
    @User() user: UserDto,
    @Body() dto: UpdateFinancialPriorityDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.updatePriority(user, dto);
    await this.financialService.updatePriority(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteFinancialReportDto,
  ): Promise<GeneralResponseDto> {
    await this.financialValidation.deleteReport(user, dto);
    await this.financialService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }
}
