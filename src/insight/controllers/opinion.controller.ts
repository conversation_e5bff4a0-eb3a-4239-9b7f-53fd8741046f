import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Public, User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { OpinionValidation } from '../validations/opinion.validation';
import { OpinionService } from '../services/opinion.service';
import { GetOpinionDto } from '../dtos/opinion/get-opinion.dto';
import { DeleteOpinionReportDto } from '../dtos/opinion/delete-opinion-report.dto';
import { UpdateOpinionDto } from '../dtos/opinion/update-opinion.dto';
import { DeleteOpinionDto } from '../dtos/opinion/delete-opinion.dto';
import { UpdateOpinionReportDto } from '../dtos/opinion/update-opinion-report.dto';
import { GetOpinionHelpDto } from '../dtos/opinion/get-opinion-help.dto';
import { CreateOpinionHelpDto } from '../dtos/opinion/create-opinion-help.dto';
import { CreateOpinionHelpAnswerDto } from '../dtos/opinion/create-opinion-help-answer.dto';
import { GetOpinionReportDto } from '../dtos/opinion/get-opinion-report.dto';
import { GetOpinionReportUserDto } from '../dtos/opinion/get-opinion-report-user.dto';
import { CreateOpinionDto } from '../dtos/opinion/create-opinion.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { Request } from 'express';
import { GetOpinionHelpDetailDto } from '../dtos/opinion/get-opinion-help-detail.dto';
import { GetCurrentOpinionDto } from '../dtos/opinion/get-current-opinion.dto';
import { CompleteOpinionHelpAnswerDto } from '../dtos/opinion/complete-opinion-help-answer.dto';

@ApiTags('Insights/Opinions')
@Controller('insights/opinions')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class OpinionController {
  constructor(
    private opinionValidation: OpinionValidation,
    private opinionService: OpinionService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getOpinions(
    @User() user: UserDto,
    @Query() dto: GetOpinionDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.opinionService.getOpinions(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentOpinions(
    @User() user: UserDto,
    @Query() dto: GetCurrentOpinionDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.getCurrentOpinions(user, dto);
    const result = await this.opinionService.getCurrentOpinions(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetOpinionReportDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.getReports(user, dto);
    const reports = await this.opinionService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetOpinionReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.getReportUsers(user, dto);
    const users = await this.opinionService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getHelps(
    @User() user: UserDto,
    @Query() dto: GetOpinionHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.getHelps(user, dto);
    const result = await this.opinionService.getHelps(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('helps/detail')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getHelpDetail(
    @Query() dto: GetOpinionHelpDetailDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.opinionValidation.getHelpDetail(dto);
    const opinions = await this.opinionService.getHelpDetail(dto, help);
    return new GeneralResponseDto().setData(opinions);
  }

  @Post()
  @ApiBearerAuth()
  @ApiBody({ type: CreateOpinionDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createOpinion(
    @User() user: UserDto,
    @Body() dto: CreateOpinionDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.createOpinion(user, dto);
    await this.opinionService.createOpinion(user, dto);
    return new GeneralResponseDto();
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.opinionService.createReport(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.opinionService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelp(
    @User() user: UserDto,
    @Body() dto: CreateOpinionHelpDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    const stakeholderUsers = await this.opinionValidation.createHelp(user, dto);
    await this.opinionService.createHelp(user, dto, stakeholderUsers, request);
    return new GeneralResponseDto();
  }

  @Post('helps/answers')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelpAnswer(
    @Body() dto: CreateOpinionHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.opinionValidation.createHelpAnswer(dto);
    await this.opinionService.createHelpAnswer(dto, help);
    return new GeneralResponseDto();
  }

  @Post('helps/answers/complete')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async completeHelpAnswer(
    @Body() dto: CompleteOpinionHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.opinionValidation.completeHelpAnswer(dto);
    await this.opinionService.completeHelpAnswer(dto, help);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateOpinionDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateOpinion(
    @User() user: UserDto,
    @Body() dto: UpdateOpinionDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.updateOpinion(user, dto);
    await this.opinionService.updateOpinion(user, dto);
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateOpinionReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateOpinionReportDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.updateReport(user, dto);
    await this.opinionService.updateReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteOpinionDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteOpinion(
    @User() user: UserDto,
    @Body() dto: DeleteOpinionDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.deleteOpinion(user, dto);
    await this.opinionService.deleteOpinion(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteOpinionReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteOpinionReportDto,
  ): Promise<GeneralResponseDto> {
    await this.opinionValidation.deleteReport(user, dto);
    await this.opinionService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }
}
