import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { GetCurrentInsightDto } from '../dtos/insight/get-current-insight.dto';
import { InsightService } from '../services/insight.service';
import { InsightValidation } from '../validations/insight.validation';
import { GetSignificanceGraphDto } from '../dtos/insight/get-significance-graph.dto';

@ApiTags('Insights')
@Controller('insights')
export class InsightController {
  constructor(
    private insightService: InsightService,
    private insightValidation: InsightValidation,
  ) {}

  @Get('currents')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrents(
    @User() user: UserDto,
    @Query() dto: GetCurrentInsightDto,
  ): Promise<GeneralResponseDto> {
    await this.insightValidation.getCurrents(user, dto);
    const result = await this.insightService.getCurrents(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('significances/graphs')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSignificanceGraphs(
    @User() user: UserDto,
    @Query() dto: GetSignificanceGraphDto,
  ): Promise<GeneralResponseDto> {
    await this.insightValidation.getSignificanceGraphs(user, dto);
    const result = await this.insightService.getSignificanceGraphs(user, dto);
    return new GeneralResponseDto().setData(result);
  }
}
