import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { MaterialityValidation } from '../validations/materiality.validation';
import { MaterialityService } from '../services/materiality.service';
import { CreateMaterialityReportDto } from '../dtos/materiality/create-materiality-report.dto';
import { GetMaterialityReportUserDto } from '../dtos/materiality/get-materiality-report-user.dto';
import { GetMaterialityReportDto } from '../dtos/materiality/get-materiality-report.dto';
import { GetMaterialityDto } from '../dtos/materiality/get-materiality.dto';

@ApiTags('Insights/Materialities')
@Controller('insights/materialities')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class MaterialityController {
  constructor(
    private materialityValidation: MaterialityValidation,
    private materialityService: MaterialityService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getMaterialities(
    @User() user: UserDto,
    @Query() dto: GetMaterialityDto,
  ): Promise<GeneralResponseDto> {
    await this.materialityValidation.getMaterialities(user, dto);
    const result = await this.materialityService.getMaterialities(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetMaterialityReportDto,
  ): Promise<GeneralResponseDto> {
    await this.materialityValidation.getReports(user, dto);
    const reports = await this.materialityService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetMaterialityReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.materialityValidation.getReportUsers(user, dto);
    const users = await this.materialityService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('reports/creating-availability')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportCreatingAvailability(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result =
      await this.materialityService.getReportCreatingAvailability(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(
    @User() user: UserDto,
    @Body() dto: CreateMaterialityReportDto,
  ): Promise<GeneralResponseDto> {
    const reports = await this.materialityValidation.createReport(user, dto);
    const result = await this.materialityService.createReport(
      user,
      dto,
      reports,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.materialityService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }
}
