import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { TendencyValidation } from '../validations/tendency.validation';
import { TendencyService } from '../services/tendency.service';
import { GetTendencyDto } from '../dtos/tendency/get-tendency.dto';
import { DeleteTendencyReportDto } from '../dtos/tendency/delete-tendency-report.dto';
import { UpdateTendencyDto } from '../dtos/tendency/update-tendency.dto';
import { DeleteTendencyDto } from '../dtos/tendency/delete-tendency.dto';
import { UpdateTendencyReportDto } from '../dtos/tendency/update-tendency-report.dto';
import { CreateTendencyHelpDto } from '../dtos/tendency/create-tendency-help.dto';
import { CreateTendencyHelpAnswerDto } from '../dtos/tendency/create-tendency-help-answer.dto';
import { GetTendencyReportDto } from '../dtos/tendency/get-tendency-report.dto';
import { GetTendencyReportUserDto } from '../dtos/tendency/get-tendency-report-user.dto';
import { CreateTendencyDto } from '../dtos/tendency/create-tendency.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { GetTendencyHelpDetailDto } from '../dtos/tendency/get-tendency-help-detail.dto';
import { GetCurrentTendencyDto } from '../dtos/tendency/get-current-tendency.dto';
import { CompleteTendencyHelpAnswerDto } from '../dtos/tendency/complete-tendency-help-answer.dto';

@ApiTags('Insights/Tendencies')
@Controller('insights/tendencies')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class TendencyController {
  constructor(
    private tendencyValidation: TendencyValidation,
    private tendencyService: TendencyService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getTendencies(
    @User() user: UserDto,
    @Query() dto: GetTendencyDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.tendencyService.getTendencies(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentTendencies(
    @User() user: UserDto,
    @Query() dto: GetCurrentTendencyDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.getCurrentTendencies(user, dto);
    const result = await this.tendencyService.getCurrentTendencies(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetTendencyReportDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.getReports(user, dto);
    const reports = await this.tendencyService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetTendencyReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.getReportUsers(user, dto);
    const users = await this.tendencyService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps/detail')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getHelpDetail(
    @User() user: UserDto,
    @Query() dto: GetTendencyHelpDetailDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.tendencyValidation.getHelpDetail(user, dto);
    const tendencies = await this.tendencyService.getHelpDetail(
      user,
      dto,
      help,
    );
    return new GeneralResponseDto().setData(tendencies);
  }

  @Post()
  @ApiBearerAuth()
  @ApiBody({ type: CreateTendencyDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createTendency(
    @User() user: UserDto,
    @Body() dto: CreateTendencyDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.createTendency(user, dto);
    await this.tendencyService.createTendency(user, dto);
    return new GeneralResponseDto();
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.tendencyService.createReport(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.tendencyService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelp(
    @User() user: UserDto,
    @Body() dto: CreateTendencyHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.createHelp(user, dto);
    await this.tendencyService.createHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Post('helps/answers')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelpAnswer(
    @User() user: UserDto,
    @Body() dto: CreateTendencyHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.tendencyValidation.createHelpAnswer(user, dto);
    await this.tendencyService.createHelpAnswer(user, dto, help);
    return new GeneralResponseDto();
  }

  @Post('helps/answers/complete')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async completeHelpAnswer(
    @User() user: UserDto,
    @Body() dto: CompleteTendencyHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.tendencyValidation.completeHelpAnswer(user, dto);
    await this.tendencyService.completeHelpAnswer(user, dto, help);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateTendencyDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateTendency(
    @User() user: UserDto,
    @Body() dto: UpdateTendencyDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.updateTendency(user, dto);
    await this.tendencyService.updateTendency(user, dto);
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateTendencyReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateTendencyReportDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.updateReport(user, dto);
    await this.tendencyService.updateReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteTendencyDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteTendency(
    @User() user: UserDto,
    @Body() dto: DeleteTendencyDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.deleteTendency(user, dto);
    await this.tendencyService.deleteTendency(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteTendencyReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteTendencyReportDto,
  ): Promise<GeneralResponseDto> {
    await this.tendencyValidation.deleteReport(user, dto);
    await this.tendencyService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }
}
