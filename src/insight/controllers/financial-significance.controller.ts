import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { GetFinancialSignificanceDto } from '../dtos/financial-significance/get-financial-significance.dto';
import { CreateFinancialSignificanceReportDto } from '../dtos/financial-significance/create-financial-significance-report.dto';
import { FinancialSignificanceService } from '../services/financial-significance.service';
import { FinancialSignificanceValidation } from '../validations/financial-significance.validation';
import { GetCurrentFinancialSignificanceDto } from '../dtos/financial-significance/get-current-financial-significance.dto';
import { GetFinancialSignificanceReportDto } from '../dtos/financial-significance/get-financial-significance-report.dto';
import { GetFinancialSignificanceReportUserDto } from '../dtos/financial-significance/get-financial-significance-report-user.dto';

@ApiTags('Insights/Financials/Significances')
@Controller('insights/financials/significances')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class FinancialSignificanceController {
  constructor(
    private financialSignificanceValidation: FinancialSignificanceValidation,
    private financialSignificanceService: FinancialSignificanceService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSignificances(
    @User() user: UserDto,
    @Query() dto: GetFinancialSignificanceDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.financialSignificanceService.getSignificances(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentSignificances(
    @User() user: UserDto,
    @Query() dto: GetCurrentFinancialSignificanceDto,
  ): Promise<GeneralResponseDto> {
    await this.financialSignificanceValidation.getCurrentSignificances(
      user,
      dto,
    );
    const result =
      await this.financialSignificanceService.getCurrentSignificances(
        user,
        dto,
      );
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetFinancialSignificanceReportDto,
  ): Promise<GeneralResponseDto> {
    await this.financialSignificanceValidation.getReports(user, dto);
    const reports = await this.financialSignificanceService.getReports(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetFinancialSignificanceReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.financialSignificanceValidation.getReportUsers(user, dto);
    const users = await this.financialSignificanceService.getReportUsers(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(users);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(
    @User() user: UserDto,
    @Body() dto: CreateFinancialSignificanceReportDto,
  ): Promise<GeneralResponseDto> {
    const report = await this.financialSignificanceValidation.createReport(
      user,
      dto,
    );
    const result = await this.financialSignificanceService.createReport(
      user,
      dto,
      report,
    );
    return new GeneralResponseDto().setData(result);
  }
}
