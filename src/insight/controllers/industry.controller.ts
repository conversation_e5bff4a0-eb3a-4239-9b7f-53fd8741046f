import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { IndustryValidation } from '../validations/industry.validation';
import { IndustryService } from '../services/industry.service';
import { GetIndustryDto } from '../dtos/industry/get-industry.dto';
import { DeleteIndustryReportDto } from '../dtos/industry/delete-industry-report.dto';
import { UpdateIndustryDto } from '../dtos/industry/update-industry.dto';
import { DeleteIndustryDto } from '../dtos/industry/delete-industry.dto';
import { UpdateIndustryReportDto } from '../dtos/industry/update-industry-report.dto';
import { CreateIndustryHelpDto } from '../dtos/industry/create-industry-help.dto';
import { CreateIndustryHelpAnswerDto } from '../dtos/industry/create-industry-help-answer.dto';
import { GetIndustryReportDto } from '../dtos/industry/get-industry-report.dto';
import { GetIndustryReportUserDto } from '../dtos/industry/get-industry-report-user.dto';
import { CreateIndustryDto } from '../dtos/industry/create-industry.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { GetIndustryHelpDetailDto } from '../dtos/industry/get-industry-help-detail.dto';
import { GetCurrentIndustryDto } from '../dtos/industry/get-current-industry.dto';
import { CompleteIndustryHelpAnswerDto } from '../dtos/industry/complete-industry-help-answer.dto';

@ApiTags('Insights/Industries')
@Controller('insights/industries')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class IndustryController {
  constructor(
    private industryValidation: IndustryValidation,
    private industryService: IndustryService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getIndustries(
    @User() user: UserDto,
    @Query() dto: GetIndustryDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.industryService.getIndustries(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentIndustries(
    @User() user: UserDto,
    @Query() dto: GetCurrentIndustryDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.getCurrentIndustries(user, dto);
    const result = await this.industryService.getCurrentIndustries(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetIndustryReportDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.getReports(user, dto);
    const reports = await this.industryService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetIndustryReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.getReportUsers(user, dto);
    const users = await this.industryService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps/detail')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getHelpDetail(
    @User() user: UserDto,
    @Query() dto: GetIndustryHelpDetailDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.industryValidation.getHelpDetail(user, dto);
    const industries = await this.industryService.getHelpDetail(
      user,
      dto,
      help,
    );
    return new GeneralResponseDto().setData(industries);
  }

  @Post()
  @ApiBearerAuth()
  @ApiBody({ type: CreateIndustryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createIndustry(
    @User() user: UserDto,
    @Body() dto: CreateIndustryDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.createIndustry(user, dto);
    await this.industryService.createIndustry(user, dto);
    return new GeneralResponseDto();
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.industryService.createReport(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.industryService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelp(
    @User() user: UserDto,
    @Body() dto: CreateIndustryHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.createHelp(user, dto);
    await this.industryService.createHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Post('helps/answers')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelpAnswer(
    @User() user: UserDto,
    @Body() dto: CreateIndustryHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.industryValidation.createHelpAnswer(user, dto);
    await this.industryService.createHelpAnswer(user, dto, help);
    return new GeneralResponseDto();
  }

  @Post('helps/answers/complete')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async completeHelpAnswer(
    @User() user: UserDto,
    @Body() dto: CompleteIndustryHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.industryValidation.completeHelpAnswer(user, dto);
    await this.industryService.completeHelpAnswer(user, dto, help);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateIndustryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateIndustry(
    @User() user: UserDto,
    @Body() dto: UpdateIndustryDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.updateIndustry(user, dto);
    await this.industryService.updateIndustry(user, dto);
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateIndustryReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateIndustryReportDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.updateReport(user, dto);
    await this.industryService.updateReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteIndustryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteIndustry(
    @User() user: UserDto,
    @Body() dto: DeleteIndustryDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.deleteIndustry(user, dto);
    await this.industryService.deleteIndustry(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteIndustryReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteIndustryReportDto,
  ): Promise<GeneralResponseDto> {
    await this.industryValidation.deleteReport(user, dto);
    await this.industryService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }
}
