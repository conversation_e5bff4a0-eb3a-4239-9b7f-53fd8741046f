import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { ImpactValidation } from '../validations/impact.validation';
import { ImpactService } from '../services/impact.service';
import { GetImpactDto } from '../dtos/impact/get-impact.dto';
import { DeleteImpactReportDto } from '../dtos/impact/delete-impact-report.dto';
import { UpdateImpactReportDto } from '../dtos/impact/update-impact-report.dto';
import { CreateImpactHelpDto } from '../dtos/impact/create-impact-help.dto';
import { CreateImpactHelpAnswerDto } from '../dtos/impact/create-impact-help-answer.dto';
import { GetImpactReportDto } from '../dtos/impact/get-impact-report.dto';
import { GetImpactReportUserDto } from '../dtos/impact/get-impact-report-user.dto';
import { UpdateImpactDetailDto } from '../dtos/impact/update-impact-detail.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { GetImpactHelpDetailDto } from '../dtos/impact/get-impact-help-detail.dto';
import { GetCurrentImpactDto } from '../dtos/impact/get-current-impact.dto';
import { CreateImpactReportFromOpinionDto } from '../dtos/impact/create-impact-report-from-opinion.dto';
import { UpdateImpactPriorityDto } from '../dtos/impact/update-impact-priority.dto';
import { CreateImpactReviewDto } from '../dtos/impact/create-impact-review.dto';
import { CreateImpactReviewAnswerDto } from '../dtos/impact/create-impact-review-answer.dto';
import { GetImpactReviewDetailDto } from '../dtos/impact/get-impact-review-detail.dto';
import { CreateImpactReportFromIndustryDto } from '../dtos/impact/create-impact-report-from-industry.dto';
import { CreateImpactReportFromTendencyDto } from '../dtos/impact/create-impact-report-from-tendency.dto';
import { ResetImpactReportDto } from '../dtos/impact/reset-impact-report.dto';

@ApiTags('Insights/Impacts')
@Controller('insights/impacts')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class ImpactController {
  constructor(
    private impactValidation: ImpactValidation,
    private impactService: ImpactService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getImpacts(
    @User() user: UserDto,
    @Query() dto: GetImpactDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.impactService.getImpacts(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentImpacts(
    @User() user: UserDto,
    @Query() dto: GetCurrentImpactDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.getCurrentImpacts(user, dto);
    const result = await this.impactService.getCurrentImpacts(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetImpactReportDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.getReports(user, dto);
    const reports = await this.impactService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetImpactReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.getReportUsers(user, dto);
    const users = await this.impactService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps/detail')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getHelpDetail(
    @User() user: UserDto,
    @Query() dto: GetImpactHelpDetailDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.impactValidation.getHelpDetail(user, dto);
    const result = await this.impactService.getHelpDetail(user, dto, help);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reviews/detail')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReviewDetail(
    @User() user: UserDto,
    @Query() dto: GetImpactReviewDetailDto,
  ): Promise<GeneralResponseDto> {
    const review = await this.impactValidation.getReviewDetail(user, dto);
    const result = await this.impactService.getReviewDetail(user, dto, review);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/opinions')
  @ApiBearerAuth()
  @ApiBody({ type: CreateImpactReportFromOpinionDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportFromOpinion(
    @User() user: UserDto,
    @Body() dto: CreateImpactReportFromOpinionDto,
  ): Promise<GeneralResponseDto> {
    const { opinionReport, opinions } =
      await this.impactValidation.createReportFromOpinion(user, dto);
    const result = await this.impactService.createReportFromOpinion(
      user,
      dto,
      opinionReport,
      opinions,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/industries')
  @ApiBearerAuth()
  @ApiBody({ type: CreateImpactReportFromIndustryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportFromIndustry(
    @User() user: UserDto,
    @Body() dto: CreateImpactReportFromIndustryDto,
  ): Promise<GeneralResponseDto> {
    const { industryReport, industries } =
      await this.impactValidation.createReportFromIndustry(user, dto);
    const result = await this.impactService.createReportFromIndustry(
      user,
      dto,
      industryReport,
      industries,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/tendencies')
  @ApiBearerAuth()
  @ApiBody({ type: CreateImpactReportFromTendencyDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportFromTendency(
    @User() user: UserDto,
    @Body() dto: CreateImpactReportFromTendencyDto,
  ): Promise<GeneralResponseDto> {
    const { tendencyReport, tendencies } =
      await this.impactValidation.createReportFromTendency(user, dto);
    const result = await this.impactService.createReportFromTendency(
      user,
      dto,
      tendencyReport,
      tendencies,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelp(
    @User() user: UserDto,
    @Body() dto: CreateImpactHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.createHelp(user, dto);
    await this.impactService.createHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Post('helps/answers')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelpAnswer(
    @User() user: UserDto,
    @Body() dto: CreateImpactHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.impactValidation.createHelpAnswer(user, dto);
    await this.impactService.createHelpAnswer(user, dto, help);
    return new GeneralResponseDto();
  }

  @Post('reviews')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReviews(
    @User() user: UserDto,
    @Body() dto: CreateImpactReviewDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.createReviews(user, dto);
    await this.impactService.createReviews(user, dto);
    return new GeneralResponseDto();
  }

  @Post('reviews/answers')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReviewAnswer(
    @User() user: UserDto,
    @Body() dto: CreateImpactReviewAnswerDto,
  ): Promise<GeneralResponseDto> {
    const review = await this.impactValidation.createReviewAnswer(user, dto);
    await this.impactService.createReviewAnswer(user, dto, review);
    return new GeneralResponseDto();
  }

  @Put('details')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateImpactDetailDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateImpactDetail(
    @User() user: UserDto,
    @Body() dto: UpdateImpactDetailDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.updateImpactDetail(user, dto);
    await this.impactService.updateImpactDetail(user, dto);
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateImpactReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateImpactReportDto,
  ): Promise<GeneralResponseDto> {
    const report = await this.impactValidation.updateReport(user, dto);
    await this.impactService.updateReport(user, dto, report);
    return new GeneralResponseDto();
  }

  @Put('reports/reset')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async resetReport(
    @User() user: UserDto,
    @Body() dto: ResetImpactReportDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.resetReport(user, dto);
    await this.impactService.resetReport(user, dto);
    return new GeneralResponseDto();
  }

  @Put('priorities')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updatePriority(
    @User() user: UserDto,
    @Body() dto: UpdateImpactPriorityDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.updatePriority(user, dto);
    await this.impactService.updatePriority(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteImpactReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteImpactReportDto,
  ): Promise<GeneralResponseDto> {
    await this.impactValidation.deleteReport(user, dto);
    await this.impactService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }
}
