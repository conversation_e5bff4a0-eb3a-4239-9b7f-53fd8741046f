import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetImpactDto } from '../dtos/impact/get-impact.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteImpactReportDto } from '../dtos/impact/delete-impact-report.dto';
import {
  ImpactHelp,
  ImpactReport,
  ImpactReview,
  Industry,
  IndustryReport,
  Opinion,
  OpinionReport,
  Prisma,
  PrismaClient,
  Tendency,
  TendencyReport,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateImpactReportDto } from '../dtos/impact/update-impact-report.dto';
import { CreateImpactHelpDto } from '../dtos/impact/create-impact-help.dto';
import { CreateImpactHelpAnswerDto } from '../dtos/impact/create-impact-help-answer.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { GetImpactReportDto } from '../dtos/impact/get-impact-report.dto';
import { GetImpactReportUserDto } from '../dtos/impact/get-impact-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { UpdateImpactDetailDto } from '../dtos/impact/update-impact-detail.dto';
import { GetImpactHelpDetailDto } from '../dtos/impact/get-impact-help-detail.dto';
import { GetCurrentImpactDto } from '../dtos/impact/get-current-impact.dto';
import { CreateImpactReportFromOpinionDto } from '../dtos/impact/create-impact-report-from-opinion.dto';
import { UpdateImpactPriorityDto } from '../dtos/impact/update-impact-priority.dto';
import { CreateImpactReviewDto } from '../dtos/impact/create-impact-review.dto';
import { CreateImpactReviewAnswerDto } from '../dtos/impact/create-impact-review-answer.dto';
import { GetImpactReviewDetailDto } from '../dtos/impact/get-impact-review-detail.dto';
import { CreateImpactReportFromIndustryDto } from '../dtos/impact/create-impact-report-from-industry.dto';
import { CreateImpactReportFromTendencyDto } from '../dtos/impact/create-impact-report-from-tendency.dto';
import { ImpactCategoryTypeEnum } from '../enums/impact-category-type.enum';
import { ActivityService } from '../../activity/activity.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateActivityArgType } from '../../activity/types/create-activity-arg.type';
import { ChainService } from '../../analysis/services/chain.service';
import { ImpactRealityTypeEnum } from '../enums/impact-reality-type.enum';
import { MaterialityResetEnum } from '../enums/materiality-reset.enum';
import { ResetImpactReportDto } from '../dtos/impact/reset-impact-report.dto';

@Injectable()
export class ImpactService {
  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private chainService: ChainService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getImpacts(
    user: UserDto,
    dto: GetImpactDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<{ report: any; impacts: any[] }> {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.impactReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        code: true,
        isCompleted: true,
        expiredAt: true,
        chainReport: { select: { id: true, name: true } },
        opinionReport: { select: { id: true, name: true } },
        industryReport: { select: { id: true, name: true } },
        tendencyReport: { select: { id: true, name: true } },
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          select: {
            id: true,
            status: true,
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
        },
        priorities: {
          select: {
            type: true,
            minValue: true,
            maxValue: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        reviews: {
          select: {
            id: true,
            status: true,
            senderDescription: true,
            reviewerDescription: true,
            createdAt: true,
            updatedAt: true,
            reviewerUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
        },
        significances: true,
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const impacts = await dbService.impact.findMany({
      select: {
        id: true,
        opinionId: true,
        industryId: true,
        tendencyId: true,
        focusAreas: true,
        priorityIssue: true,
        subPriorityIssue: true,
        minorPriorityIssue: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        details: {
          select: {
            id: true,
            categoryDescription: true,
            categoryType: true,
            realityType: true,
            chainIds: true,
            countryIds: true,
            createdAt: true,
            updatedAt: true,
            answers: {
              select: {
                scale: true,
                extent: true,
                reparation: true,
                possibility: true,
                help: { select: { helpedUserId: true } },
              },
            },
          },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
      },
    });
    for (const r of report.reviews) {
      r['reviewerUserInfo'] = plainToInstance(UserDataDto, r.reviewerUser, {
        excludeExtraneousValues: true,
      });
    }
    const helpedUsers = report.helps.map((h) => {
      return plainToInstance(UserDataDto, h.helpedUser, {
        excludeExtraneousValues: true,
      });
    });
    for (const imp of impacts) {
      imp['createdUserInfo'] = plainToInstance(UserDataDto, imp.createdUser, {
        excludeExtraneousValues: true,
      });
      for (const d of imp.details) {
        const answerDivider = d.answers.length == 0 ? 1 : d.answers.length;
        d['scale'] =
          d.answers.reduce((sum, a) => sum + a.scale, 0) / answerDivider;
        d['extent'] =
          d.answers.reduce((sum, a) => sum + a.extent, 0) / answerDivider;
        let resultTotal = d['scale'] + d['extent'];
        let resultDivider = 2;
        if (d.categoryType == ImpactCategoryTypeEnum.NEGATIVE) {
          d['reparation'] =
            d.answers.reduce((sum, a) => sum + a.reparation, 0) / answerDivider;
          resultTotal += d['reparation'];
          resultDivider++;
        } else {
          d['reparation'] = null;
        }
        if (d.realityType == ImpactRealityTypeEnum.POTENTIAL) {
          d['possibility'] =
            d.answers.reduce((sum, a) => sum + a.possibility, 0) /
            answerDivider;
          resultTotal += d['possibility'];
          resultDivider++;
        } else {
          d['possibility'] = null;
        }
        d['result'] = resultTotal / resultDivider;

        const answeredUserIds = d.answers.map((a) => a.help.helpedUserId);
        d['helpedUsers'] = structuredClone(helpedUsers).map((hu) => {
          hu['isAnswered'] = answeredUserIds.includes(hu.id);
          return hu;
        });
        delete d.answers;
      }
      delete imp.createdUser;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    report['answerRate'] =
      (report.helps.filter((h) => h.status).length /
        (report.helps.length == 0 ? 1 : report.helps.length)) *
      100;
    report['isHelped'] = report.helps.length > 0;
    report['hasSignificance'] = report.significances.length > 0;

    report['selfHelp'] = null;
    const selfHelp = report.helps.find((h) => h.helpedUser.id == user.id);
    if (selfHelp) {
      report['selfHelp'] = { id: selfHelp.id, status: selfHelp.status };
    }

    report['selfReview'] = null;
    const selfReview = report.reviews.find((r) => r.reviewerUser.id == user.id);
    if (selfReview) {
      report['selfReview'] = { id: selfReview.id, status: selfReview.status };
    }

    if (report.opinionReport) {
      report.opinionReport['isExist'] = impacts.some((imp) => imp.opinionId);
    }
    if (report.industryReport) {
      report.industryReport['isExist'] = impacts.some((imp) => imp.industryId);
    }
    if (report.tendencyReport) {
      report.tendencyReport['isExist'] = impacts.some((imp) => imp.tendencyId);
    }

    delete report.createdUser;
    delete report.helps;
    delete report.significances;
    report.reviews.map((r) => delete r.reviewerUser);
    return { report: report, impacts: impacts };
  }

  async getCurrentImpacts(
    user: UserDto,
    dto: GetCurrentImpactDto,
  ): Promise<{ report: any; impacts: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let impactReport: ImpactReport;
    if (dto.reportId) {
      impactReport = await this.prismaService.impactReport.findFirstOrThrow({
        where: {
          id: dto.reportId,
          organizationId: dto.subsidiaryId,
          isCompleted: true,
          significances: { some: {} },
        },
      });
    } else {
      impactReport = await this.getLastReport(dto.subsidiaryId, {
        hasSignificance: true,
      });
      if (!impactReport) {
        return { report: null, impacts: [] };
      }
    }
    const { report, impacts } = await this.getImpacts(user, {
      reportId: impactReport.id,
    });
    return {
      report: report,
      impacts: impacts,
    };
  }

  async getReports(user: UserDto, dto: GetImpactReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.impactReport.findMany({
      select: {
        id: true,
        name: true,
        code: true,
        isCompleted: true,
        expiredAt: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        significances: true,
      },
      where: {
        organizationId: dto.subsidiaryId,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
        significances:
          dto.hasSignificance === undefined
            ? undefined
            : dto.hasSignificance === true
              ? { some: {} }
              : { none: {} },
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      r['hasSignificance'] = r.significances.length > 0;
      delete r.createdUser;
      delete r.significances;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetImpactReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        createdImpactReports: {
          some: { id: dto.reportId, organizationId: dto.subsidiaryId },
        },
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetImpactHelpDetailDto,
    help: ImpactHelp,
  ): Promise<{ report: any; help: any; impacts: any[] }> {
    const report = await this.prismaService.impactReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        expiredAt: true,
        chainReport: { select: { id: true, name: true } },
      },
      where: { id: help.reportId },
    });
    const impacts = await this.prismaService.impact.findMany({
      select: {
        id: true,
        opinionId: true,
        industryId: true,
        tendencyId: true,
        focusAreas: true,
        priorityIssue: true,
        subPriorityIssue: true,
        minorPriorityIssue: true,
        description: true,
        details: {
          select: {
            id: true,
            categoryDescription: true,
            categoryType: true,
            realityType: true,
            chainIds: true,
            countryIds: true,
            answers: {
              select: {
                scale: true,
                extent: true,
                reparation: true,
                possibility: true,
                createdAt: true,
              },
              where: { helpId: help.id },
            },
          },
        },
      },
      where: {
        report: { id: help.reportId },
      },
    });
    for (const imp of impacts) {
      for (const d of imp.details) {
        d['answer'] = d.answers.length > 0 ? d.answers[0] : null;
        delete d.answers;
      }
    }
    return {
      report: report,
      help: { id: help.id, status: help.status },
      impacts: impacts,
    };
  }

  async getReviewDetail(
    user: UserDto,
    dto: GetImpactReviewDetailDto,
    review: ImpactReview,
  ) {
    const { report, impacts } = await this.getImpacts(user, {
      reportId: review.reportId,
    });
    const createdUser = await this.prismaService.user.findFirstOrThrow({
      where: { id: review.createdUserId },
    });
    const createdUserInfo = plainToInstance(UserDataDto, createdUser, {
      excludeExtraneousValues: true,
    });
    return {
      review: {
        id: review.id,
        status: review.status,
        senderDescription: review.senderDescription,
        createdUserInfo: createdUserInfo,
        createdAt: review.createdAt,
      },
      report: report,
      impacts: impacts,
    };
  }

  private async getLastReport(
    organizationId: number,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      code?: number;
      isCompleted?: boolean;
      hasSignificance?: boolean;
    },
  ): Promise<ImpactReport> {
    const dbService = options?.tx ?? this.prismaService;
    const report = await dbService.impactReport.findFirst({
      where: {
        organizationId: organizationId,
        code: options?.code,
        isCompleted: options?.isCompleted,
        significances:
          options?.hasSignificance === undefined
            ? undefined
            : options?.hasSignificance === true
              ? { some: {} }
              : { none: {} },
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createReport(
    user: UserDto,
    tx: Omit<PrismaClient, ITXClientDenyList>,
    args: {
      opinionReport?: OpinionReport;
      industryReport?: IndustryReport;
      tendencyReport?: TendencyReport;
    },
  ): Promise<ImpactReport> {
    const argValues = Object.values(args).filter((id) => id);
    if (argValues.length != 1) {
      throw new BadRequestException('Rapor türetirken bir sorun oluştu.');
    }
    let code: number;
    if (args.opinionReport) {
      code = args.opinionReport.code;
    } else if (args.industryReport) {
      code = args.industryReport.code;
    } else if (args.tendencyReport) {
      code = args.tendencyReport.code;
    } else {
      throw new BadRequestException('Geçersiz türetilen rapor tipi.');
    }
    const chainReport = await this.chainService.getLastReport(
      user.organizationId,
      { isCompleted: true },
    );
    if (!chainReport) {
      throw new BadRequestException(
        'Öncelikle bir değer zinciri raporu tamamlamalısınız.',
      );
    }
    const lastUncompletedReport = await this.getLastReport(
      user.organizationId,
      {
        tx: tx,
        code: code,
        isCompleted: false,
      },
    );
    if (lastUncompletedReport) {
      if (
        (args.opinionReport && lastUncompletedReport.opinionReportId) ||
        (args.industryReport && lastUncompletedReport.industryReportId) ||
        (args.tendencyReport && lastUncompletedReport.tendencyReportId)
      ) {
        throw new BadRequestException(
          `${code} numaralı etki analizi raporu zaten oluşturulmuş.`,
        );
      }
      await tx.impactReport.update({
        where: {
          id: lastUncompletedReport.id,
          organizationId: user.organizationId,
        },
        data: {
          opinionReportId: args.opinionReport?.id,
          industryReportId: args.industryReport?.id,
          tendencyReportId: args.tendencyReport?.id,
        },
      });
      return lastUncompletedReport;
    }
    const reportIsCompleted = await this.getLastReport(user.organizationId, {
      tx: tx,
      code: code,
      isCompleted: true,
    });
    if (reportIsCompleted) {
      throw new BadRequestException(
        `${code} numaralı etki analizi raporu tamamlanmış.`,
      );
    }
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const createdReport = await tx.impactReport.create({
      data: {
        createdUserId: user.id,
        organizationId: user.organizationId,
        name: `EA_${code}_${dateFormat}`,
        code: code,
        chainReportId: chainReport.id,
        opinionReportId: args.opinionReport?.id,
        industryReportId: args.industryReport?.id,
        tendencyReportId: args.tendencyReport?.id,
      },
    });
    const effectedUserIds =
      await this.organizationUtilService.getOrganizationUserIds(user, {
        tx: tx,
      });
    await this.activityService.createActivity(tx, user, [
      {
        activityType: ActivityEnum.IMPACT_REPORT_CREATE,
        effectedUserIds: effectedUserIds,
        payload: { reportId: createdReport.id },
      },
    ]);
    return createdReport;
  }

  async createReportFromOpinion(
    user: UserDto,
    dto: CreateImpactReportFromOpinionDto,
    opinionReport: OpinionReport,
    opinions: Opinion[],
  ): Promise<ImpactReport> {
    const report = await this.prismaService.$transaction(async (tx) => {
      const report = await this.createReport(user, tx, {
        opinionReport: opinionReport,
      });
      await tx.impact.createMany({
        data: opinions.map(
          (op) =>
            ({
              createdUserId: user.id,
              reportId: report.id,
              opinionId: op.id,
              focusAreas: [op.focusArea],
              priorityIssue: op.priorityIssue,
              subPriorityIssue: op.subPriorityIssue,
              minorPriorityIssue: op.minorPriorityIssue,
              description: op.description,
            }) as Prisma.ImpactCreateManyInput,
        ),
      });
      return report;
    });
    return report;
  }

  async createReportFromIndustry(
    user: UserDto,
    dto: CreateImpactReportFromIndustryDto,
    industryReport: IndustryReport,
    industries: Industry[],
  ): Promise<ImpactReport> {
    const report = await this.prismaService.$transaction(async (tx) => {
      const report = await this.createReport(user, tx, {
        industryReport: industryReport,
      });
      await tx.impact.createMany({
        data: industries.map(
          (ind) =>
            ({
              createdUserId: user.id,
              reportId: report.id,
              industryId: ind.id,
              // focusAreas: [ind.focusArea],
              // priorityIssue: ind.priorityIssue,
              // subPriorityIssue: ind.priorityIssue,
              description: ind.description,
            }) as Prisma.ImpactCreateManyInput,
        ),
      });
      return report;
    });
    return report;
  }

  async createReportFromTendency(
    user: UserDto,
    dto: CreateImpactReportFromTendencyDto,
    tendencyReport: TendencyReport,
    tendencies: Tendency[],
  ): Promise<ImpactReport> {
    const report = await this.prismaService.$transaction(async (tx) => {
      const report = await this.createReport(user, tx, {
        tendencyReport: tendencyReport,
      });
      await tx.impact.createMany({
        data: tendencies.map(
          (t) =>
            ({
              createdUserId: user.id,
              reportId: report.id,
              tendencyId: t.id,
              focusAreas: [t.focusArea],
              priorityIssue: t.topic,
              subPriorityIssue: t.topic,
              description: t.description,
            }) as Prisma.ImpactCreateManyInput,
        ),
      });
      return report;
    });
    return report;
  }

  async createHelp(user: UserDto, dto: CreateImpactHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.impactHelp.createMany({
        data: dto.userIds.map(
          (helpedUserId) =>
            ({
              createdUserId: user.id,
              helpedUserId: helpedUserId,
              reportId: dto.reportId,
            }) as Prisma.ImpactHelpCreateManyInput,
        ),
      });
      const helps = await tx.impactHelp.findMany({
        where: { reportId: dto.reportId },
      });
      const activityArgs: CreateActivityArgType[] = [];
      for (const help of helps) {
        activityArgs.push({
          activityType: ActivityEnum.IMPACT_HELP,
          effectedUserIds: [help.helpedUserId],
          payload: { helpId: help.id },
        });
      }
      await this.activityService.createActivity(tx, user, activityArgs, {
        sendNotificationToMe: true,
      });
    });
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateImpactHelpAnswerDto,
    help: ImpactHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.impactHelp.update({
        where: { id: help.id },
        data: { status: HelpStatusEnum.APPROVED },
      });
      await tx.impactHelpAnswer.createMany({
        data: dto.answers.map(
          (a) =>
            ({
              helpId: help.id,
              detailId: a.detailId,
              scale: a.scale,
              extent: a.extent,
              reparation: a.reparation,
              possibility: a.possibility,
            }) as Prisma.ImpactHelpAnswerCreateManyInput,
        ),
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.IMPACT_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async createReviews(user: UserDto, dto: CreateImpactReviewDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.impactReview.createMany({
        data: dto.reviews.map(
          (r) =>
            ({
              createdUserId: user.id,
              reportId: dto.reportId,
              reviewerUserId: r.reviewerUserId,
              senderDescription: r.description,
            }) as Prisma.ImpactReviewCreateManyInput,
        ),
      });
      const reviews = await tx.impactReview.findMany({
        where: { reportId: dto.reportId },
      });
      const activityArgs: CreateActivityArgType[] = [];
      for (const review of reviews) {
        activityArgs.push({
          activityType: ActivityEnum.IMPACT_REVIEW,
          effectedUserIds: [review.reviewerUserId],
          payload: { reviewId: review.id },
        });
      }
      await this.activityService.createActivity(tx, user, activityArgs, {
        sendNotificationToMe: true,
      });
    });
  }

  async createReviewAnswer(
    user: UserDto,
    dto: CreateImpactReviewAnswerDto,
    review: ImpactReview,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.impactReview.update({
        where: { id: review.id },
        data: { status: dto.status, reviewerDescription: dto.description },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.IMPACT_REVIEW_ANSWER,
          effectedUserIds: [review.createdUserId],
          payload: { reviewId: review.id },
        },
      ]);
    });
  }

  async updateImpactDetail(user: UserDto, dto: UpdateImpactDetailDto) {
    const createDetails = dto.details.filter((d) => !d.detailId);
    const updateDetails = dto.details.filter((d) => d.detailId && !d.isDelete);
    const deleteDetailIds = dto.details
      .filter((d) => d.detailId && d.isDelete)
      .map((d) => d.detailId);
    await this.prismaService.$transaction(async (tx) => {
      await tx.impactDetail.createMany({
        data: createDetails.map(
          (d) =>
            ({
              createdUserId: user.id,
              impactId: d.impactId,
              categoryDescription: d.categoryDescription,
              categoryType: d.categoryType,
              realityType: d.realityType,
              chainIds: d.chainIds,
              countryIds: d.countryIds,
            }) as Prisma.ImpactDetailCreateManyInput,
        ),
      });
      for (const updateDetail of updateDetails) {
        await tx.impactDetail.update({
          where: { id: updateDetail.detailId },
          data: {
            categoryDescription: updateDetail.categoryDescription,
            categoryType: updateDetail.categoryType,
            realityType: updateDetail.realityType,
            chainIds: updateDetail.chainIds,
            countryIds: updateDetail.countryIds,
          },
        });
      }
      await tx.impactDetail.deleteMany({
        where: { id: { in: deleteDetailIds } },
      });
    });
  }

  async updateReport(
    user: UserDto,
    dto: UpdateImpactReportDto,
    report: ImpactReport,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.impactReport.update({
        where: { id: dto.reportId },
        data: {
          chainReportId: dto.chainReportId,
          isCompleted: dto.isCompleted ? true : undefined,
          expiredAt: dto.expiredAt,
        },
      });
      if (dto.chainReportId && dto.chainReportId != report.chainReportId) {
        await tx.impactDetail.updateMany({
          where: { impact: { reportId: dto.reportId } },
          data: { chainIds: Prisma.JsonNull },
        });
      }
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.IMPACT_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
    });
  }

  async resetReport(user: UserDto, dto: ResetImpactReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.impactReview.deleteMany({
        where: { reportId: dto.reportId },
      });
      if (dto.resetType == MaterialityResetEnum.PRIORITY_RESET) {
        await tx.impactPriority.deleteMany({
          where: { reportId: dto.reportId },
        });
      } else if (dto.resetType == MaterialityResetEnum.HELP_RESET) {
        await tx.impactHelpAnswer.deleteMany({
          where: { help: { reportId: dto.reportId } },
        });
        await tx.impactHelp.deleteMany({
          where: { reportId: dto.reportId },
        });
        await tx.impactReport.update({
          where: { id: dto.reportId },
          data: { isCompleted: false },
        });
      }
    });
  }

  async updatePriority(user: UserDto, dto: UpdateImpactPriorityDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.impactPriority.deleteMany({
        where: {
          reportId: dto.reportId,
          type: { notIn: dto.priorities.map((p) => p.type) },
        },
      });
      for (const p of dto.priorities) {
        const priority = await tx.impactPriority.findFirst({
          where: { reportId: dto.reportId, type: p.type },
        });
        await tx.impactPriority.upsert({
          where: { id: priority?.id ?? 0 },
          create: {
            createdUserId: user.id,
            reportId: dto.reportId,
            minValue: p.minValue,
            maxValue: p.maxValue,
            type: p.type,
          },
          update: {
            createdUserId: user.id,
            minValue: p.minValue,
            maxValue: p.maxValue,
          },
        });
      }
    });
  }

  async deleteReport(user: UserDto, dto: DeleteImpactReportDto) {
    await this.prismaService.impactReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }
}
