import { Injectable } from '@nestjs/common';
import { UserDto } from '../../common/dtos/user.dto';
import { CurrentTabEnum } from '../enums/current-tab.enum';
import { GetCurrentInsightDto } from '../dtos/insight/get-current-insight.dto';
import { FinancialService } from './financial.service';
import { ImpactService } from './impact.service';
import { IndustryService } from './industry.service';
import { OpinionService } from './opinion.service';
import { TendencyService } from './tendency.service';
import { PrismaService } from '../../common/services/prisma.service';
import { GetSignificanceGraphDto } from '../dtos/insight/get-significance-graph.dto';
import { UtilService } from '../../common/services/util.service';

@Injectable()
export class InsightService {
  constructor(
    private prismaService: PrismaService,
    private financialService: FinancialService,
    private impactService: ImpactService,
    private industryService: IndustryService,
    private opinionService: OpinionService,
    private tendencyService: TendencyService,
    private utilService: UtilService,
  ) {}

  async getCurrents(user: UserDto, dto: GetCurrentInsightDto) {
    let reports: any[];
    let currents: any[];
    if (!dto.currentTab || dto.currentTab == CurrentTabEnum.TENDENCY) {
      reports = await this.tendencyService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        isCompleted: true,
      });
      const { tendencies } = await this.tendencyService.getCurrentTendencies(
        user,
        {
          subsidiaryId: dto.subsidiaryId,
          reportId: dto.reportId,
        },
      );
      currents = tendencies;
    } else if (dto.currentTab == CurrentTabEnum.FINANCIAL) {
      reports = await this.financialService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        isCompleted: true,
      });
      const { financials } = await this.financialService.getCurrentFinancials(
        user,
        {
          subsidiaryId: dto.subsidiaryId,
          reportId: dto.reportId,
        },
      );
      currents = financials;
    } else if (dto.currentTab == CurrentTabEnum.IMPACT) {
      reports = await this.impactService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        isCompleted: true,
      });
      const { impacts } = await this.impactService.getCurrentImpacts(user, {
        subsidiaryId: dto.subsidiaryId,
        reportId: dto.reportId,
      });
      currents = impacts;
    } else if (dto.currentTab == CurrentTabEnum.INDUSTRY) {
      reports = await this.industryService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        isCompleted: true,
      });
      const { industries } = await this.industryService.getCurrentIndustries(
        user,
        {
          subsidiaryId: dto.subsidiaryId,
          reportId: dto.reportId,
        },
      );
      currents = industries;
    } else if (dto.currentTab == CurrentTabEnum.OPINION) {
      reports = await this.opinionService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        isCompleted: true,
      });
      const { opinions } = await this.opinionService.getCurrentOpinions(user, {
        subsidiaryId: dto.subsidiaryId,
        reportId: dto.reportId,
      });
      currents = opinions;
    }
    reports = reports
      .sort((r1, r2) => r2.createdAt - r1.createdAt)
      .map((r) => {
        return {
          id: r.id,
          name: r.name,
          financialThreshold: r.financialThreshold,
          impactThreshold: r.impactThreshold,
        };
      });
    return { reports: reports, currents: currents };
  }

  async getSignificanceGraphs(user: UserDto, dto: GetSignificanceGraphDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const financials = await this.prismaService.financial.findMany({
      where: {
        report: {
          id: dto.financialReportId,
          organizationId: dto.subsidiaryId,
          significances: { some: {} },
        },
      },
      include: { details: { include: { answers: true } } },
    });
    const impacts = await this.prismaService.impact.findMany({
      where: {
        report: {
          id: dto.impactReportId,
          organizationId: dto.subsidiaryId,
          significances: { some: {} },
        },
      },
      include: { details: { include: { answers: true } } },
    });
    const result: {
      [key: string]: {
        name: string;
        financial?: {
          totalPoint: number;
          numberPoint: number;
          stringPoint: string;
          divider: number;
        };
        impact?: {
          totalPoint: number;
          numberPoint: number;
          stringPoint: string;
          divider: number;
        };
      };
    } = {};
    for (const f of financials) {
      const detailsData = f.details.map((detail) => {
        const answerSum = detail.answers.reduce(
          (answerSum, answer) =>
            answerSum + (answer.severity + answer.possibility) / 2,
          0,
        );
        return {
          answerSum: answerSum,
          answerLength: detail.answers.length,
        };
      });
      const answerSum = detailsData.reduce(
        (sum, data) => sum + data.answerSum,
        0,
      );
      const answerLength = detailsData.reduce(
        (sum, data) => sum + data.answerLength,
        0,
      );
      if (result[f.subPriorityIssue]?.financial?.totalPoint) {
        result[f.subPriorityIssue].financial.totalPoint += answerSum;
        result[f.subPriorityIssue].financial.divider += answerLength;
      } else {
        result[f.subPriorityIssue] = {
          name: f.subPriorityIssue,
          financial: {
            totalPoint: answerSum,
            numberPoint: 0,
            stringPoint: '',
            divider: answerLength,
          },
        };
      }
    }
    for (const imp of impacts) {
      const detailsData = imp.details.map((detail) => {
        const answerSum = detail.answers.reduce((answerSum, answer) => {
          let totalAnswer = answer.scale + answer.extent;
          let dividerAnswer = 2;
          if (answer.reparation) {
            totalAnswer += answer.reparation;
            dividerAnswer++;
          }
          if (answer.possibility) {
            totalAnswer += answer.possibility;
            dividerAnswer++;
          }
          return answerSum + totalAnswer / dividerAnswer;
        }, 0);
        return {
          answerSum: answerSum,
          answerLength: detail.answers.length,
        };
      });
      const answerSum = detailsData.reduce(
        (sum, data) => sum + data.answerSum,
        0,
      );
      const answerLength = detailsData.reduce(
        (sum, data) => sum + data.answerLength,
        0,
      );
      if (result[imp.subPriorityIssue]?.impact?.totalPoint) {
        result[imp.subPriorityIssue].impact.totalPoint += answerSum;
        result[imp.subPriorityIssue].impact.divider += answerLength;
      } else if (result[imp.subPriorityIssue]) {
        result[imp.subPriorityIssue].impact = {
          totalPoint: answerSum,
          numberPoint: 0,
          stringPoint: '',
          divider: answerLength,
        };
      } else {
        result[imp.subPriorityIssue] = {
          name: imp.subPriorityIssue,
          impact: {
            totalPoint: answerSum,
            numberPoint: 0,
            stringPoint: '',
            divider: answerLength,
          },
        };
      }
    }
    const resultValues = Object.values(result).map((r) => {
      if (r.financial) {
        r.financial.numberPoint =
          r.financial.totalPoint / (r.financial.divider || 1);
        r.financial.stringPoint = this.utilService.numberFormat(
          r.financial.numberPoint,
          2,
        );
        delete r.financial.totalPoint;
        delete r.financial.divider;
      }
      if (r.impact) {
        r.impact.numberPoint = r.impact.totalPoint / (r.impact.divider || 1);
        r.impact.stringPoint = this.utilService.numberFormat(
          r.impact.numberPoint,
          2,
        );
        delete r.impact.totalPoint;
        delete r.impact.divider;
      }
      return r;
    });
    const financialPriorities =
      await this.prismaService.financialPriority.findMany({
        select: { minValue: true, maxValue: true, type: true },
        where: {
          report: {
            id: dto.financialReportId,
            organizationId: dto.subsidiaryId,
          },
        },
      });
    const impactPriorities = await this.prismaService.impactPriority.findMany({
      select: { minValue: true, maxValue: true, type: true },
      where: {
        report: { id: dto.impactReportId, organizationId: dto.subsidiaryId },
      },
    });
    return {
      financialPriorities: financialPriorities,
      impactPriorities: impactPriorities,
      values: resultValues,
    };
  }
}
