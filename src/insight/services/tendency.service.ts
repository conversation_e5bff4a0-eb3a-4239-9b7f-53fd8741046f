import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetTendencyDto } from '../dtos/tendency/get-tendency.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteTendencyReportDto } from '../dtos/tendency/delete-tendency-report.dto';
import {
  TendencyReport,
  TendencyHelp,
  PrismaClient,
  Prisma,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateTendencyDto } from '../dtos/tendency/update-tendency.dto';
import { DeleteTendencyDto } from '../dtos/tendency/delete-tendency.dto';
import { UpdateTendencyReportDto } from '../dtos/tendency/update-tendency-report.dto';
import { CreateTendencyHelpDto } from '../dtos/tendency/create-tendency-help.dto';
import { CreateTendencyHelpAnswerDto } from '../dtos/tendency/create-tendency-help-answer.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { GetTendencyReportDto } from '../dtos/tendency/get-tendency-report.dto';
import { GetTendencyReportUserDto } from '../dtos/tendency/get-tendency-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { CreateTendencyDto } from '../dtos/tendency/create-tendency.dto';
import { GetTendencyHelpDetailDto } from '../dtos/tendency/get-tendency-help-detail.dto';
import { GetCurrentTendencyDto } from '../dtos/tendency/get-current-tendency.dto';
import { ActivityService } from '../../activity/activity.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateActivityArgType } from '../../activity/types/create-activity-arg.type';
import { CompleteTendencyHelpAnswerDto } from '../dtos/tendency/complete-tendency-help-answer.dto';

@Injectable()
export class TendencyService {
  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getTendencies(
    user: UserDto,
    dto: GetTendencyDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<{ report: any; tendencies: any[] }> {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.tendencyReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        financialThreshold: true,
        impactThreshold: true,
        isCompleted: true,
        isReady: true,
        expiredAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          select: {
            id: true,
            isFinancial: true,
            isImpact: true,
            financialStatus: true,
            impactStatus: true,
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
        },
        financialReports: { select: { id: true, name: true } },
        impactReports: { select: { id: true, name: true } },
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const isSelected = report.isCompleted || report.helps.length > 0;
    const tendencies = await dbService.tendency.findMany({
      select: {
        id: true,
        focusArea: true,
        topic: true,
        source: true,
        description: true,
        isSelected: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helpAnswers: { include: { help: true } },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
        isSelected: isSelected ? true : undefined,
      },
    });
    const helpedUsers = report.helps.map((h) => {
      const helpedUser = plainToInstance(UserDataDto, h.helpedUser, {
        excludeExtraneousValues: true,
      });
      helpedUser['isFinancial'] = h.isFinancial;
      helpedUser['isImpact'] = h.isImpact;
      return helpedUser;
    });
    for (const t of tendencies) {
      t['createdUserInfo'] = plainToInstance(UserDataDto, t.createdUser, {
        excludeExtraneousValues: true,
      });
      const financialAnswers = t.helpAnswers.filter(
        (a) =>
          a.help.isFinancial &&
          a.help.financialStatus &&
          a.financialQuestionAnswer,
      );
      const impactAnswers = t.helpAnswers.filter(
        (a) => a.help.isImpact && a.help.impactStatus && a.impactQuestionAnswer,
      );
      t['financialPoint'] =
        financialAnswers.reduce(
          (sum, answer) => sum + answer.financialQuestionAnswer,
          0,
        ) / (financialAnswers.length == 0 ? 1 : financialAnswers.length);
      t['impactPoint'] =
        impactAnswers.reduce(
          (sum, answer) => sum + answer.impactQuestionAnswer,
          0,
        ) / (impactAnswers.length == 0 ? 1 : impactAnswers.length);

      const financialAnsweredUserIds = financialAnswers.map(
        (a) => a.help.helpedUserId,
      );
      const impactAnsweredUserIds = impactAnswers.map(
        (a) => a.help.helpedUserId,
      );
      t['helpedUsers'] = structuredClone(helpedUsers).map((hu) => {
        hu['isFinancialAnswered'] = financialAnsweredUserIds.includes(hu.id);
        hu['isImpactAnswered'] = impactAnsweredUserIds.includes(hu.id);
        return hu;
      });
      delete t.createdUser;
      delete t.helpAnswers;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    report['answerRate'] =
      (report.helps.filter((h) => h.financialStatus || h.impactStatus).length /
        (report.helps.length == 0 ? 1 : report.helps.length)) *
      100;
    report['isFinancialHelped'] =
      report.helps.filter((h) => h.isFinancial).length > 0;
    report['isImpactHelped'] =
      report.helps.filter((h) => h.isImpact).length > 0;

    report['selfHelp'] = null;
    const selfHelp = report.helps.find((h) => h.helpedUser.id == user.id);
    if (selfHelp) {
      report['selfHelp'] = {
        id: selfHelp.id,
        isFinancial: selfHelp.isFinancial,
        isImpact: selfHelp.isImpact,
        financialStatus: selfHelp.financialStatus,
        impactStatus: selfHelp.impactStatus,
      };
    }

    delete report.createdUser;
    delete report.helps;
    return { report: report, tendencies: tendencies };
  }

  async getCurrentTendencies(
    user: UserDto,
    dto: GetCurrentTendencyDto,
  ): Promise<{ report: any; tendencies: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let tendencyReport: TendencyReport;
    if (dto.reportId) {
      tendencyReport = await this.prismaService.tendencyReport.findFirstOrThrow(
        {
          where: {
            id: dto.reportId,
            organizationId: dto.subsidiaryId,
            isReady: true,
          },
        },
      );
    } else {
      tendencyReport = await this.getLastReport(dto.subsidiaryId, {
        isReady: true,
      });
      if (!tendencyReport) {
        return { report: null, tendencies: [] };
      }
    }
    const { report, tendencies } = await this.getTendencies(user, {
      reportId: tendencyReport.id,
    });
    return {
      report: report,
      tendencies: tendencies,
    };
  }

  async getReports(user: UserDto, dto: GetTendencyReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.tendencyReport.findMany({
      select: {
        id: true,
        name: true,
        financialThreshold: true,
        impactThreshold: true,
        isCompleted: true,
        isReady: true,
        expiredAt: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
      },
      where: {
        organizationId: dto.subsidiaryId,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
        isReady: dto.isReady === undefined ? undefined : dto.isReady,
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      delete r.createdUser;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetTendencyReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        createdTendencyReports: {
          some: { id: dto.reportId, organizationId: dto.subsidiaryId },
        },
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetTendencyHelpDetailDto,
    help: TendencyHelp,
  ): Promise<{ report: any; help: any; tendencies: any[] }> {
    const report = await this.prismaService.tendencyReport.findFirstOrThrow({
      select: { id: true, name: true, expiredAt: true },
      where: { id: help.reportId },
    });
    const tendencies = await this.prismaService.tendency.findMany({
      select: {
        id: true,
        focusArea: true,
        topic: true,
        source: true,
        description: true,
        helpAnswers: {
          select: {
            financialQuestionAnswer: true,
            impactQuestionAnswer: true,
            createdAt: true,
          },
          where: { helpId: help.id },
        },
      },
      where: {
        isSelected: true,
        report: { id: help.reportId },
      },
    });
    for (const t of tendencies) {
      t['helpAnswer'] = t.helpAnswers.length > 0 ? t.helpAnswers[0] : null;
      delete t.helpAnswers;
    }
    return {
      report: report,
      help: {
        id: help.id,
        financialStatus: help.financialStatus,
        impactStatus: help.impactStatus,
        isFinancial: help.isFinancial,
        isImpact: help.isImpact,
      },
      tendencies: tendencies,
    };
  }

  private async getLastReport(
    organizationId: number,
    options?: { isCompleted?: boolean; isReady?: boolean },
  ): Promise<TendencyReport> {
    const report = await this.prismaService.tendencyReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
        isReady: options?.isReady,
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createTendency(user: UserDto, dto: CreateTendencyDto) {
    await this.prismaService.tendency.create({
      data: {
        createdUserId: user.id,
        reportId: dto.reportId,
        focusArea: dto.focusArea,
        topic: dto.topic,
        source: dto.source,
        description: dto.description,
      },
    });
  }

  async createReport(
    user: UserDto,
  ): Promise<{ report: any; tendencies: any[] }> {
    const maxCodeResult = await this.prismaService.tendencyReport.aggregate({
      _max: { code: true },
      where: { organizationId: user.organizationId },
    });
    const code = (maxCodeResult._max.code ?? 0) + 1;
    const defaults = await this.prismaService.tendencyDefault.findMany();
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const createdReport = await tx.tendencyReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          name: `TA_${code}_${dateFormat}`,
          code: code,
        },
      });
      await tx.tendency.createMany({
        data: defaults.map(
          (d) =>
            ({
              createdUserId: user.id,
              reportId: createdReport.id,
              focusArea: d.focusArea,
              topic: d.topic,
              source: d.source,
              description: d.description,
              isDefault: true,
            }) as Prisma.TendencyCreateManyInput,
        ),
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.TENDENCY_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: createdReport.id },
        },
      ]);
      const { report, tendencies } = await this.getTendencies(
        user,
        { reportId: createdReport.id },
        { tx: tx },
      );
      return {
        report: report,
        tendencies: tendencies,
      };
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: any; tendencies: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      isReady: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        isReady: false,
      });
      if (!lastReport) {
        return await this.createReport(user);
      }
    }
    const { report, tendencies } = await this.getTendencies(user, {
      reportId: lastReport.id,
    });
    return {
      report: report,
      tendencies: tendencies,
    };
  }

  async createHelp(user: UserDto, dto: CreateTendencyHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      const upsertedHelps: TendencyHelp[] = [];
      for (const u of dto.users) {
        const help = await tx.tendencyHelp.findFirst({
          where: {
            helpedUserId: u.id,
            reportId: dto.reportId,
          },
        });
        const upsertedHelp = await tx.tendencyHelp.upsert({
          where: { id: help?.id ?? 0 },
          create: {
            createdUserId: user.id,
            helpedUserId: u.id,
            reportId: dto.reportId,
            isFinancial: u.isFinancial ? true : undefined,
            isImpact: u.isImpact ? true : undefined,
          },
          update: {
            isFinancial: u.isFinancial ? true : undefined,
            isImpact: u.isImpact ? true : undefined,
          },
        });
        upsertedHelps.push(upsertedHelp);
      }
      const activityArgs: CreateActivityArgType[] = [];
      for (const help of upsertedHelps) {
        activityArgs.push({
          activityType: ActivityEnum.TENDENCY_HELP,
          effectedUserIds: [help.helpedUserId],
          payload: { helpId: help.id },
        });
      }
      await this.activityService.createActivity(tx, user, activityArgs, {
        sendNotificationToMe: true,
      });
    });
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateTendencyHelpAnswerDto,
    help: TendencyHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      for (const a of dto.answers) {
        const answer = await tx.tendencyHelpAnswer.findFirst({
          where: {
            tendencyId: a.tendencyId,
            helpId: help.id,
          },
        });
        await tx.tendencyHelpAnswer.upsert({
          where: { id: answer?.id ?? 0 },
          create: {
            tendencyId: a.tendencyId,
            helpId: help.id,
            financialQuestionAnswer: a.financialQuestionAnswer,
            impactQuestionAnswer: a.impactQuestionAnswer,
          },
          update: {
            financialQuestionAnswer: a.financialQuestionAnswer,
            impactQuestionAnswer: a.impactQuestionAnswer,
          },
        });
      }
    });
  }

  async completeHelpAnswer(
    user: UserDto,
    dto: CompleteTendencyHelpAnswerDto,
    help: TendencyHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.tendencyHelp.update({
        where: { id: help.id },
        data: {
          financialStatus: help.isFinancial
            ? HelpStatusEnum.APPROVED
            : undefined,
          impactStatus: help.isImpact ? HelpStatusEnum.APPROVED : undefined,
        },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.TENDENCY_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async updateTendency(user: UserDto, dto: UpdateTendencyDto) {
    await this.prismaService.tendency.update({
      where: {
        id: dto.tendencyId,
        report: { organizationId: user.organizationId },
      },
      data: {
        focusArea: dto.focusArea,
        topic: dto.topic,
        source: dto.source,
        description: dto.description,
      },
    });
  }

  async updateReport(user: UserDto, dto: UpdateTendencyReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.tendencyReport.update({
        where: { id: dto.reportId },
        data: {
          financialThreshold: dto.financialThreshold,
          impactThreshold: dto.impactThreshold,
          isCompleted: dto.isCompleted ? true : undefined,
          isReady: dto.isReady ? true : undefined,
          expiredAt: dto.expiredAt,
        },
      });
      if (dto.selectedTendencyIds) {
        await tx.tendency.updateMany({
          where: {
            id: { in: dto.selectedTendencyIds },
            reportId: dto.reportId,
          },
          data: { isSelected: true },
        });
        await tx.tendency.updateMany({
          where: {
            id: { notIn: dto.selectedTendencyIds },
            reportId: dto.reportId,
          },
          data: { isSelected: false },
        });
      }
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.TENDENCY_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
    });
  }

  async deleteTendency(user: UserDto, dto: DeleteTendencyDto) {
    await this.prismaService.tendency.delete({
      where: {
        id: dto.tendencyId,
        report: { organizationId: user.organizationId },
      },
    });
  }

  async deleteReport(user: UserDto, dto: DeleteTendencyReportDto) {
    await this.prismaService.tendencyReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }
}
