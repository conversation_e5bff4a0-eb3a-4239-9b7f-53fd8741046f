import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetOpinionDto } from '../dtos/opinion/get-opinion.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteOpinionReportDto } from '../dtos/opinion/delete-opinion-report.dto';
import {
  OpinionReport,
  OpinionHelp,
  PrismaClient,
  Prisma,
  StakeholderUser,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateOpinionDto } from '../dtos/opinion/update-opinion.dto';
import { DeleteOpinionDto } from '../dtos/opinion/delete-opinion.dto';
import { UpdateOpinionReportDto } from '../dtos/opinion/update-opinion-report.dto';
import { GetOpinionHelpDto } from '../dtos/opinion/get-opinion-help.dto';
import { CreateOpinionHelpDto } from '../dtos/opinion/create-opinion-help.dto';
import { CreateOpinionHelpAnswerDto } from '../dtos/opinion/create-opinion-help-answer.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { GetOpinionReportDto } from '../dtos/opinion/get-opinion-report.dto';
import { GetOpinionReportUserDto } from '../dtos/opinion/get-opinion-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { UtilService } from '../../common/services/util.service';
import { CreateOpinionDto } from '../dtos/opinion/create-opinion.dto';
import { MailService } from '../../mail/mail.service';
import { OpinionHelpStakeholderUserMailDto } from '../../mail/dtos/opinion-help-mail.dto';
import { Request } from 'express';
import { GetOpinionHelpDetailDto } from '../dtos/opinion/get-opinion-help-detail.dto';
import { GetCurrentOpinionDto } from '../dtos/opinion/get-current-opinion.dto';
import { ActivityService } from '../../activity/activity.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CompleteOpinionHelpAnswerDto } from '../dtos/opinion/complete-opinion-help-answer.dto';

@Injectable()
export class OpinionService {
  private readonly logger = new Logger(OpinionService.name);

  constructor(
    private prismaService: PrismaService,
    private mailService: MailService,
    private activityService: ActivityService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
    private utilService: UtilService,
  ) {}

  async getOpinions(
    user: UserDto,
    dto: GetOpinionDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<{ report: any; opinions: any[] }> {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.opinionReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        financialThreshold: true,
        impactThreshold: true,
        isCompleted: true,
        isReady: true,
        expiredAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          select: {
            id: true,
            isFinancial: true,
            isImpact: true,
            financialStatus: true,
            impactStatus: true,
            stakeholderUser: {
              select: {
                stakeholder: { select: { id: true, name: true, type: true } },
              },
            },
          },
        },
        financialReports: { select: { id: true, name: true } },
        impactReports: { select: { id: true, name: true } },
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const isSelected = report.isCompleted || report.helps.length > 0;
    const opinions = await dbService.opinion.findMany({
      select: {
        id: true,
        focusArea: true,
        priorityIssue: true,
        subPriorityIssue: true,
        minorPriorityIssue: true,
        description: true,
        isSelected: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helpAnswers: { include: { help: true } },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
        isSelected: isSelected ? true : undefined,
      },
    });
    const stakeholders = {};
    for (const help of report.helps) {
      stakeholders[help.stakeholderUser.stakeholder.id] =
        help.stakeholderUser.stakeholder;
    }
    for (const op of opinions) {
      op['createdUserInfo'] = plainToInstance(UserDataDto, op.createdUser, {
        excludeExtraneousValues: true,
      });
      const financialAnswers = op.helpAnswers.filter(
        (a) =>
          a.help.isFinancial &&
          a.help.financialStatus &&
          a.financialQuestionAnswer,
      );
      const impactAnswers = op.helpAnswers.filter(
        (a) => a.help.isImpact && a.help.impactStatus && a.impactQuestionAnswer,
      );
      op['financialPoint'] =
        financialAnswers.reduce(
          (sum, answer) => sum + answer.financialQuestionAnswer,
          0,
        ) / (financialAnswers.length == 0 ? 1 : financialAnswers.length);
      op['impactPoint'] =
        impactAnswers.reduce(
          (sum, answer) => sum + answer.impactQuestionAnswer,
          0,
        ) / (impactAnswers.length == 0 ? 1 : impactAnswers.length);
      delete op.createdUser;
      delete op.helpAnswers;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    report['stakeholders'] = Object.values(stakeholders);
    report['answerRate'] =
      (report.helps.filter((h) => h.financialStatus || h.impactStatus).length /
        (report.helps.length == 0 ? 1 : report.helps.length)) *
      100;
    report['isFinancialHelped'] =
      report.helps.filter((h) => h.isFinancial).length > 0;
    report['isImpactHelped'] =
      report.helps.filter((h) => h.isImpact).length > 0;

    delete report.createdUser;
    delete report.helps;
    return { report: report, opinions: opinions };
  }

  async getCurrentOpinions(
    user: UserDto,
    dto: GetCurrentOpinionDto,
  ): Promise<{ report: any; opinions: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let opinionReport: OpinionReport;
    if (dto.reportId) {
      opinionReport = await this.prismaService.opinionReport.findFirstOrThrow({
        where: {
          id: dto.reportId,
          organizationId: dto.subsidiaryId,
          isReady: true,
        },
      });
    } else {
      opinionReport = await this.getLastReport(dto.subsidiaryId, {
        isReady: true,
      });
      if (!opinionReport) {
        return { report: null, opinions: [] };
      }
    }
    const { report, opinions } = await this.getOpinions(user, {
      reportId: opinionReport.id,
    });
    return {
      report: report,
      opinions: opinions,
    };
  }

  async getReports(user: UserDto, dto: GetOpinionReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.opinionReport.findMany({
      select: {
        id: true,
        name: true,
        financialThreshold: true,
        impactThreshold: true,
        isCompleted: true,
        isReady: true,
        expiredAt: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
      },
      where: {
        organizationId: dto.subsidiaryId,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
        isReady: dto.isReady === undefined ? undefined : dto.isReady,
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      delete r.createdUser;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetOpinionReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        createdOpinionReports: {
          some: { id: dto.reportId, organizationId: dto.subsidiaryId },
        },
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getHelps(user: UserDto, dto: GetOpinionHelpDto) {
    const helps = await this.prismaService.opinionHelp.findMany({
      select: {
        isFinancial: true,
        isImpact: true,
        financialStatus: true,
        impactStatus: true,
        stakeholderUser: {
          select: {
            id: true,
            stakeholder: { select: { id: true, name: true } },
          },
        },
      },
      where: { reportId: dto.reportId },
    });
    const stakeholderResult = {};
    for (const h of helps) {
      const stakeholder = h.stakeholderUser.stakeholder;
      if (stakeholderResult.hasOwnProperty(stakeholder.id)) {
        stakeholderResult[stakeholder.id].userCount++;
        stakeholderResult[stakeholder.id].completeCount +=
          h.financialStatus || h.impactStatus ? 1 : 0;
      } else {
        stakeholderResult[stakeholder.id] = {
          stakeholder: stakeholder,
          userCount: 1,
          completeCount: h.financialStatus || h.impactStatus ? 1 : 0,
        };
      }
    }
    const stakeholderUsers = helps.map((h) => {
      return {
        id: h.stakeholderUser.id,
        isFinancial: h.isFinancial,
        isImpact: h.isImpact,
      };
    });
    return {
      stakeholders: Object.values(stakeholderResult),
      stakeholderUsers: stakeholderUsers,
    };
  }

  async getHelpDetail(
    dto: GetOpinionHelpDetailDto,
    help: OpinionHelp,
  ): Promise<{ report: any; help: any; opinions: any[] }> {
    const report = await this.prismaService.opinionReport.findFirstOrThrow({
      select: { id: true, name: true, expiredAt: true },
      where: { id: help.reportId },
    });
    const opinions = await this.prismaService.opinion.findMany({
      select: {
        id: true,
        focusArea: true,
        priorityIssue: true,
        subPriorityIssue: true,
        minorPriorityIssue: true,
        description: true,
        helpAnswers: {
          select: {
            financialQuestionAnswer: true,
            impactQuestionAnswer: true,
            createdAt: true,
          },
          where: { helpId: help.id },
        },
      },
      where: {
        isSelected: true,
        report: { id: help.reportId },
      },
    });
    for (const op of opinions) {
      op['helpAnswer'] = op.helpAnswers.length > 0 ? op.helpAnswers[0] : null;
      delete op.helpAnswers;
    }
    return {
      report: report,
      help: {
        id: help.id,
        financialStatus: help.financialStatus,
        impactStatus: help.impactStatus,
        isFinancial: help.isFinancial,
        isImpact: help.isImpact,
      },
      opinions: opinions,
    };
  }

  private async getLastReport(
    organizationId: number,
    options?: { isCompleted?: boolean; isReady?: boolean },
  ): Promise<OpinionReport> {
    const report = await this.prismaService.opinionReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
        isReady: options?.isReady,
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createOpinion(user: UserDto, dto: CreateOpinionDto) {
    await this.prismaService.opinion.create({
      data: {
        createdUserId: user.id,
        reportId: dto.reportId,
        focusArea: dto.focusArea,
        priorityIssue: dto.priorityIssue,
        subPriorityIssue: dto.subPriorityIssue,
        minorPriorityIssue: dto.minorPriorityIssue,
        description: dto.description,
      },
    });
  }

  async createReport(user: UserDto): Promise<{ report: any; opinions: any[] }> {
    const maxCodeResult = await this.prismaService.opinionReport.aggregate({
      _max: { code: true },
      where: { organizationId: user.organizationId },
    });
    const code = (maxCodeResult._max.code ?? 0) + 1;
    const defaults = await this.prismaService.opinionDefault.findMany();
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const createdReport = await tx.opinionReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          name: `PG_${code}_${dateFormat}`,
          code: code,
        },
      });
      await tx.opinion.createMany({
        data: defaults.map(
          (d) =>
            ({
              createdUserId: user.id,
              reportId: createdReport.id,
              focusArea: d.focusArea,
              priorityIssue: d.priorityIssue,
              subPriorityIssue: d.subPriorityIssue,
              minorPriorityIssue: d.minorPriorityIssue,
              description: d.description,
              isDefault: true,
            }) as Prisma.OpinionCreateManyInput,
        ),
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.OPINION_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: createdReport.id },
        },
      ]);
      const { report, opinions } = await this.getOpinions(
        user,
        { reportId: createdReport.id },
        { tx: tx },
      );
      return {
        report: report,
        opinions: opinions,
      };
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: any; opinions: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      isReady: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        isReady: false,
      });
      if (!lastReport) {
        return await this.createReport(user);
      }
    }
    const { report, opinions } = await this.getOpinions(user, {
      reportId: lastReport.id,
    });
    return {
      report: report,
      opinions: opinions,
    };
  }

  async createHelp(
    user: UserDto,
    dto: CreateOpinionHelpDto,
    stakeholderUsers: StakeholderUser[],
    request: Request,
  ) {
    const stakeholderUsersMailDto: OpinionHelpStakeholderUserMailDto[] = [];
    await this.prismaService.$transaction(async (tx) => {
      const stakeholderEmails: string[] = [];
      for (const su of stakeholderUsers) {
        if (stakeholderEmails.includes(su.email)) {
          continue;
        }
        const token = this.utilService.getRandomString(
          this.utilService.getRandomInteger(100, 150),
        );
        stakeholderEmails.push(su.email);
        const help = await tx.opinionHelp.findFirst({
          where: {
            stakeholderUserId: su.id,
            reportId: dto.reportId,
          },
        });
        const dtoStakeholderUser = dto.stakeholderUsers.find(
          (dtoSu) => dtoSu.id == su.id,
        );
        const upsertedHelp = await tx.opinionHelp.upsert({
          where: { id: help?.id ?? 0 },
          create: {
            createdUserId: user.id,
            stakeholderUserId: su.id,
            reportId: dto.reportId,
            isFinancial: dtoStakeholderUser.isFinancial ? true : undefined,
            isImpact: dtoStakeholderUser.isImpact ? true : undefined,
            token: token,
          },
          update: {
            isFinancial: dtoStakeholderUser.isFinancial ? true : undefined,
            isImpact: dtoStakeholderUser.isImpact ? true : undefined,
          },
        });
        stakeholderUsersMailDto.push({
          token: upsertedHelp.token,
          stakeholderUser: su,
        });
      }
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(
        tx,
        user,
        [
          {
            activityType: ActivityEnum.OPINION_HELP,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ],
        {
          withoutNotification: true,
        },
      );
    });
    this.mailService
      .sendOpinionHelp(user, {
        request: request,
        stakeholderUsers: stakeholderUsersMailDto,
      })
      .catch((err) => this.logger.error(err));
  }

  async createHelpAnswer(dto: CreateOpinionHelpAnswerDto, help: OpinionHelp) {
    await this.prismaService.$transaction(async (tx) => {
      for (const a of dto.answers) {
        const answer = await tx.opinionHelpAnswer.findFirst({
          where: {
            opinionId: a.opinionId,
            helpId: help.id,
          },
        });
        await tx.opinionHelpAnswer.upsert({
          where: { id: answer?.id ?? 0 },
          create: {
            opinionId: a.opinionId,
            helpId: help.id,
            financialQuestionAnswer: a.financialQuestionAnswer,
            impactQuestionAnswer: a.impactQuestionAnswer,
          },
          update: {
            financialQuestionAnswer: a.financialQuestionAnswer,
            impactQuestionAnswer: a.impactQuestionAnswer,
          },
        });
      }
    });
  }

  async completeHelpAnswer(
    dto: CompleteOpinionHelpAnswerDto,
    help: OpinionHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.opinionHelp.update({
        where: { id: help.id },
        data: {
          financialStatus: help.isFinancial
            ? HelpStatusEnum.APPROVED
            : undefined,
          impactStatus: help.isImpact ? HelpStatusEnum.APPROVED : undefined,
        },
      });
      // await this.activityService.createActivity(tx, user, [
      //   {
      //     activityType: ActivityEnum.OPINION_HELP_ANSWER,
      //     effectedUserIds: [help.createdUserId],
      //     payload: { helpId: help.id },
      //   },
      // ]);
    });
  }

  async updateOpinion(user: UserDto, dto: UpdateOpinionDto) {
    await this.prismaService.opinion.update({
      where: {
        id: dto.opinionId,
        report: { organizationId: user.organizationId },
      },
      data: {
        focusArea: dto.focusArea,
        priorityIssue: dto.priorityIssue,
        subPriorityIssue: dto.subPriorityIssue,
        minorPriorityIssue: dto.minorPriorityIssue,
        description: dto.description,
      },
    });
  }

  async updateReport(user: UserDto, dto: UpdateOpinionReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.opinionReport.update({
        where: { id: dto.reportId },
        data: {
          financialThreshold: dto.financialThreshold,
          impactThreshold: dto.impactThreshold,
          isCompleted: dto.isCompleted ? true : undefined,
          isReady: dto.isReady ? true : undefined,
          expiredAt: dto.expiredAt,
        },
      });
      if (dto.selectedOpinionIds) {
        await tx.opinion.updateMany({
          where: { id: { in: dto.selectedOpinionIds }, reportId: dto.reportId },
          data: { isSelected: true },
        });
        await tx.opinion.updateMany({
          where: {
            id: { notIn: dto.selectedOpinionIds },
            reportId: dto.reportId,
          },
          data: { isSelected: false },
        });
      }
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.OPINION_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
    });
  }

  async deleteOpinion(user: UserDto, dto: DeleteOpinionDto) {
    await this.prismaService.opinion.delete({
      where: {
        id: dto.opinionId,
        report: { organizationId: user.organizationId },
      },
    });
  }

  async deleteReport(user: UserDto, dto: DeleteOpinionReportDto) {
    await this.prismaService.opinionReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }
}
