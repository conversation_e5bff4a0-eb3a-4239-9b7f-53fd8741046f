import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import {
  FinancialReport,
  ImpactReport,
  Prisma,
  PrismaClient,
} from '@prisma/client';
import { ActivityService } from '../../activity/activity.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { CreateMaterialityReportDto } from '../dtos/materiality/create-materiality-report.dto';
import { GetMaterialityReportUserDto } from '../dtos/materiality/get-materiality-report-user.dto';
import { GetMaterialityReportDto } from '../dtos/materiality/get-materiality-report.dto';
import { CreateMaterialityReportType } from '../types/materiality.type';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { MaterialityCreatingAvailabilityEnum } from '../enums/materiality-creating-availability.enum';
import { GetMaterialityDto } from '../dtos/materiality/get-materiality.dto';
import { FinancialService } from './financial.service';
import { ImpactService } from './impact.service';
import { CreateActivityArgType } from '../../activity/types/create-activity-arg.type';

@Injectable()
export class MaterialityService {
  constructor(
    private prismaService: PrismaService,
    private financialService: FinancialService,
    private impactService: ImpactService,
    private activityService: ActivityService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getMaterialities(
    user: UserDto,
    dto: GetMaterialityDto,
  ): Promise<{
    financial?: { report: any; financials: any[] };
    impact?: { report: any; impacts: any[] };
  }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const financialReport = await this.prismaService.financialReport.findFirst({
      where: { code: dto.code, organizationId: dto.subsidiaryId },
    });
    const impactReport = await this.prismaService.impactReport.findFirst({
      where: { code: dto.code, organizationId: dto.subsidiaryId },
    });
    const result: {
      financial?: { report: any; financials: any[] };
      impact?: { report: any; impacts: any[] };
    } = { financial: null, impact: null };
    if (!financialReport && !impactReport) {
      return result;
    }
    if (financialReport) {
      result.financial = await this.financialService.getFinancials(user, {
        reportId: financialReport.id,
      });
    }
    if (impactReport) {
      result.impact = await this.impactService.getImpacts(user, {
        reportId: impactReport.id,
      });
    }
    return result;
  }

  async getReports(user: UserDto, dto: GetMaterialityReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const financialReports = await this.prismaService.financialReport.findMany({
      select: {
        id: true,
        code: true,
        isCompleted: true,
        createdAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        significances: true,
      },
      where: { organizationId: dto.subsidiaryId },
    });
    const impactReports = await this.prismaService.impactReport.findMany({
      select: {
        id: true,
        code: true,
        isCompleted: true,
        createdAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        significances: true,
      },
      where: { organizationId: dto.subsidiaryId },
    });

    const codes: number[] = [];
    const reports = [];
    for (const financialReport of financialReports) {
      codes.push(financialReport.code);
      const impactReport = impactReports.find(
        (ir) => ir.code === financialReport.code,
      );
      const dateFormat = format(financialReport.createdAt, 'ddMMyyyy');
      const name = `ÖA_${financialReport.code}_${dateFormat}`;
      const createdUserInfo = plainToInstance(
        UserDataDto,
        financialReport.createdUser,
        {
          excludeExtraneousValues: true,
        },
      );
      let isCompleted = financialReport.isCompleted;
      if (impactReport && !impactReport.isCompleted) {
        isCompleted = false;
      }
      let hasSignificance = financialReport.significances.length > 0;
      if (impactReport && impactReport.significances.length == 0) {
        hasSignificance = false;
      }
      reports.push({
        financialReportId: financialReport.id,
        impactReportId: impactReport?.id ?? null,
        name: name,
        code: financialReport.code,
        isCompleted: isCompleted,
        createdAt: financialReport.createdAt,
        organization: financialReport.organization,
        createdUserInfo: createdUserInfo,
        hasSignificance: hasSignificance,
      });
    }
    for (const impactReport of impactReports) {
      if (codes.includes(impactReport.code)) {
        continue;
      }
      const dateFormat = format(impactReport.createdAt, 'ddMMyyyy');
      const name = `ÖA_${impactReport.code}_${dateFormat}`;
      const createdUserInfo = plainToInstance(
        UserDataDto,
        impactReport.createdUser,
        {
          excludeExtraneousValues: true,
        },
      );
      reports.push({
        financialReportId: null,
        impactReportId: impactReport.id,
        name: name,
        code: impactReport.code,
        isCompleted: impactReport.isCompleted,
        createdAt: impactReport.createdAt,
        organization: impactReport.organization,
        createdUserInfo: createdUserInfo,
        hasSignificance: impactReport.significances.length > 0,
      });
    }
    return reports.sort((r1, r2) => r1.code - r2.code);
  }

  async getReportUsers(user: UserDto, dto: GetMaterialityReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        OR: [
          {
            createdFinancialReports: {
              some: {
                id: dto.financialReportId,
                organizationId: dto.subsidiaryId,
              },
            },
          },
          {
            createdImpactReports: {
              some: {
                id: dto.impactReportId,
                organizationId: dto.subsidiaryId,
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getReportCreatingAvailability(user: UserDto) {
    const result = {
      opinionReports: [],
      industryReports: [],
      tendencyReports: [],
    };
    const opinionReports = await this.prismaService.opinionReport.findMany({
      where: { organizationId: user.organizationId },
      orderBy: { createdAt: 'desc' },
    });
    const industryReports = await this.prismaService.industryReport.findMany({
      where: { organizationId: user.organizationId },
      orderBy: { createdAt: 'desc' },
    });
    const tendencyReports = await this.prismaService.tendencyReport.findMany({
      where: { organizationId: user.organizationId },
      orderBy: { createdAt: 'desc' },
    });

    for (const r of opinionReports) {
      const report = { id: r.id, name: r.name, creatingAvailabilityType: null };
      if (!r.isCompleted) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.NOT_COMPLETED;
      } else if (r.financialThreshold === null && r.impactThreshold === null) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.HAS_NOT_THRESHOLD;
      } else if (!r.isReady) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.NOT_READY;
      }
      result.opinionReports.push(report);
    }
    for (const r of industryReports) {
      const report = { id: r.id, name: r.name, creatingAvailabilityType: null };
      if (!r.isCompleted) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.NOT_COMPLETED;
      } else if (r.financialThreshold === null && r.impactThreshold === null) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.HAS_NOT_THRESHOLD;
      } else if (!r.isReady) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.NOT_READY;
      }
      result.industryReports.push(report);
    }
    for (const r of tendencyReports) {
      const report = { id: r.id, name: r.name, creatingAvailabilityType: null };
      if (!r.isCompleted) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.NOT_COMPLETED;
      } else if (r.financialThreshold === null && r.impactThreshold === null) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.HAS_NOT_THRESHOLD;
      } else if (!r.isReady) {
        report.creatingAvailabilityType =
          MaterialityCreatingAvailabilityEnum.NOT_READY;
      }
      result.tendencyReports.push(report);
    }
    return result;
  }

  private async getLastCode(
    organizationId: number,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      isCompleted?: boolean;
      hasSignificance?: boolean;
    },
  ): Promise<number> {
    const dbService = options?.tx ?? this.prismaService;
    const financialReport = await dbService.financialReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
        significances:
          options?.hasSignificance === undefined
            ? undefined
            : options?.hasSignificance === true
              ? { some: {} }
              : { none: {} },
      },
      orderBy: { createdAt: 'desc' },
    });
    const impactReport = await dbService.impactReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
        significances:
          options?.hasSignificance === undefined
            ? undefined
            : options?.hasSignificance === true
              ? { some: {} }
              : { none: {} },
      },
      orderBy: { createdAt: 'desc' },
    });
    let code = financialReport?.code;
    if (
      impactReport &&
      impactReport.createdAt.getTime() > financialReport?.createdAt?.getTime()
    ) {
      code = impactReport.code;
    }
    return code;
  }

  async createReport(
    user: UserDto,
    dto: CreateMaterialityReportDto,
    reports: CreateMaterialityReportType,
  ) {
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const financialMaxCode =
      (
        await this.prismaService.financialReport.aggregate({
          _max: { code: true },
          where: { organizationId: user.organizationId },
        })
      )?._max?.code ?? 0;
    const impactMaxCode =
      (
        await this.prismaService.impactReport.aggregate({
          _max: { code: true },
          where: { organizationId: user.organizationId },
        })
      )?._max?.code ?? 0;
    const code = Math.max(financialMaxCode, impactMaxCode) + 1;
    let financialReport: FinancialReport = null;
    let impactReport: ImpactReport = null;
    const result = await this.prismaService.$transaction(async (tx) => {
      if (
        reports.opinion?.financialOpinions?.length > 0 ||
        reports.industry?.financialIndustries?.length > 0 ||
        reports.tendency?.financialTendencies?.length > 0
      ) {
        financialReport = await tx.financialReport.create({
          data: {
            createdUserId: user.id,
            organizationId: user.organizationId,
            name: `FA_${code}_${dateFormat}`,
            code: code,
            chainReportId: reports.chain.report.id,
            opinionReportId: reports.opinion?.report?.id,
            industryReportId: reports.industry?.report?.id,
            tendencyReportId: reports.tendency?.report?.id,
          },
        });
      }
      if (
        reports.opinion?.impactOpinions?.length > 0 ||
        reports.industry?.impactIndustries?.length > 0 ||
        reports.tendency?.impactTendencies?.length > 0
      ) {
        impactReport = await tx.impactReport.create({
          data: {
            createdUserId: user.id,
            organizationId: user.organizationId,
            name: `EA_${code}_${dateFormat}`,
            code: code,
            chainReportId: reports.chain.report.id,
            opinionReportId: reports.opinion?.report?.id,
            industryReportId: reports.industry?.report?.id,
            tendencyReportId: reports.tendency?.report?.id,
          },
        });
      }
      if (!financialReport && !impactReport) {
        throw new BadRequestException(
          'Önemlilik tablosu oluşturmak için gerekli veri bulunamadı.',
        );
      }
      if (reports.opinion) {
        await this.createReportFromOpinion(tx, user, reports.opinion, {
          financialReportId: financialReport?.id,
          impactReportId: impactReport?.id,
        });
      }
      if (reports.industry) {
        await this.createReportFromIndustry(tx, user, reports.industry, {
          financialReportId: financialReport?.id,
          impactReportId: impactReport?.id,
        });
      }
      if (reports.tendency) {
        await this.createReportFromTendency(tx, user, reports.tendency, {
          financialReportId: financialReport?.id,
          impactReportId: impactReport?.id,
        });
      }
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      const activityArgs: CreateActivityArgType[] = [];
      if (financialReport) {
        activityArgs.push({
          activityType: ActivityEnum.FINANCIAL_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: financialReport.id },
        });
      }
      if (impactReport) {
        activityArgs.push({
          activityType: ActivityEnum.IMPACT_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: impactReport.id },
        });
      }
      await this.activityService.createActivity(tx, user, activityArgs);
      return { financialReport: financialReport, impactReport: impactReport };
    });
    return result;
  }

  private async createReportFromOpinion(
    tx: Omit<PrismaClient, ITXClientDenyList>,
    user: UserDto,
    opinion: CreateMaterialityReportType['opinion'],
    materialityReports: {
      financialReportId?: number;
      impactReportId?: number;
    },
  ): Promise<void> {
    if (materialityReports.financialReportId) {
      await tx.financial.createMany({
        data: opinion.financialOpinions.map(
          (op) =>
            ({
              createdUserId: user.id,
              reportId: materialityReports.financialReportId,
              opinionId: op.id,
              focusAreas: [op.focusArea],
              priorityIssue: op.priorityIssue,
              subPriorityIssue: op.subPriorityIssue,
              minorPriorityIssue: op.minorPriorityIssue,
              description: op.description,
            }) as Prisma.FinancialCreateManyInput,
        ),
      });
    }
    if (materialityReports.impactReportId) {
      await tx.impact.createMany({
        data: opinion.impactOpinions.map(
          (op) =>
            ({
              createdUserId: user.id,
              reportId: materialityReports.impactReportId,
              opinionId: op.id,
              focusAreas: [op.focusArea],
              priorityIssue: op.priorityIssue,
              subPriorityIssue: op.subPriorityIssue,
              minorPriorityIssue: op.minorPriorityIssue,
              description: op.description,
            }) as Prisma.ImpactCreateManyInput,
        ),
      });
    }
  }

  private async createReportFromIndustry(
    tx: Omit<PrismaClient, ITXClientDenyList>,
    user: UserDto,
    industry: CreateMaterialityReportType['industry'],
    materialityReports: {
      financialReportId?: number;
      impactReportId?: number;
    },
  ): Promise<void> {
    if (materialityReports.financialReportId) {
      const createManyFinancials = industry.financialIndustries
        .map((ind) => {
          return ind.details
            .map(
              (d) =>
                ({
                  createdUserId: user.id,
                  reportId: materialityReports.financialReportId,
                  industryId: ind.id,
                  focusAreas: d.focusAreas,
                  priorityIssue: d.priorityIssue,
                  subPriorityIssue: d.priorityIssue,
                  description: ind.description,
                  industryDetailId: d.id,
                }) as Prisma.FinancialCreateManyInput,
            )
            .flat(1);
        })
        .flat(1);
      await tx.financial.createMany({ data: createManyFinancials });
    }
    if (materialityReports.impactReportId) {
      const createManyImpacts = industry.impactIndustries
        .map((ind) => {
          return ind.details
            .map(
              (d) =>
                ({
                  createdUserId: user.id,
                  reportId: materialityReports.impactReportId,
                  industryId: ind.id,
                  focusAreas: d.focusAreas,
                  priorityIssue: d.priorityIssue,
                  subPriorityIssue: d.priorityIssue,
                  description: ind.description,
                  industryDetailId: d.id,
                }) as Prisma.ImpactCreateManyInput,
            )
            .flat(1);
        })
        .flat(1);
      await tx.impact.createMany({ data: createManyImpacts });
    }
  }

  private async createReportFromTendency(
    tx: Omit<PrismaClient, ITXClientDenyList>,
    user: UserDto,
    tendency: CreateMaterialityReportType['tendency'],
    materialityReports: {
      financialReportId?: number;
      impactReportId?: number;
    },
  ): Promise<void> {
    if (materialityReports.financialReportId) {
      await tx.financial.createMany({
        data: tendency.financialTendencies.map(
          (t) =>
            ({
              createdUserId: user.id,
              reportId: materialityReports.financialReportId,
              tendencyId: t.id,
              focusAreas: [t.focusArea],
              priorityIssue: t.topic,
              subPriorityIssue: t.topic,
              description: t.description,
            }) as Prisma.FinancialCreateManyInput,
        ),
      });
    }
    if (materialityReports.impactReportId) {
      await tx.impact.createMany({
        data: tendency.impactTendencies.map(
          (t) =>
            ({
              createdUserId: user.id,
              reportId: materialityReports.impactReportId,
              tendencyId: t.id,
              focusAreas: [t.focusArea],
              priorityIssue: t.topic,
              subPriorityIssue: t.topic,
              description: t.description,
            }) as Prisma.ImpactCreateManyInput,
        ),
      });
    }
  }

  async createReportForSidebar(user: UserDto): Promise<{
    financial?: { report: any; financials: any[] };
    impact?: { report: any; impacts: any[] };
  }> {
    let lastCode = await this.getLastCode(user.organizationId, {
      hasSignificance: true,
    });
    if (!lastCode) {
      lastCode = await this.getLastCode(user.organizationId, {
        hasSignificance: false,
      });
      if (!lastCode) {
        return { financial: null, impact: null };
      }
    }
    const result = await this.getMaterialities(user, {
      code: lastCode,
    });
    return result;
  }
}
