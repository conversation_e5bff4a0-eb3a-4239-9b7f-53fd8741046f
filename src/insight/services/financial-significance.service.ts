import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import {
  FinancialReport,
  FinancialSignificance,
  PrismaClient,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { GetFinancialSignificanceReportDto } from '../dtos/financial-significance/get-financial-significance-report.dto';
import { GetFinancialSignificanceReportUserDto } from '../dtos/financial-significance/get-financial-significance-report-user.dto';
import { GetCurrentFinancialSignificanceDto } from '../dtos/financial-significance/get-current-financial-significance.dto';
import { ActivityService } from '../../activity/activity.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { GetFinancialSignificanceDto } from '../dtos/financial-significance/get-financial-significance.dto';
import { CreateFinancialSignificanceReportDto } from '../dtos/financial-significance/create-financial-significance-report.dto';
import { FinancialService } from './financial.service';

@Injectable()
export class FinancialSignificanceService {
  constructor(
    private prismaService: PrismaService,
    private financialService: FinancialService,
    private activityService: ActivityService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getSignificances(
    user: UserDto,
    dto: GetFinancialSignificanceDto,
  ): Promise<{
    significance: any;
    report: any;
    financials: any[];
  }> {
    const significance =
      await this.prismaService.financialSignificance.findFirstOrThrow({
        select: { id: true, reportId: true, name: true, createdAt: true },
        where: {
          id: dto.significanceId,
          report: { organizationId: user.organizationId },
        },
      });
    const { report, financials } = await this.financialService.getFinancials(
      user,
      {
        reportId: significance.reportId,
      },
    );
    return {
      significance: significance,
      report: report,
      financials: financials,
    };
  }

  async getCurrentSignificances(
    user: UserDto,
    dto: GetCurrentFinancialSignificanceDto,
  ): Promise<{ significance: any; report: any; financials: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let significance: FinancialSignificance;
    if (dto.significanceId) {
      significance =
        await this.prismaService.financialSignificance.findFirstOrThrow({
          where: {
            id: dto.significanceId,
            report: { organizationId: dto.subsidiaryId },
          },
        });
    } else {
      significance = await this.getLastReport(dto.subsidiaryId);
      if (!significance) {
        return { significance: null, report: null, financials: [] };
      }
    }
    const { report, financials } = await this.financialService.getFinancials(
      user,
      {
        reportId: significance.reportId,
      },
    );
    return {
      significance: significance,
      report: report,
      financials: financials,
    };
  }

  async getReports(user: UserDto, dto: GetFinancialSignificanceReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const significances =
      await this.prismaService.financialSignificance.findMany({
        select: {
          id: true,
          name: true,
          createdAt: true,
          updatedAt: true,
          createdUser: {
            select: {
              id: true,
              name: true,
              surname: true,
              image: true,
              userOrganizations: true,
            },
          },
          report: {
            select: {
              id: true,
              name: true,
              organization: { select: { id: true, name: true } },
            },
          },
        },
        where: { report: { organizationId: dto.subsidiaryId } },
      });
    for (const s of significances) {
      s['createdUserInfo'] = plainToInstance(UserDataDto, s.createdUser, {
        excludeExtraneousValues: true,
      });
      s['organization'] = s.report.organization;
      delete s.createdUser;
      delete s.report.organization;
    }
    return significances;
  }

  async getReportUsers(
    user: UserDto,
    dto: GetFinancialSignificanceReportUserDto,
  ) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        createdFinancialSignificances: {
          some: {
            id: dto.significanceId,
            report: { organizationId: dto.subsidiaryId },
          },
        },
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  private async getLastReport(
    organizationId: number,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ): Promise<FinancialSignificance> {
    const dbService = options?.tx ?? this.prismaService;
    const report = await dbService.financialSignificance.findFirst({
      where: {
        report: { organizationId: organizationId },
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createReport(
    user: UserDto,
    dto: CreateFinancialSignificanceReportDto,
    report: FinancialReport,
  ): Promise<{ significance: any; report: any; financials: any[] }> {
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const significance = await this.prismaService.$transaction(async (tx) => {
      const significance = await tx.financialSignificance.create({
        data: {
          createdUserId: user.id,
          reportId: report.id,
          name: `ÖT_FA_${report.code}_${dateFormat}`,
        },
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.FINANCIAL_SIGNIFICANCE_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: significance.id },
        },
      ]);
      return significance;
    });
    const { report: financialReport, financials } =
      await this.financialService.getFinancials(user, { reportId: report.id });
    return {
      significance: significance,
      report: financialReport,
      financials: financials,
    };
  }
}
