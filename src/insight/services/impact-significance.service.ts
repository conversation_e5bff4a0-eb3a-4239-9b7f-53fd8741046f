import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { ImpactReport, ImpactSignificance, PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { GetImpactSignificanceReportDto } from '../dtos/impact-significance/get-impact-significance-report.dto';
import { GetImpactSignificanceReportUserDto } from '../dtos/impact-significance/get-impact-significance-report-user.dto';
import { GetCurrentImpactSignificanceDto } from '../dtos/impact-significance/get-current-impact-significance.dto';
import { ActivityService } from '../../activity/activity.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { GetImpactSignificanceDto } from '../dtos/impact-significance/get-impact-significance.dto';
import { CreateImpactSignificanceReportDto } from '../dtos/impact-significance/create-impact-significance-report.dto';
import { ImpactService } from './impact.service';

@Injectable()
export class ImpactSignificanceService {
  constructor(
    private prismaService: PrismaService,
    private impactService: ImpactService,
    private activityService: ActivityService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getSignificances(
    user: UserDto,
    dto: GetImpactSignificanceDto,
  ): Promise<{
    significance: any;
    report: any;
    impacts: any[];
  }> {
    const significance =
      await this.prismaService.impactSignificance.findFirstOrThrow({
        select: { id: true, reportId: true, name: true, createdAt: true },
        where: {
          id: dto.significanceId,
          report: { organizationId: user.organizationId },
        },
      });
    const { report, impacts } = await this.impactService.getImpacts(user, {
      reportId: significance.reportId,
    });
    return {
      significance: significance,
      report: report,
      impacts: impacts,
    };
  }

  async getCurrentSignificances(
    user: UserDto,
    dto: GetCurrentImpactSignificanceDto,
  ): Promise<{ significance: any; report: any; impacts: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let significance: ImpactSignificance;
    if (dto.significanceId) {
      significance =
        await this.prismaService.impactSignificance.findFirstOrThrow({
          where: {
            id: dto.significanceId,
            report: { organizationId: dto.subsidiaryId },
          },
        });
    } else {
      significance = await this.getLastReport(dto.subsidiaryId);
      if (!significance) {
        return { significance: null, report: null, impacts: [] };
      }
    }
    const { report, impacts } = await this.impactService.getImpacts(user, {
      reportId: significance.reportId,
    });
    return {
      significance: significance,
      report: report,
      impacts: impacts,
    };
  }

  async getReports(user: UserDto, dto: GetImpactSignificanceReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const significances = await this.prismaService.impactSignificance.findMany({
      select: {
        id: true,
        name: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        report: {
          select: {
            id: true,
            name: true,
            organization: { select: { id: true, name: true } },
          },
        },
      },
      where: { report: { organizationId: dto.subsidiaryId } },
    });
    for (const s of significances) {
      s['createdUserInfo'] = plainToInstance(UserDataDto, s.createdUser, {
        excludeExtraneousValues: true,
      });
      s['organization'] = s.report.organization;
      delete s.createdUser;
      delete s.report.organization;
    }
    return significances;
  }

  async getReportUsers(user: UserDto, dto: GetImpactSignificanceReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        createdImpactSignificances: {
          some: {
            id: dto.significanceId,
            report: { organizationId: dto.subsidiaryId },
          },
        },
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  private async getLastReport(
    organizationId: number,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ): Promise<ImpactSignificance> {
    const dbService = options?.tx ?? this.prismaService;
    const report = await dbService.impactSignificance.findFirst({
      where: {
        report: { organizationId: organizationId },
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createReport(
    user: UserDto,
    dto: CreateImpactSignificanceReportDto,
    report: ImpactReport,
  ): Promise<{ significance: any; report: any; impacts: any[] }> {
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const significance = await this.prismaService.$transaction(async (tx) => {
      const significance = await tx.impactSignificance.create({
        data: {
          createdUserId: user.id,
          reportId: report.id,
          name: `ÖT_EA_${report.code}_${dateFormat}`,
        },
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.IMPACT_SIGNIFICANCE_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: significance.id },
        },
      ]);
      return significance;
    });
    const { report: impactReport, impacts } =
      await this.impactService.getImpacts(user, { reportId: report.id });
    return {
      significance: significance,
      report: impactReport,
      impacts: impacts,
    };
  }
}
