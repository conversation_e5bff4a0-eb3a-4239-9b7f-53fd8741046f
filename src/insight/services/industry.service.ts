import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetIndustryDto } from '../dtos/industry/get-industry.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteIndustryReportDto } from '../dtos/industry/delete-industry-report.dto';
import {
  IndustryReport,
  IndustryHelp,
  PrismaClient,
  Prisma,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateIndustryDto } from '../dtos/industry/update-industry.dto';
import { DeleteIndustryDto } from '../dtos/industry/delete-industry.dto';
import { UpdateIndustryReportDto } from '../dtos/industry/update-industry-report.dto';
import { CreateIndustryHelpDto } from '../dtos/industry/create-industry-help.dto';
import { CreateIndustryHelpAnswerDto } from '../dtos/industry/create-industry-help-answer.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { GetIndustryReportDto } from '../dtos/industry/get-industry-report.dto';
import { GetIndustryReportUserDto } from '../dtos/industry/get-industry-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { CreateIndustryDto } from '../dtos/industry/create-industry.dto';
import { GetIndustryHelpDetailDto } from '../dtos/industry/get-industry-help-detail.dto';
import { GetCurrentIndustryDto } from '../dtos/industry/get-current-industry.dto';
import { ActivityService } from '../../activity/activity.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateActivityArgType } from '../../activity/types/create-activity-arg.type';
import { CompleteIndustryHelpAnswerDto } from '../dtos/industry/complete-industry-help-answer.dto';

@Injectable()
export class IndustryService {
  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getIndustries(
    user: UserDto,
    dto: GetIndustryDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<{ report: any; industries: any[] }> {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const orgSectorIds = (
      await dbService.organizationSector.findMany({
        where: { organizationId: user.organizationId },
      })
    ).map((os) => os.sectorId);
    const report = await dbService.industryReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        financialThreshold: true,
        impactThreshold: true,
        isCompleted: true,
        isReady: true,
        expiredAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          select: {
            id: true,
            isFinancial: true,
            isImpact: true,
            financialStatus: true,
            impactStatus: true,
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
        },
        financialReports: { select: { id: true, name: true } },
        impactReports: { select: { id: true, name: true } },
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const isSelected = report.isCompleted || report.helps.length > 0;
    if (isSelected) {
      dto.sectorIds = undefined;
    } else if (!dto.sectorIds) {
      dto.sectorIds = orgSectorIds;
    }
    const industries = await dbService.industry.findMany({
      select: {
        id: true,
        sectorIds: true,
        sourceIds: true,
        sourceText: true,
        description: true,
        isDefault: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        details: {
          select: {
            id: true,
            priorityIssue: true,
            focusAreas: true,
            isSelected: true,
            createdAt: true,
            updatedAt: true,
            helpAnswers: { include: { help: true } },
          },
          where: { isSelected: isSelected ? true : undefined },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
        details: isSelected ? { some: { isSelected: true } } : undefined,
        OR:
          dto.sectorIds?.length > 0
            ? dto.sectorIds.map((sId) => ({
                sectorIds: { array_contains: sId },
              }))
            : undefined,
      },
    });
    const helpedUsers = report.helps.map((h) => {
      const helpedUser = plainToInstance(UserDataDto, h.helpedUser, {
        excludeExtraneousValues: true,
      });
      helpedUser['isFinancial'] = h.isFinancial;
      helpedUser['isImpact'] = h.isImpact;
      return helpedUser;
    });
    for (const ind of industries) {
      ind['createdUserInfo'] = plainToInstance(UserDataDto, ind.createdUser, {
        excludeExtraneousValues: true,
      });
      for (const detail of ind.details) {
        const financialAnswers = detail.helpAnswers.filter(
          (a) =>
            a.help.isFinancial &&
            a.help.financialStatus &&
            a.financialQuestionAnswer,
        );
        const impactAnswers = detail.helpAnswers.filter(
          (a) =>
            a.help.isImpact && a.help.impactStatus && a.impactQuestionAnswer,
        );
        detail['financialPoint'] =
          financialAnswers.reduce(
            (sum, answer) => sum + answer.financialQuestionAnswer,
            0,
          ) / (financialAnswers.length == 0 ? 1 : financialAnswers.length);
        detail['impactPoint'] =
          impactAnswers.reduce(
            (sum, answer) => sum + answer.impactQuestionAnswer,
            0,
          ) / (impactAnswers.length == 0 ? 1 : impactAnswers.length);

        const financialAnsweredUserIds = financialAnswers.map(
          (a) => a.help.helpedUserId,
        );
        const impactAnsweredUserIds = impactAnswers.map(
          (a) => a.help.helpedUserId,
        );
        detail['helpedUsers'] = structuredClone(helpedUsers).map((hu) => {
          hu['isFinancialAnswered'] = financialAnsweredUserIds.includes(hu.id);
          hu['isImpactAnswered'] = impactAnsweredUserIds.includes(hu.id);
          return hu;
        });
        delete detail.helpAnswers;
      }
      delete ind.createdUser;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    report['answerRate'] =
      (report.helps.filter((h) => h.financialStatus || h.impactStatus).length /
        (report.helps.length == 0 ? 1 : report.helps.length)) *
      100;
    report['isFinancialHelped'] =
      report.helps.filter((h) => h.isFinancial).length > 0;
    report['isImpactHelped'] =
      report.helps.filter((h) => h.isImpact).length > 0;
    report['organizationSectorIds'] = orgSectorIds;

    report['selfHelp'] = null;
    const selfHelp = report.helps.find((h) => h.helpedUser.id == user.id);
    if (selfHelp) {
      report['selfHelp'] = {
        id: selfHelp.id,
        isFinancial: selfHelp.isFinancial,
        isImpact: selfHelp.isImpact,
        financialStatus: selfHelp.financialStatus,
        impactStatus: selfHelp.impactStatus,
      };
    }

    delete report.createdUser;
    delete report.helps;
    return { report: report, industries: industries };
  }

  async getCurrentIndustries(
    user: UserDto,
    dto: GetCurrentIndustryDto,
  ): Promise<{ report: any; industries: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let industryReport: IndustryReport;
    if (dto.reportId) {
      industryReport = await this.prismaService.industryReport.findFirstOrThrow(
        {
          where: {
            id: dto.reportId,
            organizationId: dto.subsidiaryId,
            isReady: true,
          },
        },
      );
    } else {
      industryReport = await this.getLastReport(dto.subsidiaryId, {
        isReady: true,
      });
      if (!industryReport) {
        return { report: null, industries: [] };
      }
    }
    const { report, industries } = await this.getIndustries(user, {
      reportId: industryReport.id,
    });
    return {
      report: report,
      industries: industries,
    };
  }

  async getReports(user: UserDto, dto: GetIndustryReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.industryReport.findMany({
      select: {
        id: true,
        name: true,
        financialThreshold: true,
        impactThreshold: true,
        isCompleted: true,
        isReady: true,
        expiredAt: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
      },
      where: {
        organizationId: dto.subsidiaryId,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
        isReady: dto.isReady === undefined ? undefined : dto.isReady,
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      delete r.createdUser;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetIndustryReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        createdIndustryReports: {
          some: { id: dto.reportId, organizationId: dto.subsidiaryId },
        },
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetIndustryHelpDetailDto,
    help: IndustryHelp,
  ): Promise<{ report: any; help: any; industries: any[] }> {
    const report = await this.prismaService.industryReport.findFirstOrThrow({
      select: { id: true, name: true, expiredAt: true },
      where: { id: help.reportId },
    });
    const industries = await this.prismaService.industry.findMany({
      select: {
        id: true,
        sectorIds: true,
        sourceIds: true,
        sourceText: true,
        description: true,
        details: {
          select: {
            id: true,
            priorityIssue: true,
            focusAreas: true,
            helpAnswers: {
              select: {
                financialQuestionAnswer: true,
                impactQuestionAnswer: true,
                createdAt: true,
              },
              where: { helpId: help.id },
            },
          },
          where: { isSelected: true },
        },
      },
      where: {
        details: { some: { isSelected: true } },
        report: { id: help.reportId },
      },
    });
    for (const ind of industries) {
      for (const d of ind.details) {
        d['helpAnswer'] = d.helpAnswers.length > 0 ? d.helpAnswers[0] : null;
        delete d.helpAnswers;
      }
    }
    return {
      report: report,
      help: {
        id: help.id,
        financialStatus: help.financialStatus,
        impactStatus: help.impactStatus,
        isFinancial: help.isFinancial,
        isImpact: help.isImpact,
      },
      industries: industries,
    };
  }

  private async getLastReport(
    organizationId: number,
    options?: { isCompleted?: boolean; isReady?: boolean },
  ): Promise<IndustryReport> {
    const report = await this.prismaService.industryReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
        isReady: options?.isReady,
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createIndustry(user: UserDto, dto: CreateIndustryDto) {
    await this.prismaService.industry.create({
      data: {
        createdUserId: user.id,
        reportId: dto.reportId,
        sectorIds: dto.sectorIds,
        sourceIds: dto.sourceIds,
        sourceText: dto.sourceText,
        description: dto.description,
        details: {
          createMany: {
            data: dto.details.map((d) => ({
              createdUserId: user.id,
              priorityIssue: d.priorityIssue,
              focusAreas: d.focusAreas,
            })),
          },
        },
      },
    });
  }

  async createReport(
    user: UserDto,
  ): Promise<{ report: any; industries: any[] }> {
    const maxCodeResult = await this.prismaService.industryReport.aggregate({
      _max: { code: true },
      where: { organizationId: user.organizationId },
    });
    const code = (maxCodeResult._max.code ?? 0) + 1;
    const defaults = await this.prismaService.industryDefault.findMany({
      include: { details: true },
    });
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const createdReport = await tx.industryReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          name: `SA_${code}_${dateFormat}`,
          code: code,
        },
      });
      const createIndustryData = (d: any, user: any, reportId: number) => ({
        createdUserId: user.id,
        reportId,
        sectorIds: d.sectorIds,
        sourceIds: d.sourceIds,
        sourceText: d.sourceText,
        description: d.description,
        isDefault: true,
        details: {
          createMany: {
            data: d.details.map((detail) => ({
              createdUserId: user.id,
              priorityIssue: detail.priorityIssue,
              focusAreas: detail.focusAreas,
              metricDefaultIds: detail.metricDefaultIds,
            })),
          },
        },
      });

      await Promise.all(
        defaults.map((d) =>
          tx.industry.create({
            data: createIndustryData(d, user, createdReport.id),
          }),
        ),
      );
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.INDUSTRY_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: createdReport.id },
        },
      ]);
      const { report, industries } = await this.getIndustries(
        user,
        { reportId: createdReport.id },
        { tx: tx },
      );
      return {
        report: report,
        industries: industries,
      };
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: any; industries: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      isReady: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        isReady: false,
      });
      if (!lastReport) {
        return await this.createReport(user);
      }
    }
    const { report, industries } = await this.getIndustries(user, {
      reportId: lastReport.id,
    });
    return {
      report: report,
      industries: industries,
    };
  }

  async createHelp(user: UserDto, dto: CreateIndustryHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      const upsertedHelps: IndustryHelp[] = [];
      for (const u of dto.users) {
        const help = await tx.industryHelp.findFirst({
          where: {
            helpedUserId: u.id,
            reportId: dto.reportId,
          },
        });
        const upsertedHelp = await tx.industryHelp.upsert({
          where: { id: help?.id ?? 0 },
          create: {
            createdUserId: user.id,
            helpedUserId: u.id,
            reportId: dto.reportId,
            isFinancial: u.isFinancial ? true : undefined,
            isImpact: u.isImpact ? true : undefined,
          },
          update: {
            isFinancial: u.isFinancial ? true : undefined,
            isImpact: u.isImpact ? true : undefined,
          },
        });
        upsertedHelps.push(upsertedHelp);
      }
      const activityArgs: CreateActivityArgType[] = [];
      for (const help of upsertedHelps) {
        activityArgs.push({
          activityType: ActivityEnum.INDUSTRY_HELP,
          effectedUserIds: [help.helpedUserId],
          payload: { helpId: help.id },
        });
      }
      await this.activityService.createActivity(tx, user, activityArgs, {
        sendNotificationToMe: true,
      });
    });
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateIndustryHelpAnswerDto,
    help: IndustryHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      for (const a of dto.answers) {
        const answer = await tx.industryHelpAnswer.findFirst({
          where: {
            detailId: a.detailId,
            helpId: help.id,
          },
        });
        await tx.industryHelpAnswer.upsert({
          where: { id: answer?.id ?? 0 },
          create: {
            detailId: a.detailId,
            helpId: help.id,
            financialQuestionAnswer: a.financialQuestionAnswer,
            impactQuestionAnswer: a.impactQuestionAnswer,
          },
          update: {
            financialQuestionAnswer: a.financialQuestionAnswer,
            impactQuestionAnswer: a.impactQuestionAnswer,
          },
        });
      }
    });
  }

  async completeHelpAnswer(
    user: UserDto,
    dto: CompleteIndustryHelpAnswerDto,
    help: IndustryHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.industryHelp.update({
        where: { id: help.id },
        data: {
          financialStatus: help.isFinancial
            ? HelpStatusEnum.APPROVED
            : undefined,
          impactStatus: help.isImpact ? HelpStatusEnum.APPROVED : undefined,
        },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.INDUSTRY_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async updateIndustry(user: UserDto, dto: UpdateIndustryDto) {
    const updateOrDeleteDetails = dto.details.filter((d) => d.id);
    const createDetails = dto.details.filter((d) => !d.id);
    await this.prismaService.$transaction(async (tx) => {
      await tx.industry.update({
        where: {
          id: dto.industryId,
          report: { organizationId: user.organizationId },
        },
        data: {
          sectorIds: dto.sectorIds,
          sourceIds: dto.sourceIds,
          sourceText: dto.sourceText,
          description: dto.description,
        },
      });
      for (const d of updateOrDeleteDetails) {
        await tx.industryDetail.update({
          where: { id: d.id },
          data: {
            priorityIssue: d.priorityIssue,
            focusAreas: d.focusAreas,
          },
        });
      }
      await tx.industryDetail.deleteMany({
        where: {
          id: { notIn: updateOrDeleteDetails.map((d) => d.id) },
          industryId: dto.industryId,
        },
      });
      await tx.industryDetail.createMany({
        data: createDetails.map((d) => ({
          createdUserId: user.id,
          industryId: dto.industryId,
          priorityIssue: d.priorityIssue,
          focusAreas: d.focusAreas,
        })),
      });
    });
  }

  async updateReport(user: UserDto, dto: UpdateIndustryReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.industryReport.update({
        where: { id: dto.reportId },
        data: {
          financialThreshold: dto.financialThreshold,
          impactThreshold: dto.impactThreshold,
          isCompleted: dto.isCompleted ? true : undefined,
          isReady: dto.isReady ? true : undefined,
          expiredAt: dto.expiredAt,
        },
      });
      if (dto.selectedDetailIds) {
        await tx.industryDetail.updateMany({
          where: {
            id: { in: dto.selectedDetailIds },
            industry: { reportId: dto.reportId },
          },
          data: { isSelected: true },
        });
        await tx.industryDetail.updateMany({
          where: {
            id: { notIn: dto.selectedDetailIds },
            industry: { reportId: dto.reportId },
          },
          data: { isSelected: false },
        });
      }
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.INDUSTRY_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
    });
  }

  async deleteIndustry(user: UserDto, dto: DeleteIndustryDto) {
    await this.prismaService.industry.delete({
      where: {
        id: dto.industryId,
        report: { organizationId: user.organizationId },
      },
    });
  }

  async deleteReport(user: UserDto, dto: DeleteIndustryReportDto) {
    await this.prismaService.industryReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }
}
