import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetFinancialDto } from '../dtos/financial/get-financial.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteFinancialReportDto } from '../dtos/financial/delete-financial-report.dto';
import {
  FinancialHelp,
  FinancialReport,
  FinancialReview,
  Industry,
  IndustryReport,
  Opinion,
  OpinionReport,
  Prisma,
  PrismaClient,
  Tendency,
  TendencyReport,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateFinancialReportDto } from '../dtos/financial/update-financial-report.dto';
import { CreateFinancialHelpDto } from '../dtos/financial/create-financial-help.dto';
import { CreateFinancialHelpAnswerDto } from '../dtos/financial/create-financial-help-answer.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { GetFinancialReportDto } from '../dtos/financial/get-financial-report.dto';
import { GetFinancialReportUserDto } from '../dtos/financial/get-financial-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { UpdateFinancialDetailDto } from '../dtos/financial/update-financial-detail.dto';
import { GetFinancialHelpDetailDto } from '../dtos/financial/get-financial-help-detail.dto';
import { GetCurrentFinancialDto } from '../dtos/financial/get-current-financial.dto';
import { CreateFinancialReportFromOpinionDto } from '../dtos/financial/create-financial-report-from-opinion.dto';
import { UpdateFinancialPriorityDto } from '../dtos/financial/update-financial-priority.dto';
import { CreateFinancialReviewDto } from '../dtos/financial/create-financial-review.dto';
import { CreateFinancialReviewAnswerDto } from '../dtos/financial/create-financial-review-answer.dto';
import { GetFinancialReviewDetailDto } from '../dtos/financial/get-financial-review-detail.dto';
import { CreateFinancialReportFromIndustryDto } from '../dtos/financial/create-financial-report-from-industry.dto';
import { CreateFinancialReportFromTendencyDto } from '../dtos/financial/create-financial-report-from-tendency.dto';
import { ActivityService } from '../../activity/activity.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { CreateActivityArgType } from '../../activity/types/create-activity-arg.type';
import { ChainService } from '../../analysis/services/chain.service';
import { ResetFinancialReportDto } from '../dtos/financial/reset-financial-report.dto';
import { MaterialityResetEnum } from '../enums/materiality-reset.enum';

@Injectable()
export class FinancialService {
  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private chainService: ChainService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getFinancials(
    user: UserDto,
    dto: GetFinancialDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<{ report: any; financials: any[] }> {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.financialReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        code: true,
        isCompleted: true,
        expiredAt: true,
        chainReport: { select: { id: true, name: true } },
        opinionReport: { select: { id: true, name: true } },
        industryReport: { select: { id: true, name: true } },
        tendencyReport: { select: { id: true, name: true } },
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          select: {
            id: true,
            status: true,
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
        },
        priorities: {
          select: {
            type: true,
            minValue: true,
            maxValue: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        reviews: {
          select: {
            id: true,
            status: true,
            senderDescription: true,
            reviewerDescription: true,
            createdAt: true,
            updatedAt: true,
            reviewerUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
        },
        significances: true,
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const financials = await dbService.financial.findMany({
      select: {
        id: true,
        opinionId: true,
        industryId: true,
        tendencyId: true,
        focusAreas: true,
        priorityIssue: true,
        subPriorityIssue: true,
        minorPriorityIssue: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        details: {
          select: {
            id: true,
            categoryDescription: true,
            categoryType: true,
            periodType: true,
            chainIds: true,
            countryIds: true,
            createdAt: true,
            updatedAt: true,
            answers: {
              select: {
                severity: true,
                possibility: true,
                help: { select: { helpedUserId: true } },
              },
            },
          },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
      },
    });
    for (const r of report.reviews) {
      r['reviewerUserInfo'] = plainToInstance(UserDataDto, r.reviewerUser, {
        excludeExtraneousValues: true,
      });
    }
    const helpedUsers = report.helps.map((h) => {
      return plainToInstance(UserDataDto, h.helpedUser, {
        excludeExtraneousValues: true,
      });
    });
    for (const f of financials) {
      f['createdUserInfo'] = plainToInstance(UserDataDto, f.createdUser, {
        excludeExtraneousValues: true,
      });
      for (const d of f.details) {
        const divider = d.answers.length == 0 ? 1 : d.answers.length;
        d['severity'] =
          d.answers.reduce((sum, a) => sum + a.severity, 0) / divider;
        d['possibility'] =
          d.answers.reduce((sum, a) => sum + a.possibility, 0) / divider;
        d['result'] = (d['severity'] + d['possibility']) / 2;

        const answeredUserIds = d.answers.map((a) => a.help.helpedUserId);
        d['helpedUsers'] = structuredClone(helpedUsers).map((hu) => {
          hu['isAnswered'] = answeredUserIds.includes(hu.id);
          return hu;
        });
        delete d.answers;
      }
      delete f.createdUser;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    report['answerRate'] =
      (report.helps.filter((h) => h.status).length /
        (report.helps.length == 0 ? 1 : report.helps.length)) *
      100;
    report['isHelped'] = report.helps.length > 0;
    report['hasSignificance'] = report.significances.length > 0;

    report['selfHelp'] = null;
    const selfHelp = report.helps.find((h) => h.helpedUser.id == user.id);
    if (selfHelp) {
      report['selfHelp'] = { id: selfHelp.id, status: selfHelp.status };
    }

    report['selfReview'] = null;
    const selfReview = report.reviews.find((r) => r.reviewerUser.id == user.id);
    if (selfReview) {
      report['selfReview'] = { id: selfReview.id, status: selfReview.status };
    }

    if (report.opinionReport) {
      report.opinionReport['isExist'] = financials.some((f) => f.opinionId);
    }
    if (report.industryReport) {
      report.industryReport['isExist'] = financials.some((f) => f.industryId);
    }
    if (report.tendencyReport) {
      report.tendencyReport['isExist'] = financials.some((f) => f.tendencyId);
    }

    delete report.createdUser;
    delete report.helps;
    delete report.significances;
    report.reviews.map((r) => delete r.reviewerUser);
    return { report: report, financials: financials };
  }

  async getCurrentFinancials(
    user: UserDto,
    dto: GetCurrentFinancialDto,
  ): Promise<{ report: any; financials: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let financialReport: FinancialReport;
    if (dto.reportId) {
      financialReport =
        await this.prismaService.financialReport.findFirstOrThrow({
          where: {
            id: dto.reportId,
            organizationId: dto.subsidiaryId,
            isCompleted: true,
            significances: { some: {} },
          },
        });
    } else {
      financialReport = await this.getLastReport(dto.subsidiaryId, {
        hasSignificance: true,
      });
      if (!financialReport) {
        return { report: null, financials: [] };
      }
    }
    const { report, financials } = await this.getFinancials(user, {
      reportId: financialReport.id,
    });
    return {
      report: report,
      financials: financials,
    };
  }

  async getReports(user: UserDto, dto: GetFinancialReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.financialReport.findMany({
      select: {
        id: true,
        name: true,
        code: true,
        isCompleted: true,
        expiredAt: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        significances: true,
      },
      where: {
        organizationId: dto.subsidiaryId,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
        significances:
          dto.hasSignificance === undefined
            ? undefined
            : dto.hasSignificance === true
              ? { some: {} }
              : { none: {} },
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      r['hasSignificance'] = r.significances.length > 0;
      delete r.createdUser;
      delete r.significances;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetFinancialReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        createdFinancialReports: {
          some: { id: dto.reportId, organizationId: dto.subsidiaryId },
        },
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetFinancialHelpDetailDto,
    help: FinancialHelp,
  ): Promise<{ report: any; help: any; financials: any[] }> {
    const report = await this.prismaService.financialReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        expiredAt: true,
        chainReport: { select: { id: true, name: true } },
      },
      where: { id: help.reportId },
    });
    const financials = await this.prismaService.financial.findMany({
      select: {
        id: true,
        opinionId: true,
        industryId: true,
        tendencyId: true,
        focusAreas: true,
        priorityIssue: true,
        subPriorityIssue: true,
        minorPriorityIssue: true,
        description: true,
        details: {
          select: {
            id: true,
            categoryDescription: true,
            categoryType: true,
            periodType: true,
            chainIds: true,
            countryIds: true,
            answers: {
              select: {
                severity: true,
                possibility: true,
                createdAt: true,
              },
              where: { helpId: help.id },
            },
          },
        },
      },
      where: {
        report: { id: help.reportId },
      },
    });
    for (const f of financials) {
      for (const d of f.details) {
        d['answer'] = d.answers.length > 0 ? d.answers[0] : null;
        delete d.answers;
      }
    }
    return {
      report: report,
      help: { id: help.id, status: help.status },
      financials: financials,
    };
  }

  async getReviewDetail(
    user: UserDto,
    dto: GetFinancialReviewDetailDto,
    review: FinancialReview,
  ) {
    const { report, financials } = await this.getFinancials(user, {
      reportId: review.reportId,
    });
    const createdUser = await this.prismaService.user.findFirstOrThrow({
      where: { id: review.createdUserId },
    });
    const createdUserInfo = plainToInstance(UserDataDto, createdUser, {
      excludeExtraneousValues: true,
    });
    return {
      review: {
        id: review.id,
        status: review.status,
        senderDescription: review.senderDescription,
        createdUserInfo: createdUserInfo,
        createdAt: review.createdAt,
      },
      report: report,
      financials: financials,
    };
  }

  private async getLastReport(
    organizationId: number,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      code?: number;
      isCompleted?: boolean;
      hasSignificance?: boolean;
    },
  ): Promise<FinancialReport> {
    const dbService = options?.tx ?? this.prismaService;
    const report = await dbService.financialReport.findFirst({
      where: {
        organizationId: organizationId,
        code: options?.code,
        isCompleted: options?.isCompleted,
        significances:
          options?.hasSignificance === undefined
            ? undefined
            : options?.hasSignificance === true
              ? { some: {} }
              : { none: {} },
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createReport(
    tx: Omit<PrismaClient, ITXClientDenyList>,
    user: UserDto,
    args: {
      opinionReport?: OpinionReport;
      industryReport?: IndustryReport;
      tendencyReport?: TendencyReport;
    },
  ): Promise<FinancialReport> {
    const argValues = Object.values(args).filter((id) => id);
    if (argValues.length != 1) {
      throw new BadRequestException('Rapor türetirken bir sorun oluştu.');
    }
    let code: number;
    if (args.opinionReport) {
      code = args.opinionReport.code;
    } else if (args.industryReport) {
      code = args.industryReport.code;
    } else if (args.tendencyReport) {
      code = args.tendencyReport.code;
    } else {
      throw new BadRequestException('Geçersiz türetilen rapor tipi.');
    }
    const chainReport = await this.chainService.getLastReport(
      user.organizationId,
      { isCompleted: true },
    );
    if (!chainReport) {
      throw new BadRequestException(
        'Öncelikle bir değer zinciri raporu tamamlamalısınız.',
      );
    }
    const lastUncompletedReport = await this.getLastReport(
      user.organizationId,
      {
        tx: tx,
        code: code,
        isCompleted: false,
      },
    );
    if (lastUncompletedReport) {
      if (
        (args.opinionReport && lastUncompletedReport.opinionReportId) ||
        (args.industryReport && lastUncompletedReport.industryReportId) ||
        (args.tendencyReport && lastUncompletedReport.tendencyReportId)
      ) {
        throw new BadRequestException(
          `${code} numaralı finansal önemlilik raporu zaten oluşturulmuş.`,
        );
      }
      await tx.financialReport.update({
        where: {
          id: lastUncompletedReport.id,
          organizationId: user.organizationId,
        },
        data: {
          opinionReportId: args.opinionReport?.id,
          industryReportId: args.industryReport?.id,
          tendencyReportId: args.tendencyReport?.id,
        },
      });
      return lastUncompletedReport;
    }
    const reportIsCompleted = await this.getLastReport(user.organizationId, {
      tx: tx,
      code: code,
      isCompleted: true,
    });
    if (reportIsCompleted) {
      throw new BadRequestException(
        `${code} numaralı finansal önemlilik raporu tamamlanmış.`,
      );
    }
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const createdReport = await tx.financialReport.create({
      data: {
        createdUserId: user.id,
        organizationId: user.organizationId,
        name: `FA_${code}_${dateFormat}`,
        code: code,
        chainReportId: chainReport.id,
        opinionReportId: args.opinionReport?.id,
        industryReportId: args.industryReport?.id,
        tendencyReportId: args.tendencyReport?.id,
      },
    });
    const effectedUserIds =
      await this.organizationUtilService.getOrganizationUserIds(user, {
        tx: tx,
      });
    await this.activityService.createActivity(tx, user, [
      {
        activityType: ActivityEnum.FINANCIAL_REPORT_CREATE,
        effectedUserIds: effectedUserIds,
        payload: { reportId: createdReport.id },
      },
    ]);
    return createdReport;
  }

  async createReportFromOpinion(
    user: UserDto,
    dto: CreateFinancialReportFromOpinionDto,
    opinionReport: OpinionReport,
    opinions: Opinion[],
  ): Promise<FinancialReport> {
    const report = await this.prismaService.$transaction(async (tx) => {
      const report = await this.createReport(tx, user, {
        opinionReport: opinionReport,
      });
      await tx.financial.createMany({
        data: opinions.map(
          (op) =>
            ({
              createdUserId: user.id,
              reportId: report.id,
              opinionId: op.id,
              focusAreas: [op.focusArea],
              priorityIssue: op.priorityIssue,
              subPriorityIssue: op.subPriorityIssue,
              minorPriorityIssue: op.minorPriorityIssue,
              description: op.description,
            }) as Prisma.FinancialCreateManyInput,
        ),
      });
      return report;
    });
    return report;
  }

  async createReportFromIndustry(
    user: UserDto,
    dto: CreateFinancialReportFromIndustryDto,
    industryReport: IndustryReport,
    industries: Industry[],
  ): Promise<FinancialReport> {
    const report = await this.prismaService.$transaction(async (tx) => {
      const report = await this.createReport(tx, user, {
        industryReport: industryReport,
      });
      await tx.financial.createMany({
        data: industries.map(
          (ind) =>
            ({
              createdUserId: user.id,
              reportId: report.id,
              industryId: ind.id,
              // focusAreas: [ind.focusArea],
              // priorityIssue: ind.priorityIssue,
              // subPriorityIssue: ind.priorityIssue,
              description: ind.description,
            }) as Prisma.FinancialCreateManyInput,
        ),
      });
      return report;
    });
    return report;
  }

  async createReportFromTendency(
    user: UserDto,
    dto: CreateFinancialReportFromTendencyDto,
    tendencyReport: TendencyReport,
    tendencies: Tendency[],
  ): Promise<FinancialReport> {
    const report = await this.prismaService.$transaction(async (tx) => {
      const report = await this.createReport(tx, user, {
        tendencyReport: tendencyReport,
      });
      await tx.financial.createMany({
        data: tendencies.map(
          (t) =>
            ({
              createdUserId: user.id,
              reportId: report.id,
              tendencyId: t.id,
              focusAreas: [t.focusArea],
              priorityIssue: t.topic,
              subPriorityIssue: t.topic,
              description: t.description,
            }) as Prisma.FinancialCreateManyInput,
        ),
      });
      return report;
    });
    return report;
  }

  async createHelp(user: UserDto, dto: CreateFinancialHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.financialHelp.createMany({
        data: dto.userIds.map(
          (helpedUserId) =>
            ({
              createdUserId: user.id,
              helpedUserId: helpedUserId,
              reportId: dto.reportId,
            }) as Prisma.FinancialHelpCreateManyInput,
        ),
      });
      const helps = await tx.financialHelp.findMany({
        where: { reportId: dto.reportId },
      });
      const activityArgs: CreateActivityArgType[] = [];
      for (const help of helps) {
        activityArgs.push({
          activityType: ActivityEnum.FINANCIAL_HELP,
          effectedUserIds: [help.helpedUserId],
          payload: { helpId: help.id },
        });
      }
      await this.activityService.createActivity(tx, user, activityArgs, {
        sendNotificationToMe: true,
      });
    });
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateFinancialHelpAnswerDto,
    help: FinancialHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.financialHelp.update({
        where: { id: help.id },
        data: { status: HelpStatusEnum.APPROVED },
      });
      await tx.financialHelpAnswer.createMany({
        data: dto.answers.map(
          (a) =>
            ({
              helpId: help.id,
              detailId: a.detailId,
              severity: a.severity,
              possibility: a.possibility,
            }) as Prisma.FinancialHelpAnswerCreateManyInput,
        ),
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.FINANCIAL_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async createReviews(user: UserDto, dto: CreateFinancialReviewDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.financialReview.createMany({
        data: dto.reviews.map(
          (r) =>
            ({
              createdUserId: user.id,
              reportId: dto.reportId,
              reviewerUserId: r.reviewerUserId,
              senderDescription: r.description,
            }) as Prisma.FinancialReviewCreateManyInput,
        ),
      });
      const reviews = await tx.financialReview.findMany({
        where: { reportId: dto.reportId },
      });
      const activityArgs: CreateActivityArgType[] = [];
      for (const review of reviews) {
        activityArgs.push({
          activityType: ActivityEnum.FINANCIAL_REVIEW,
          effectedUserIds: [review.reviewerUserId],
          payload: { reviewId: review.id },
        });
      }
      await this.activityService.createActivity(tx, user, activityArgs, {
        sendNotificationToMe: true,
      });
    });
  }

  async createReviewAnswer(
    user: UserDto,
    dto: CreateFinancialReviewAnswerDto,
    review: FinancialReview,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.financialReview.update({
        where: { id: review.id },
        data: { status: dto.status, reviewerDescription: dto.description },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.FINANCIAL_REVIEW_ANSWER,
          effectedUserIds: [review.createdUserId],
          payload: { reviewId: review.id },
        },
      ]);
    });
  }

  async updateFinancialDetail(user: UserDto, dto: UpdateFinancialDetailDto) {
    const createDetails = dto.details.filter((d) => !d.detailId);
    const updateDetails = dto.details.filter((d) => d.detailId && !d.isDelete);
    const deleteDetailIds = dto.details
      .filter((d) => d.detailId && d.isDelete)
      .map((d) => d.detailId);
    await this.prismaService.$transaction(async (tx) => {
      await tx.financialDetail.createMany({
        data: createDetails.map(
          (d) =>
            ({
              createdUserId: user.id,
              financialId: d.financialId,
              categoryDescription: d.categoryDescription,
              categoryType: d.categoryType,
              periodType: d.periodType,
              chainIds: d.chainIds,
              countryIds: d.countryIds,
            }) as Prisma.FinancialDetailCreateManyInput,
        ),
      });
      for (const updateDetail of updateDetails) {
        await tx.financialDetail.update({
          where: { id: updateDetail.detailId },
          data: {
            categoryDescription: updateDetail.categoryDescription,
            categoryType: updateDetail.categoryType,
            periodType: updateDetail.periodType,
            chainIds: updateDetail.chainIds,
            countryIds: updateDetail.countryIds,
          },
        });
      }
      await tx.financialDetail.deleteMany({
        where: { id: { in: deleteDetailIds } },
      });
    });
  }

  async updateReport(
    user: UserDto,
    dto: UpdateFinancialReportDto,
    report: FinancialReport,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.financialReport.update({
        where: { id: dto.reportId },
        data: {
          chainReportId: dto.chainReportId,
          isCompleted: dto.isCompleted ? true : undefined,
          expiredAt: dto.expiredAt,
        },
      });
      if (dto.chainReportId && dto.chainReportId != report.chainReportId) {
        await tx.financialDetail.updateMany({
          where: { financial: { reportId: dto.reportId } },
          data: { chainIds: Prisma.JsonNull },
        });
      }
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.FINANCIAL_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
    });
  }

  async resetReport(user: UserDto, dto: ResetFinancialReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.financialReview.deleteMany({
        where: { reportId: dto.reportId },
      });
      if (dto.resetType == MaterialityResetEnum.PRIORITY_RESET) {
        await tx.financialPriority.deleteMany({
          where: { reportId: dto.reportId },
        });
      } else if (dto.resetType == MaterialityResetEnum.HELP_RESET) {
        await tx.financialHelpAnswer.deleteMany({
          where: { help: { reportId: dto.reportId } },
        });
        await tx.financialHelp.deleteMany({
          where: { reportId: dto.reportId },
        });
        await tx.financialReport.update({
          where: { id: dto.reportId },
          data: { isCompleted: false },
        });
      }
    });
  }

  async updatePriority(user: UserDto, dto: UpdateFinancialPriorityDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.financialPriority.deleteMany({
        where: {
          reportId: dto.reportId,
          type: { notIn: dto.priorities.map((p) => p.type) },
        },
      });
      for (const p of dto.priorities) {
        const priority = await tx.financialPriority.findFirst({
          where: { reportId: dto.reportId, type: p.type },
        });
        await tx.financialPriority.upsert({
          where: { id: priority?.id ?? 0 },
          create: {
            createdUserId: user.id,
            reportId: dto.reportId,
            minValue: p.minValue,
            maxValue: p.maxValue,
            type: p.type,
          },
          update: {
            createdUserId: user.id,
            minValue: p.minValue,
            maxValue: p.maxValue,
          },
        });
      }
    });
  }

  async deleteReport(user: UserDto, dto: DeleteFinancialReportDto) {
    await this.prismaService.financialReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }
}
