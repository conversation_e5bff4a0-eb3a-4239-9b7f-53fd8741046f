import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteImpactReportDto } from '../dtos/impact/delete-impact-report.dto';
import { UpdateImpactReportDto } from '../dtos/impact/update-impact-report.dto';
import { CreateImpactHelpDto } from '../dtos/impact/create-impact-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { CreateImpactHelpAnswerDto } from '../dtos/impact/create-impact-help-answer.dto';
import {
  ImpactHelp,
  ImpactReport,
  ImpactReview,
  Industry,
  IndustryReport,
  Opinion,
  OpinionReport,
  Tendency,
  TendencyReport,
} from '@prisma/client';
import { GetImpactReportDto } from '../dtos/impact/get-impact-report.dto';
import { GetImpactReportUserDto } from '../dtos/impact/get-impact-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { UpdateImpactDetailDto } from '../dtos/impact/update-impact-detail.dto';
import { GetImpactHelpDetailDto } from '../dtos/impact/get-impact-help-detail.dto';
import { GetCurrentImpactDto } from '../dtos/impact/get-current-impact.dto';
import { CreateImpactReportFromOpinionDto } from '../dtos/impact/create-impact-report-from-opinion.dto';
import {
  ImpactPriorityDto,
  UpdateImpactPriorityDto,
} from '../dtos/impact/update-impact-priority.dto';
import { CreateImpactReviewDto } from '../dtos/impact/create-impact-review.dto';
import { ReviewStatusEnum } from '../enums/review-status.enum';
import { CreateImpactReviewAnswerDto } from '../dtos/impact/create-impact-review-answer.dto';
import { GetImpactReviewDetailDto } from '../dtos/impact/get-impact-review-detail.dto';
import { CreateImpactReportFromIndustryDto } from '../dtos/impact/create-impact-report-from-industry.dto';
import { CreateImpactReportFromTendencyDto } from '../dtos/impact/create-impact-report-from-tendency.dto';
import { UserService } from '../../common/services/user.service';
import { ImpactCategoryTypeEnum } from '../enums/impact-category-type.enum';
import { ImpactRealityTypeEnum } from '../enums/impact-reality-type.enum';
import { PriorityTypeEnum } from '../enums/priority-type.enum';
import { ResetImpactReportDto } from '../dtos/impact/reset-impact-report.dto';

@Injectable()
export class ImpactValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private userService: UserService,
  ) {}

  private reportCompletedCheck(report: ImpactReport): void {
    if (report.isCompleted) {
      throw new BadRequestException(
        'Etki analiz raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async getCurrentImpacts(
    user: UserDto,
    dto: GetCurrentImpactDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetImpactReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetImpactReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetImpactHelpDetailDto,
  ): Promise<ImpactHelp> {
    const help = await this.prismaService.impactHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        report: { organizationId: user.organizationId },
      },
    });
    return help;
  }

  async getReviewDetail(
    user: UserDto,
    dto: GetImpactReviewDetailDto,
  ): Promise<ImpactReview> {
    const review = await this.prismaService.impactReview.findFirstOrThrow({
      where: {
        id: dto.reviewId,
        reviewerUserId: user.id,
        report: { organizationId: user.organizationId },
      },
    });
    return review;
  }

  async createReportFromOpinion(
    user: UserDto,
    dto: CreateImpactReportFromOpinionDto,
  ): Promise<{ opinionReport: OpinionReport; opinions: Opinion[] }> {
    await this.userService.checkPassword(user, dto.password);
    const opinionReport =
      await this.prismaService.opinionReport.findFirstOrThrow({
        where: {
          id: dto.opinionReportId,
          organizationId: user.organizationId,
          isCompleted: true,
        },
        include: {
          opinions: {
            where: { isSelected: true },
            include: { helpAnswers: true },
          },
          impactReports: true,
        },
      });
    if (opinionReport.impactThreshold === null) {
      throw new BadRequestException('Etki eşik değeri belirtilmemiş.');
    }
    const opinions = opinionReport.opinions.filter((op) => {
      const impactPoint =
        op.helpAnswers.reduce(
          (sum, answer) => sum + answer.impactQuestionAnswer,
          0,
        ) / (op.helpAnswers.length == 0 ? 1 : op.helpAnswers.length);
      return impactPoint >= opinionReport.impactThreshold;
    });
    if (opinions.length == 0) {
      throw new BadRequestException(
        'Etki analiz raporu oluşturmak için eşik değerini geçen paydaş görüşü bulunamadı.',
      );
    }
    if (opinionReport.impactReports.length > 0) {
      throw new BadRequestException(
        'Paydaş görüşleri için etki analiz raporu zaten oluşturuldu.',
      );
    }
    return { opinionReport: opinionReport, opinions: opinions };
  }

  async createReportFromIndustry(
    user: UserDto,
    dto: CreateImpactReportFromIndustryDto,
  ): Promise<{ industryReport: IndustryReport; industries: Industry[] }> {
    // await this.userService.checkPassword(user, dto.password);
    // const industryReport =
    //   await this.prismaService.industryReport.findFirstOrThrow({
    //     where: {
    //       id: dto.industryReportId,
    //       organizationId: user.organizationId,
    //       isCompleted: true,
    //     },
    //     include: {
    //       industries: {
    //         where: { isSelected: true },
    //         include: { helpAnswers: true },
    //       },
    //       impactReports: true,
    //     },
    //   });
    // if (industryReport.impactThreshold === null) {
    //   throw new BadRequestException('Etki eşik değeri belirtilmemiş.');
    // }
    // const industries = industryReport.industries.filter((ind) => {
    //   const impactPoint =
    //     ind.helpAnswers.reduce(
    //       (sum, answer) => sum + answer.impactQuestionAnswer,
    //       0,
    //     ) / (ind.helpAnswers.length == 0 ? 1 : ind.helpAnswers.length);
    //   return impactPoint >= industryReport.impactThreshold;
    // });
    // if (industries.length == 0) {
    //   throw new BadRequestException(
    //     'Etki analiz raporu oluşturmak için eşik değerini geçen sektör analizi görüşü bulunamadı.',
    //   );
    // }
    // if (industryReport.impactReports.length > 0) {
    //   throw new BadRequestException(
    //     'Sektör analizi için etki analiz raporu zaten oluşturuldu.',
    //   );
    // }
    // return { industryReport: industryReport, industries: industries };
    return { industryReport: null, industries: [] };
  }

  async createReportFromTendency(
    user: UserDto,
    dto: CreateImpactReportFromTendencyDto,
  ): Promise<{ tendencyReport: TendencyReport; tendencies: Tendency[] }> {
    await this.userService.checkPassword(user, dto.password);
    const tendencyReport =
      await this.prismaService.tendencyReport.findFirstOrThrow({
        where: {
          id: dto.tendencyReportId,
          organizationId: user.organizationId,
          isCompleted: true,
        },
        include: {
          tendencies: {
            include: { helpAnswers: true },
          },
          impactReports: true,
        },
      });
    if (tendencyReport.impactThreshold === null) {
      throw new BadRequestException('Etki eşik değeri belirtilmemiş.');
    }
    const tendencies = tendencyReport.tendencies.filter((t) => {
      const impactPoint =
        t.helpAnswers.reduce(
          (sum, answer) => sum + answer.impactQuestionAnswer,
          0,
        ) / (t.helpAnswers.length == 0 ? 1 : t.helpAnswers.length);
      return impactPoint >= tendencyReport.impactThreshold;
    });
    if (tendencies.length == 0) {
      throw new BadRequestException(
        'Etki analiz raporu oluşturmak için eşik değerini geçen trend analizi görüşü bulunamadı.',
      );
    }
    if (tendencyReport.impactReports.length > 0) {
      throw new BadRequestException(
        'Trend analizi için etki analiz raporu zaten oluşturuldu.',
      );
    }
    return { tendencyReport: tendencyReport, tendencies: tendencies };
  }

  async createHelp(user: UserDto, dto: CreateImpactHelpDto): Promise<void> {
    const report = await this.prismaService.impactReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
    });
    this.reportCompletedCheck(report);
    const userOrganizationsCount =
      await this.prismaService.userOrganization.count({
        where: {
          userId: { in: [...new Set(dto.userIds)] },
          organizationId: user.organizationId,
        },
      });
    if (userOrganizationsCount !== dto.userIds.length) {
      throw new BadRequestException('Geçersiz kullanıcılar.');
    }
    const impactHelpsCount = await this.prismaService.impactHelp.count({
      where: { reportId: report.id },
    });
    if (impactHelpsCount > 0) {
      throw new BadRequestException(
        'Bu rapor için zaten atama oluşturulmuştur.',
      );
    }
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateImpactHelpAnswerDto,
  ): Promise<ImpactHelp> {
    const help = await this.prismaService.impactHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        status: HelpStatusEnum.PENDING,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(help.report);
    if (help.report.expiredAt && help.report.expiredAt < new Date()) {
      throw new BadRequestException(
        'Rapor süresi dolduğu için işlem yapılamaz.',
      );
    }
    const detailIds = dto.answers.map((a) => a.detailId);
    const details = await this.prismaService.impactDetail.findMany({
      where: {
        id: { in: [...new Set(detailIds)] },
        impact: { reportId: help.report.id },
      },
    });
    if (details.length !== detailIds.length) {
      throw new BadRequestException('Geçersiz etki analiz detay cevapları.');
    }
    for (const d of details) {
      const answer = dto.answers.find((a) => a.detailId == d.id);
      if (
        d.categoryType == ImpactCategoryTypeEnum.POSITIVE &&
        answer.reparation
      ) {
        delete answer.reparation;
      } else if (
        d.categoryType == ImpactCategoryTypeEnum.NEGATIVE &&
        !answer.reparation
      ) {
        throw new BadRequestException('Onarılabilirlik değeri giriniz.');
      }

      if (d.realityType == ImpactRealityTypeEnum.REAL && answer.possibility) {
        delete answer.possibility;
      } else if (
        d.realityType == ImpactRealityTypeEnum.POTENTIAL &&
        !answer.possibility
      ) {
        throw new BadRequestException('Olasılık değeri giriniz.');
      }
    }
    return help;
  }

  async createReviews(
    user: UserDto,
    dto: CreateImpactReviewDto,
  ): Promise<void> {
    await this.prismaService.impactReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isCompleted: true,
      },
    });
    const reviewerUserIds = dto.reviews.map((r) => r.reviewerUserId);
    const userOrgCount = await this.prismaService.userOrganization.count({
      where: {
        userId: { in: [...new Set(reviewerUserIds)] },
        organizationId: user.organizationId,
      },
    });
    if (userOrgCount !== reviewerUserIds.length) {
      throw new BadRequestException('Geçersiz kullanıcılar.');
    }
    const pendingReviews = await this.prismaService.impactReview.findMany({
      where: {
        reviewerUserId: { in: [...new Set(reviewerUserIds)] },
        reportId: dto.reportId,
        status: ReviewStatusEnum.PENDING,
      },
      include: { reviewerUser: { select: { name: true, surname: true } } },
    });
    if (pendingReviews.length > 0) {
      const pendingReviewerFullNames = pendingReviews.map(
        (pr) => `${pr.reviewerUser.name} ${pr.reviewerUser.surname}`,
      );
      throw new BadRequestException(
        `${pendingReviewerFullNames.join(', ')} adlı kullanıcı/kullanıcılar için zaten yönetici onayı durumu beklemededir.`,
      );
    }
  }

  async createReviewAnswer(
    user: UserDto,
    dto: CreateImpactReviewAnswerDto,
  ): Promise<ImpactReview> {
    const review = await this.prismaService.impactReview.findFirstOrThrow({
      where: {
        id: dto.reviewId,
        reviewerUserId: user.id,
        report: { organizationId: user.organizationId },
        status: ReviewStatusEnum.PENDING,
      },
    });
    return review;
  }

  async updateImpactDetail(
    user: UserDto,
    dto: UpdateImpactDetailDto,
  ): Promise<void> {
    const report = await this.prismaService.impactReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
    for (const detail of dto.details) {
      if (detail.isDelete && !detail.detailId) {
        throw new BadRequestException('Geçersiz detay silme işlemi.');
      }
    }
    const impactIds = [...new Set(dto.details.map((d) => d.impactId))];
    const impactCount = await this.prismaService.impact.count({
      where: {
        id: { in: impactIds },
        reportId: report.id,
      },
    });
    if (impactCount != impactIds.length) {
      throw new BadRequestException('Geçersiz etki analiz verileri.');
    }
    // Chain ids validation
    const chainIds = [
      ...new Set(
        dto.details
          .filter((d) => d.chainIds)
          .map((d) => d.chainIds)
          .flat(1),
      ),
    ];
    if (chainIds.length > 0) {
      if (!report.chainReportId) {
        throw new BadRequestException(
          'Değer zincir raporu seçmeniz gerekmektedir.',
        );
      }
      const chainCount = await this.prismaService.chain.count({
        where: {
          id: { in: chainIds },
          reportId: report.chainReportId,
        },
      });
      if (chainCount != chainIds.length) {
        throw new BadRequestException('Geçersiz değer zinciri verileri.');
      }
    }
    // Country ids validation
    const countryIds = [
      ...new Set(
        dto.details
          .filter((d) => d.countryIds)
          .map((d) => d.countryIds)
          .flat(1),
      ),
    ];
    if (countryIds.length > 0) {
      const countryCount = await this.prismaService.country.count({
        where: {
          id: { in: countryIds },
          organizationCountries: {
            some: { organizationId: user.organizationId },
          },
        },
      });
      if (countryCount != countryIds.length) {
        throw new BadRequestException('Geçersiz coğrafya verileri.');
      }
    }
    // Update or Delete validation
    const updateOrDeleteDetails = dto.details.filter((d) => d.detailId);
    const detailCount = await this.prismaService.impactDetail.count({
      where: {
        OR: updateOrDeleteDetails.map((d) => ({
          id: d.detailId,
          impactId: d.impactId,
        })),
        impact: {
          report: { id: report.id, organizationId: user.organizationId },
        },
      },
    });
    if (detailCount != updateOrDeleteDetails.length) {
      throw new BadRequestException('Geçersiz detay verileri.');
    }
  }

  async updateReport(
    user: UserDto,
    dto: UpdateImpactReportDto,
  ): Promise<ImpactReport> {
    const report = await this.prismaService.impactReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { impacts: true, helps: { include: { answers: true } } },
    });
    this.reportCompletedCheck(report);
    if (dto.chainReportId) {
      await this.prismaService.chainReport.findFirstOrThrow({
        where: { id: dto.chainReportId, organizationId: user.organizationId },
      });
    }
    if (dto.expiredAt && dto.expiredAt <= new Date()) {
      throw new BadRequestException('Geçersiz son tarih.');
    }
    if (dto.isCompleted) {
      if (new Date() < report.expiredAt) {
        if (dto.password) {
          await this.userService.checkPassword(user, dto.password);
        } else {
          throw new BadRequestException('Şifre girilmesi zorunludur.');
        }
      }
      const answers = report.helps.map((h) => h.answers).flat(1);
      if (answers.length == 0) {
        throw new BadRequestException(
          'Sorulara atanan kişilerden en az biri cevap vermediği için rapor tamamlanamaz.',
        );
      }
    }
    return report;
  }

  async resetReport(user: UserDto, dto: ResetImpactReportDto) {
    await this.prismaService.impactReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isCompleted: true,
        reviews: { some: { status: ReviewStatusEnum.DECLINED } },
      },
    });
  }

  async updatePriority(
    user: UserDto,
    dto: UpdateImpactPriorityDto,
  ): Promise<void> {
    await this.prismaService.impactReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isCompleted: true,
      },
    });
    const priorityTypes = dto.priorities.map((p) => p.type);
    if (priorityTypes.length != new Set(priorityTypes).size) {
      throw new BadRequestException(
        'Aynı tip öncelikten birden fazla giremezsiniz.',
      );
    }
    let low: ImpactPriorityDto;
    let medium: ImpactPriorityDto;
    let high: ImpactPriorityDto;
    for (const p of dto.priorities) {
      if (p.minValue > p.maxValue) {
        throw new BadRequestException('Geçersiz öncelik aralığı.');
      }
      if (p.type == PriorityTypeEnum.LOW) {
        low = p;
      } else if (p.type == PriorityTypeEnum.MEDIUM) {
        medium = p;
      } else if (p.type == PriorityTypeEnum.HIGH) {
        high = p;
      }
    }
    if (low && medium) {
      if (low.maxValue > medium.minValue) {
        throw new BadRequestException(
          'Geçersiz düşük ve orta öncelik aralığı.',
        );
      }
    }
    if (low && high) {
      if (low.maxValue > high.minValue) {
        throw new BadRequestException(
          'Geçersiz düşük ve yüksek öncelik aralığı.',
        );
      }
    }
    if (medium && high) {
      if (medium.maxValue > high.minValue) {
        throw new BadRequestException(
          'Geçersiz orta ve yüksek öncelik aralığı.',
        );
      }
    }
  }

  async deleteReport(user: UserDto, dto: DeleteImpactReportDto): Promise<void> {
    const report = await this.prismaService.impactReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
  }
}
