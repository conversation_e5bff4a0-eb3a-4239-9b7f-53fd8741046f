import { BadRequestException, Injectable } from '@nestjs/common';
import { UserDto } from '../../common/dtos/user.dto';
import { GetCurrentInsightDto } from '../dtos/insight/get-current-insight.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetSignificanceGraphDto } from '../dtos/insight/get-significance-graph.dto';
import { PrismaService } from '../../common/services/prisma.service';
import { FinancialService } from '../services/financial.service';
import { ImpactService } from '../services/impact.service';

@Injectable()
export class InsightValidation {
  constructor(
    private prismaService: PrismaService,
    private financialService: FinancialService,
    private impactService: ImpactService,
    private organizationService: OrganizationService,
  ) {}

  async getCurrents(user: UserDto, dto: GetCurrentInsightDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getSignificanceGraphs(
    user: UserDto,
    dto: GetSignificanceGraphDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    } else {
      dto.subsidiaryId = user.organizationId;
    }

    if (dto.financialReportId) {
      await this.prismaService.financialReport.findFirstOrThrow({
        where: {
          id: dto.financialReportId,
          organizationId: dto.subsidiaryId,
          significances: { some: {} },
        },
      });
    } else {
      const financialReports = await this.financialService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        hasSignificance: true,
      });
      if (financialReports.length == 0) {
        throw new BadRequestException(
          'Finansal önemlilik tablosu oluşturunuz.',
        );
      }
      dto.financialReportId = financialReports[0].id;
    }

    if (dto.impactReportId) {
      await this.prismaService.impactReport.findFirstOrThrow({
        where: {
          id: dto.impactReportId,
          organizationId: dto.subsidiaryId,
          significances: { some: {} },
        },
      });
    } else {
      const impactReports = await this.impactService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        hasSignificance: true,
      });
      if (impactReports.length == 0) {
        throw new BadRequestException('Etki önemlilik tablosu oluşturunuz.');
      }
      dto.impactReportId = impactReports[0].id;
    }
  }
}
