import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteOpinionReportDto } from '../dtos/opinion/delete-opinion-report.dto';
import { UpdateOpinionDto } from '../dtos/opinion/update-opinion.dto';
import { DeleteOpinionDto } from '../dtos/opinion/delete-opinion.dto';
import { UpdateOpinionReportDto } from '../dtos/opinion/update-opinion-report.dto';
import { CreateOpinionHelpDto } from '../dtos/opinion/create-opinion-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { CreateOpinionHelpAnswerDto } from '../dtos/opinion/create-opinion-help-answer.dto';
import { OpinionHelp, OpinionReport, StakeholderUser } from '@prisma/client';
import { GetOpinionReportDto } from '../dtos/opinion/get-opinion-report.dto';
import { GetOpinionReportUserDto } from '../dtos/opinion/get-opinion-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { CreateOpinionDto } from '../dtos/opinion/create-opinion.dto';
import { GetOpinionHelpDto } from '../dtos/opinion/get-opinion-help.dto';
import { GetOpinionHelpDetailDto } from '../dtos/opinion/get-opinion-help-detail.dto';
import { GetCurrentOpinionDto } from '../dtos/opinion/get-current-opinion.dto';
import { UserService } from '../../common/services/user.service';
import { CompleteOpinionHelpAnswerDto } from '../dtos/opinion/complete-opinion-help-answer.dto';

@Injectable()
export class OpinionValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private userService: UserService,
  ) {}

  private reportCompletedCheck(report: OpinionReport): void {
    if (report.isCompleted) {
      throw new BadRequestException(
        'Paydaş görüşleri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async getCurrentOpinions(
    user: UserDto,
    dto: GetCurrentOpinionDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetOpinionReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetOpinionReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getHelps(user: UserDto, dto: GetOpinionHelpDto): Promise<void> {
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    await this.prismaService.opinionReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
  }

  async getHelpDetail(dto: GetOpinionHelpDetailDto): Promise<OpinionHelp> {
    const help = await this.prismaService.opinionHelp.findFirst({
      where: { token: dto.token },
    });
    if (!help) {
      throw new BadRequestException('Soru bulunamadı.');
    }
    return help;
  }

  async createOpinion(user: UserDto, dto: CreateOpinionDto): Promise<void> {
    const report = await this.prismaService.opinionReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
  }

  async createHelp(
    user: UserDto,
    dto: CreateOpinionHelpDto,
  ): Promise<StakeholderUser[]> {
    const report = await this.prismaService.opinionReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
      include: { opinions: { where: { isSelected: true } } },
    });
    this.reportCompletedCheck(report);
    if (report.opinions.length == 0) {
      throw new BadRequestException('Seçilmiş paydaş görüşü bulunamadı.');
    }
    for (const su of dto.stakeholderUsers) {
      if (!su.isFinancial && !su.isImpact) {
        throw new BadRequestException(
          'Finansal önemlilik ve/veya etki analizi ataması seçilmelidir.',
        );
      }
    }
    const stakeholderUserIds = dto.stakeholderUsers.map((su) => su.id);
    const stakeholderUsers = await this.prismaService.stakeholderUser.findMany({
      where: {
        id: { in: [...new Set(stakeholderUserIds)] },
        stakeholder: { organizationId: user.organizationId },
      },
    });
    if (stakeholderUsers.length !== stakeholderUserIds.length) {
      throw new BadRequestException('Geçersiz paydaş kullanıcıları.');
    }
    const helps = await this.prismaService.opinionHelp.findMany({
      where: { reportId: report.id },
    });
    const financialHelps = helps.filter((h) => h.isFinancial);
    const impactHelps = helps.filter((h) => h.isImpact);
    const isFinancial = dto.stakeholderUsers.some((su) => su.isFinancial);
    const isImpact = dto.stakeholderUsers.some((su) => su.isImpact);
    if (isFinancial && financialHelps.length > 0) {
      throw new BadRequestException(
        'Bu rapor için zaten finansal önemlilik ataması oluşturulmuştur.',
      );
    }
    if (isImpact && impactHelps.length > 0) {
      throw new BadRequestException(
        'Bu rapor için zaten etki analizi ataması oluşturulmuştur.',
      );
    }
    return stakeholderUsers;
  }

  async createHelpAnswer(
    dto: CreateOpinionHelpAnswerDto,
  ): Promise<OpinionHelp> {
    const help = await this.prismaService.opinionHelp.findFirstOrThrow({
      where: {
        OR: [
          { financialStatus: HelpStatusEnum.PENDING },
          { impactStatus: HelpStatusEnum.PENDING },
        ],
        token: dto.token,
      },
      include: { report: true },
    });
    this.reportCompletedCheck(help.report);
    if (help.report.expiredAt && help.report.expiredAt < new Date()) {
      throw new BadRequestException(
        'Rapor süresi dolduğu için işlem yapılamaz.',
      );
    }
    const opinionIds = dto.answers.map((a) => a.opinionId);
    const opinionCount = await this.prismaService.opinion.count({
      where: {
        id: { in: [...new Set(opinionIds)] },
        reportId: help.reportId,
        isSelected: true,
      },
    });
    if (opinionCount !== opinionIds.length) {
      throw new BadRequestException('Geçersiz paydaş görüş cevapları.');
    }
    const financialAnswers = dto.answers.filter(
      (a) => a.financialQuestionAnswer,
    );
    const impactAnswers = dto.answers.filter((a) => a.impactQuestionAnswer);
    if (help.isFinancial) {
      if (help.financialStatus) {
        throw new BadRequestException(
          'Finansal önemlilik cevaplarını zaten tamamladınız.',
        );
      }
    } else {
      if (financialAnswers.length > 0) {
        throw new BadRequestException(
          'Bu rapora finansal önemlilik cevabı veremezsiniz.',
        );
      }
    }
    if (help.isImpact) {
      if (help.impactStatus) {
        throw new BadRequestException(
          'Etki analizi cevaplarını zaten tamamladınız.',
        );
      }
    } else {
      if (impactAnswers.length > 0) {
        throw new BadRequestException(
          'Bu rapora etki analizi cevabı veremezsiniz.',
        );
      }
    }
    return help;
  }

  async completeHelpAnswer(
    dto: CompleteOpinionHelpAnswerDto,
  ): Promise<OpinionHelp> {
    const help = await this.prismaService.opinionHelp.findFirstOrThrow({
      where: {
        OR: [
          { financialStatus: HelpStatusEnum.PENDING },
          { impactStatus: HelpStatusEnum.PENDING },
        ],
        token: dto.token,
      },
      include: { report: true, answers: true },
    });
    this.reportCompletedCheck(help.report);
    if (help.report.expiredAt && help.report.expiredAt < new Date()) {
      throw new BadRequestException(
        'Rapor süresi dolduğu için işlem yapılamaz.',
      );
    }
    const opinions = await this.prismaService.opinion.findMany({
      where: {
        reportId: help.reportId,
        isSelected: true,
      },
    });
    if (help.isFinancial) {
      if (help.financialStatus) {
        throw new BadRequestException(
          'Finansal önemlilik cevaplarını zaten tamamladınız.',
        );
      } else {
        const answeredOpinionIds = help.answers
          .filter((a) => a.financialQuestionAnswer)
          .map((a) => a.opinionId);
        const isMissing = opinions.some(
          (t) => !answeredOpinionIds.includes(t.id),
        );
        if (isMissing) {
          throw new BadRequestException(
            'Finansal önemlilik sorularının cevapları eksik.',
          );
        }
      }
    }
    if (help.isImpact) {
      if (help.impactStatus) {
        throw new BadRequestException(
          'Etki analizi cevaplarını zaten tamamladınız.',
        );
      } else {
        const answeredOpinionIds = help.answers
          .filter((a) => a.impactQuestionAnswer)
          .map((a) => a.opinionId);
        const isMissing = opinions.some(
          (t) => !answeredOpinionIds.includes(t.id),
        );
        if (isMissing) {
          throw new BadRequestException(
            'Etki analizi sorularının cevapları eksik.',
          );
        }
      }
    }
    return help;
  }

  async updateOpinion(user: UserDto, dto: UpdateOpinionDto): Promise<void> {
    const opinion = await this.prismaService.opinion.findFirstOrThrow({
      where: {
        id: dto.opinionId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(opinion.report);
    if (
      opinion.isDefault &&
      (dto.focusArea ||
        dto.priorityIssue ||
        dto.subPriorityIssue ||
        dto.minorPriorityIssue)
    ) {
      throw new BadRequestException(
        'Sistem tarafından atanan varsayılan alanlar güncellenemez.',
      );
    }
  }

  async updateReport(
    user: UserDto,
    dto: UpdateOpinionReportDto,
  ): Promise<void> {
    const report = await this.prismaService.opinionReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: {
        opinions: true,
        financialReports: true,
        impactReports: true,
        helps: { include: { answers: true } },
      },
    });
    if (report.isReady) {
      throw new BadRequestException(
        'Rapor hazır hale getirildiği için işlem yapılamaz.',
      );
    }
    if (
      dto.selectedOpinionIds !== undefined ||
      dto.isCompleted !== undefined ||
      dto.expiredAt !== undefined
    ) {
      this.reportCompletedCheck(report);
    }
    if (report.isReady && (dto.financialThreshold || dto.impactThreshold)) {
      throw new BadRequestException(
        'Rapor hazır hale getirildiği için eşik değerleri değiştirilemez.',
      );
    }
    if (dto.expiredAt && dto.expiredAt <= new Date()) {
      throw new BadRequestException('Geçersiz son tarih.');
    }
    if (dto.selectedOpinionIds) {
      const opinionCount = await this.prismaService.opinion.count({
        where: {
          id: { in: [...new Set(dto.selectedOpinionIds)] },
          reportId: report.id,
        },
      });
      if (opinionCount !== dto.selectedOpinionIds.length) {
        throw new BadRequestException('Geçersiz paydaş görüşleri seçimi.');
      }
    }
    if (dto.isCompleted) {
      if (new Date() < report.expiredAt) {
        if (dto.password) {
          await this.userService.checkPassword(user, dto.password);
        } else {
          throw new BadRequestException('Şifre girilmesi zorunludur.');
        }
      }
      const answers = report.helps.map((h) => h.answers).flat(1);
      if (answers.length == 0) {
        throw new BadRequestException(
          'Sorulara atanan kişilerden en az biri cevap vermediği için rapor tamamlanamaz.',
        );
      }
    }
    if (dto.isReady) {
      if (
        report.financialThreshold === undefined &&
        report.impactThreshold === undefined
      ) {
        throw new BadRequestException(
          'Eşik değerleri tanımlanmadığı için rapor hazır hale getirilemez.',
        );
      }
      if (dto.password) {
        await this.userService.checkPassword(user, dto.password);
      } else {
        throw new BadRequestException('Şifre girilmesi zorunludur.');
      }
    }
  }

  async deleteOpinion(user: UserDto, dto: DeleteOpinionDto): Promise<void> {
    const opinion = await this.prismaService.opinion.findFirstOrThrow({
      where: {
        id: dto.opinionId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(opinion.report);
  }

  async deleteReport(
    user: UserDto,
    dto: DeleteOpinionReportDto,
  ): Promise<void> {
    const report = await this.prismaService.opinionReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
  }
}
