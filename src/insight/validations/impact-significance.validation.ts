import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { ImpactReport } from '@prisma/client';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetCurrentImpactSignificanceDto } from '../dtos/impact-significance/get-current-impact-significance.dto';
import { GetImpactSignificanceReportDto } from '../dtos/impact-significance/get-impact-significance-report.dto';
import { GetImpactSignificanceReportUserDto } from '../dtos/impact-significance/get-impact-significance-report-user.dto';
import { CreateImpactSignificanceReportDto } from '../dtos/impact-significance/create-impact-significance-report.dto';

@Injectable()
export class ImpactSignificanceValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
  ) {}

  async getCurrentSignificances(
    user: UserDto,
    dto: GetCurrentImpactSignificanceDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(
    user: UserDto,
    dto: GetImpactSignificanceReportDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetImpactSignificanceReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async createReport(
    user: UserDto,
    dto: CreateImpactSignificanceReportDto,
  ): Promise<ImpactReport> {
    const report = await this.prismaService.impactReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isCompleted: true,
      },
      include: { significances: true },
    });
    if (report.significances.length > 0) {
      throw new BadRequestException(
        'Bu rapor için önemlilik tablosu zaten oluşturulmuştur.',
      );
    }
    return report;
  }
}
