import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { FinancialReport } from '@prisma/client';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetCurrentFinancialSignificanceDto } from '../dtos/financial-significance/get-current-financial-significance.dto';
import { GetFinancialSignificanceReportDto } from '../dtos/financial-significance/get-financial-significance-report.dto';
import { GetFinancialSignificanceReportUserDto } from '../dtos/financial-significance/get-financial-significance-report-user.dto';
import { CreateFinancialSignificanceReportDto } from '../dtos/financial-significance/create-financial-significance-report.dto';

@Injectable()
export class FinancialSignificanceValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
  ) {}

  async getCurrentSignificances(
    user: UserDto,
    dto: GetCurrentFinancialSignificanceDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(
    user: UserDto,
    dto: GetFinancialSignificanceReportDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetFinancialSignificanceReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async createReport(
    user: UserDto,
    dto: CreateFinancialSignificanceReportDto,
  ): Promise<FinancialReport> {
    const report = await this.prismaService.financialReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isCompleted: true,
      },
      include: { significances: true },
    });
    if (report.significances.length > 0) {
      throw new BadRequestException(
        'Bu rapor için önemlilik tablosu zaten oluşturulmuştur.',
      );
    }
    return report;
  }
}
