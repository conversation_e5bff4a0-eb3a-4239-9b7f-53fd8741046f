import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteIndustryReportDto } from '../dtos/industry/delete-industry-report.dto';
import { UpdateIndustryDto } from '../dtos/industry/update-industry.dto';
import { DeleteIndustryDto } from '../dtos/industry/delete-industry.dto';
import { UpdateIndustryReportDto } from '../dtos/industry/update-industry-report.dto';
import { CreateIndustryHelpDto } from '../dtos/industry/create-industry-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { CreateIndustryHelpAnswerDto } from '../dtos/industry/create-industry-help-answer.dto';
import { IndustryHelp, IndustryReport } from '@prisma/client';
import { GetIndustryReportDto } from '../dtos/industry/get-industry-report.dto';
import { GetIndustryReportUserDto } from '../dtos/industry/get-industry-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { CreateIndustryDto } from '../dtos/industry/create-industry.dto';
import { GetIndustryHelpDetailDto } from '../dtos/industry/get-industry-help-detail.dto';
import { GetCurrentIndustryDto } from '../dtos/industry/get-current-industry.dto';
import { UserService } from '../../common/services/user.service';
import { CompleteIndustryHelpAnswerDto } from '../dtos/industry/complete-industry-help-answer.dto';

@Injectable()
export class IndustryValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private userService: UserService,
  ) {}

  private reportCompletedCheck(report: IndustryReport): void {
    if (report.isCompleted) {
      throw new BadRequestException(
        'Sektör analizi raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async getCurrentIndustries(
    user: UserDto,
    dto: GetCurrentIndustryDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetIndustryReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetIndustryReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetIndustryHelpDetailDto,
  ): Promise<IndustryHelp> {
    const help = await this.prismaService.industryHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        report: { organizationId: user.organizationId },
      },
    });
    return help;
  }

  async createIndustry(user: UserDto, dto: CreateIndustryDto): Promise<void> {
    const report = await this.prismaService.industryReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
    const sectorCount = await this.prismaService.sector.count({
      where: { id: { in: dto.sectorIds } },
    });
    if (sectorCount !== dto.sectorIds.length) {
      throw new BadRequestException('Geçersiz sektörler');
    }
    if (dto.sourceIds && dto.sourceText) {
      throw new BadRequestException(
        'source ids and source text cannot be used together',
      );
    }
    if (!dto.sourceIds && !dto.sourceText) {
      throw new BadRequestException(
        'source ids or source text must be provided',
      );
    }
    if (dto.sourceIds) {
      const sourceCount = await this.prismaService.sectorSource.count({
        where: { id: { in: dto.sourceIds } },
      });
      if (sourceCount !== dto.sourceIds.length) {
        throw new BadRequestException('Geçersiz kaynaklar');
      }
    }
  }

  async createHelp(user: UserDto, dto: CreateIndustryHelpDto): Promise<void> {
    const report = await this.prismaService.industryReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
      include: {
        industries: { where: { details: { some: { isSelected: true } } } },
      },
    });
    this.reportCompletedCheck(report);
    if (report.industries.length == 0) {
      throw new BadRequestException('Seçilmiş sektör analizi bulunamadı.');
    }
    for (const u of dto.users) {
      if (!u.isFinancial && !u.isImpact) {
        throw new BadRequestException(
          'Finansal önemlilik ve/veya etki analizi ataması seçilmelidir.',
        );
      }
    }
    const userIds = dto.users.map((u) => u.id);
    const userOrganizationsCount =
      await this.prismaService.userOrganization.count({
        where: {
          userId: { in: [...new Set(userIds)] },
          organizationId: user.organizationId,
        },
      });
    if (userOrganizationsCount !== userIds.length) {
      throw new BadRequestException('Geçersiz kullanıcılar.');
    }
    const helps = await this.prismaService.industryHelp.findMany({
      where: { reportId: report.id },
    });
    const financialHelps = helps.filter((h) => h.isFinancial);
    const impactHelps = helps.filter((h) => h.isImpact);
    const isFinancial = dto.users.some((u) => u.isFinancial);
    const isImpact = dto.users.some((u) => u.isImpact);
    if (isFinancial && financialHelps.length > 0) {
      throw new BadRequestException(
        'Bu rapor için zaten finansal önemlilik ataması oluşturulmuştur.',
      );
    }
    if (isImpact && impactHelps.length > 0) {
      throw new BadRequestException(
        'Bu rapor için zaten etki analizi ataması oluşturulmuştur.',
      );
    }
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateIndustryHelpAnswerDto,
  ): Promise<IndustryHelp> {
    const help = await this.prismaService.industryHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        OR: [
          { financialStatus: HelpStatusEnum.PENDING },
          { impactStatus: HelpStatusEnum.PENDING },
        ],
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(help.report);
    if (help.report.expiredAt && help.report.expiredAt < new Date()) {
      throw new BadRequestException(
        'Rapor süresi dolduğu için işlem yapılamaz.',
      );
    }
    const detailIds = dto.answers.map((a) => a.detailId);
    const detailCount = await this.prismaService.industryDetail.count({
      where: {
        id: { in: [...new Set(detailIds)] },
        industry: { reportId: help.reportId },
        isSelected: true,
      },
    });
    if (detailCount !== detailIds.length) {
      throw new BadRequestException('Geçersiz sektör analizi cevapları.');
    }
    const financialAnswers = dto.answers.filter(
      (a) => a.financialQuestionAnswer,
    );
    const impactAnswers = dto.answers.filter((a) => a.impactQuestionAnswer);
    if (help.isFinancial) {
      if (help.financialStatus) {
        throw new BadRequestException(
          'Finansal önemlilik cevaplarını zaten tamamladınız.',
        );
      }
    } else {
      if (financialAnswers.length > 0) {
        throw new BadRequestException(
          'Bu rapora finansal önemlilik cevabı veremezsiniz.',
        );
      }
    }
    if (help.isImpact) {
      if (help.impactStatus) {
        throw new BadRequestException(
          'Etki analizi cevaplarını zaten tamamladınız.',
        );
      }
    } else {
      if (impactAnswers.length > 0) {
        throw new BadRequestException(
          'Bu rapora etki analizi cevabı veremezsiniz.',
        );
      }
    }
    return help;
  }

  async completeHelpAnswer(
    user: UserDto,
    dto: CompleteIndustryHelpAnswerDto,
  ): Promise<IndustryHelp> {
    const help = await this.prismaService.industryHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        OR: [
          { financialStatus: HelpStatusEnum.PENDING },
          { impactStatus: HelpStatusEnum.PENDING },
        ],
        report: { organizationId: user.organizationId },
      },
      include: { report: true, answers: true },
    });
    this.reportCompletedCheck(help.report);
    if (help.report.expiredAt && help.report.expiredAt < new Date()) {
      throw new BadRequestException(
        'Rapor süresi dolduğu için işlem yapılamaz.',
      );
    }
    const details = await this.prismaService.industryDetail.findMany({
      where: {
        industry: { reportId: help.reportId },
        isSelected: true,
      },
    });
    if (help.isFinancial) {
      if (help.financialStatus) {
        throw new BadRequestException(
          'Finansal önemlilik cevaplarını zaten tamamladınız.',
        );
      } else {
        const answeredDetailIds = help.answers
          .filter((a) => a.financialQuestionAnswer)
          .map((a) => a.detailId);
        const isMissing = details.some(
          (d) => !answeredDetailIds.includes(d.id),
        );
        if (isMissing) {
          throw new BadRequestException(
            'Finansal önemlilik sorularının cevapları eksik.',
          );
        }
      }
    }
    if (help.isImpact) {
      if (help.impactStatus) {
        throw new BadRequestException(
          'Etki analizi cevaplarını zaten tamamladınız.',
        );
      } else {
        const answeredDetailIds = help.answers
          .filter((a) => a.impactQuestionAnswer)
          .map((a) => a.detailId);
        const isMissing = details.some(
          (t) => !answeredDetailIds.includes(t.id),
        );
        if (isMissing) {
          throw new BadRequestException(
            'Etki analizi sorularının cevapları eksik.',
          );
        }
      }
    }
    return help;
  }

  async updateIndustry(user: UserDto, dto: UpdateIndustryDto): Promise<void> {
    const industry = await this.prismaService.industry.findFirstOrThrow({
      where: {
        id: dto.industryId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(industry.report);
    if (industry.isDefault) {
      throw new BadRequestException(
        'Sistem tarafından atanan varsayılan alanlar güncellenemez.',
      );
    }
    if (dto.sectorIds === null || dto.sectorIds?.length === 0) {
      throw new BadRequestException('Sektörleri kaldıramazsanız');
    }
    if (dto.sectorIds) {
      const sectorCount = await this.prismaService.sector.count({
        where: { id: { in: dto.sectorIds } },
      });
      if (sectorCount !== dto.sectorIds.length) {
        throw new BadRequestException('Geçersiz sektörler');
      }
    }
    if (dto.sourceIds) {
      const sourceCount = await this.prismaService.sectorSource.count({
        where: { id: { in: dto.sourceIds } },
      });
      if (sourceCount !== dto.sourceIds.length) {
        throw new BadRequestException('Geçersiz kaynaklar');
      }
    }
    if (dto.sourceIds && dto.sourceText) {
      throw new BadRequestException(
        'source ids and source text cannot be used together',
      );
    }
    if (
      (dto.sourceIds === null && dto.sourceText === null) ||
      (dto.sourceIds === null && industry.sourceText === null) ||
      (dto.sourceText === null && industry.sourceIds === null)
    ) {
      throw new BadRequestException(
        'source ids or source text must be provided',
      );
    }
    if (dto.sourceIds) {
      dto.sourceText = null;
    }
    if (dto.sourceText) {
      dto.sourceIds = null;
    }
    const detailIds = dto.details.filter((d) => d.id).map((d) => d.id);
    const detailCount = await this.prismaService.industryDetail.count({
      where: { id: { in: detailIds }, industryId: dto.industryId },
    });
    if (detailCount !== detailIds.length) {
      throw new BadRequestException('Geçersiz detay verileri');
    }
  }

  async updateReport(
    user: UserDto,
    dto: UpdateIndustryReportDto,
  ): Promise<void> {
    const report = await this.prismaService.industryReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: {
        industries: true,
        financialReports: true,
        impactReports: true,
        helps: { include: { answers: true } },
      },
    });
    if (report.isReady) {
      throw new BadRequestException(
        'Rapor hazır hale getirildiği için işlem yapılamaz.',
      );
    }
    if (
      dto.selectedDetailIds !== undefined ||
      dto.isCompleted !== undefined ||
      dto.expiredAt !== undefined
    ) {
      this.reportCompletedCheck(report);
    }
    if (report.isReady && (dto.financialThreshold || dto.impactThreshold)) {
      throw new BadRequestException(
        'Rapor hazır hale getirildiği için eşik değerleri değiştirilemez.',
      );
    }
    if (dto.expiredAt && dto.expiredAt <= new Date()) {
      throw new BadRequestException('Geçersiz son tarih.');
    }
    if (dto.selectedDetailIds) {
      const detailCount = await this.prismaService.industryDetail.count({
        where: {
          id: { in: [...new Set(dto.selectedDetailIds)] },
          industry: { reportId: report.id },
        },
      });
      if (detailCount !== dto.selectedDetailIds.length) {
        throw new BadRequestException('Geçersiz sektör analizleri seçimi.');
      }
    }
    if (dto.isCompleted) {
      if (new Date() < report.expiredAt) {
        if (dto.password) {
          await this.userService.checkPassword(user, dto.password);
        } else {
          throw new BadRequestException('Şifre girilmesi zorunludur.');
        }
      }
      const answers = report.helps.map((h) => h.answers).flat(1);
      if (answers.length == 0) {
        throw new BadRequestException(
          'Sorulara atanan kişilerden en az biri cevap vermediği için rapor tamamlanamaz.',
        );
      }
    }
    if (dto.isReady) {
      if (
        report.financialThreshold === undefined &&
        report.impactThreshold === undefined
      ) {
        throw new BadRequestException(
          'Eşik değerleri tanımlanmadığı için rapor hazır hale getirilemez.',
        );
      }
      if (dto.password) {
        await this.userService.checkPassword(user, dto.password);
      } else {
        throw new BadRequestException('Şifre girilmesi zorunludur.');
      }
    }
  }

  async deleteIndustry(user: UserDto, dto: DeleteIndustryDto): Promise<void> {
    const industry = await this.prismaService.industry.findFirstOrThrow({
      where: {
        id: dto.industryId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(industry.report);
  }

  async deleteReport(
    user: UserDto,
    dto: DeleteIndustryReportDto,
  ): Promise<void> {
    const report = await this.prismaService.industryReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
  }
}
