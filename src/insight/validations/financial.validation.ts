import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteFinancialReportDto } from '../dtos/financial/delete-financial-report.dto';
import { UpdateFinancialReportDto } from '../dtos/financial/update-financial-report.dto';
import { CreateFinancialHelpDto } from '../dtos/financial/create-financial-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { CreateFinancialHelpAnswerDto } from '../dtos/financial/create-financial-help-answer.dto';
import {
  FinancialHelp,
  FinancialReport,
  FinancialReview,
  Industry,
  IndustryReport,
  Opinion,
  OpinionReport,
  Tendency,
  TendencyReport,
} from '@prisma/client';
import { GetFinancialReportDto } from '../dtos/financial/get-financial-report.dto';
import { GetFinancialReportUserDto } from '../dtos/financial/get-financial-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { UpdateFinancialDetailDto } from '../dtos/financial/update-financial-detail.dto';
import { GetFinancialHelpDetailDto } from '../dtos/financial/get-financial-help-detail.dto';
import { GetCurrentFinancialDto } from '../dtos/financial/get-current-financial.dto';
import { CreateFinancialReportFromOpinionDto } from '../dtos/financial/create-financial-report-from-opinion.dto';
import {
  FinancialPriorityDto,
  UpdateFinancialPriorityDto,
} from '../dtos/financial/update-financial-priority.dto';
import { CreateFinancialReviewDto } from '../dtos/financial/create-financial-review.dto';
import { ReviewStatusEnum } from '../enums/review-status.enum';
import { CreateFinancialReviewAnswerDto } from '../dtos/financial/create-financial-review-answer.dto';
import { GetFinancialReviewDetailDto } from '../dtos/financial/get-financial-review-detail.dto';
import { CreateFinancialReportFromIndustryDto } from '../dtos/financial/create-financial-report-from-industry.dto';
import { CreateFinancialReportFromTendencyDto } from '../dtos/financial/create-financial-report-from-tendency.dto';
import { UserService } from '../../common/services/user.service';
import { PriorityTypeEnum } from '../enums/priority-type.enum';
import { ResetFinancialReportDto } from '../dtos/financial/reset-financial-report.dto';

@Injectable()
export class FinancialValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private userService: UserService,
  ) {}

  private reportCompletedCheck(report: FinancialReport): void {
    if (report.isCompleted) {
      throw new BadRequestException(
        'Finansal analiz raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async getCurrentFinancials(
    user: UserDto,
    dto: GetCurrentFinancialDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetFinancialReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetFinancialReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetFinancialHelpDetailDto,
  ): Promise<FinancialHelp> {
    const help = await this.prismaService.financialHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        report: { organizationId: user.organizationId },
      },
    });
    return help;
  }

  async getReviewDetail(
    user: UserDto,
    dto: GetFinancialReviewDetailDto,
  ): Promise<FinancialReview> {
    const review = await this.prismaService.financialReview.findFirstOrThrow({
      where: {
        id: dto.reviewId,
        reviewerUserId: user.id,
        report: { organizationId: user.organizationId },
      },
    });
    return review;
  }

  async createReportFromOpinion(
    user: UserDto,
    dto: CreateFinancialReportFromOpinionDto,
  ): Promise<{ opinionReport: OpinionReport; opinions: Opinion[] }> {
    await this.userService.checkPassword(user, dto.password);
    const opinionReport =
      await this.prismaService.opinionReport.findFirstOrThrow({
        where: {
          id: dto.opinionReportId,
          organizationId: user.organizationId,
          isCompleted: true,
        },
        include: {
          opinions: {
            where: { isSelected: true },
            include: { helpAnswers: true },
          },
          financialReports: true,
        },
      });
    if (opinionReport.financialThreshold === null) {
      throw new BadRequestException('Finansal eşik değeri belirtilmemiş.');
    }
    const opinions = opinionReport.opinions.filter((op) => {
      const financialPoint =
        op.helpAnswers.reduce(
          (sum, answer) => sum + answer.financialQuestionAnswer,
          0,
        ) / (op.helpAnswers.length == 0 ? 1 : op.helpAnswers.length);
      return financialPoint >= opinionReport.financialThreshold;
    });
    if (opinions.length == 0) {
      throw new BadRequestException(
        'Finansal analiz raporu oluşturmak için eşik değerini geçen paydaş görüşü bulunamadı.',
      );
    }
    if (opinionReport.financialReports.length > 0) {
      throw new BadRequestException(
        'Paydaş görüşleri için finansal analiz raporu zaten oluşturuldu.',
      );
    }
    return { opinionReport: opinionReport, opinions: opinions };
  }

  async createReportFromIndustry(
    user: UserDto,
    dto: CreateFinancialReportFromIndustryDto,
  ): Promise<{ industryReport: IndustryReport; industries: Industry[] }> {
    // await this.userService.checkPassword(user, dto.password);
    // const industryReport =
    //   await this.prismaService.industryReport.findFirstOrThrow({
    //     where: {
    //       id: dto.industryReportId,
    //       organizationId: user.organizationId,
    //       isCompleted: true,
    //     },
    //     include: {
    //       industries: {
    //         where: { isSelected: true },
    //         include: { helpAnswers: true },
    //       },
    //       financialReports: true,
    //     },
    //   });
    // if (industryReport.financialThreshold === null) {
    //   throw new BadRequestException('Finansal eşik değeri belirtilmemiş.');
    // }
    // const industries = industryReport.industries.filter((ind) => {
    //   const financialPoint =
    //     ind.helpAnswers.reduce(
    //       (sum, answer) => sum + answer.financialQuestionAnswer,
    //       0,
    //     ) / (ind.helpAnswers.length == 0 ? 1 : ind.helpAnswers.length);
    //   return financialPoint >= industryReport.financialThreshold;
    // });
    // if (industries.length == 0) {
    //   throw new BadRequestException(
    //     'Finansal analiz raporu oluşturmak için eşik değerini geçen sektör analizi görüşü bulunamadı.',
    //   );
    // }
    // if (industryReport.financialReports.length > 0) {
    //   throw new BadRequestException(
    //     'Sektör analizi için finansal analiz raporu zaten oluşturuldu.',
    //   );
    // }
    // return { industryReport: industryReport, industries: industries };
    return { industryReport: null, industries: [] };
  }

  async createReportFromTendency(
    user: UserDto,
    dto: CreateFinancialReportFromTendencyDto,
  ): Promise<{ tendencyReport: TendencyReport; tendencies: Tendency[] }> {
    await this.userService.checkPassword(user, dto.password);
    const tendencyReport =
      await this.prismaService.tendencyReport.findFirstOrThrow({
        where: {
          id: dto.tendencyReportId,
          organizationId: user.organizationId,
          isCompleted: true,
        },
        include: {
          tendencies: {
            include: { helpAnswers: true },
          },
          financialReports: true,
        },
      });
    if (tendencyReport.financialThreshold === null) {
      throw new BadRequestException('Finansal eşik değeri belirtilmemiş.');
    }
    const tendencies = tendencyReport.tendencies.filter((t) => {
      const financialPoint =
        t.helpAnswers.reduce(
          (sum, answer) => sum + answer.financialQuestionAnswer,
          0,
        ) / (t.helpAnswers.length == 0 ? 1 : t.helpAnswers.length);
      return financialPoint >= tendencyReport.financialThreshold;
    });
    if (tendencies.length == 0) {
      throw new BadRequestException(
        'Finansal analiz raporu oluşturmak için eşik değerini geçen trend analizi görüşü bulunamadı.',
      );
    }
    if (tendencyReport.financialReports.length > 0) {
      throw new BadRequestException(
        'Trend analizi için finansal analiz raporu zaten oluşturuldu.',
      );
    }
    return { tendencyReport: tendencyReport, tendencies: tendencies };
  }

  async createHelp(user: UserDto, dto: CreateFinancialHelpDto): Promise<void> {
    const report = await this.prismaService.financialReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
    });
    this.reportCompletedCheck(report);
    const userOrganizationsCount =
      await this.prismaService.userOrganization.count({
        where: {
          userId: { in: [...new Set(dto.userIds)] },
          organizationId: user.organizationId,
        },
      });
    if (userOrganizationsCount !== dto.userIds.length) {
      throw new BadRequestException('Geçersiz kullanıcılar.');
    }
    const financialHelpsCount = await this.prismaService.financialHelp.count({
      where: { reportId: report.id },
    });
    if (financialHelpsCount > 0) {
      throw new BadRequestException(
        'Bu rapor için zaten atama oluşturulmuştur.',
      );
    }
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateFinancialHelpAnswerDto,
  ): Promise<FinancialHelp> {
    const help = await this.prismaService.financialHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        status: HelpStatusEnum.PENDING,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(help.report);
    if (help.report.expiredAt && help.report.expiredAt < new Date()) {
      throw new BadRequestException(
        'Rapor süresi dolduğu için işlem yapılamaz.',
      );
    }
    const detailIds = dto.answers.map((a) => a.detailId);
    const detailsCount = await this.prismaService.financialDetail.count({
      where: {
        id: { in: [...new Set(detailIds)] },
        financial: { reportId: help.report.id },
      },
    });
    if (detailsCount !== detailIds.length) {
      throw new BadRequestException(
        'Geçersiz finansal analiz detay cevapları.',
      );
    }
    return help;
  }

  async createReviews(
    user: UserDto,
    dto: CreateFinancialReviewDto,
  ): Promise<void> {
    await this.prismaService.financialReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isCompleted: true,
      },
    });
    const reviewerUserIds = dto.reviews.map((r) => r.reviewerUserId);
    const userOrgCount = await this.prismaService.userOrganization.count({
      where: {
        userId: { in: [...new Set(reviewerUserIds)] },
        organizationId: user.organizationId,
      },
    });
    if (userOrgCount !== reviewerUserIds.length) {
      throw new BadRequestException('Geçersiz kullanıcılar.');
    }
    const pendingReviews = await this.prismaService.financialReview.findMany({
      where: {
        reviewerUserId: { in: [...new Set(reviewerUserIds)] },
        reportId: dto.reportId,
        status: ReviewStatusEnum.PENDING,
      },
      include: { reviewerUser: { select: { name: true, surname: true } } },
    });
    if (pendingReviews.length > 0) {
      const pendingReviewerFullNames = pendingReviews.map(
        (pr) => `${pr.reviewerUser.name} ${pr.reviewerUser.surname}`,
      );
      throw new BadRequestException(
        `${pendingReviewerFullNames.join(', ')} adlı kullanıcı/kullanıcılar için zaten yönetici onayı durumu beklemededir.`,
      );
    }
  }

  async createReviewAnswer(
    user: UserDto,
    dto: CreateFinancialReviewAnswerDto,
  ): Promise<FinancialReview> {
    const review = await this.prismaService.financialReview.findFirstOrThrow({
      where: {
        id: dto.reviewId,
        reviewerUserId: user.id,
        report: { organizationId: user.organizationId },
        status: ReviewStatusEnum.PENDING,
      },
    });
    return review;
  }

  async updateFinancialDetail(
    user: UserDto,
    dto: UpdateFinancialDetailDto,
  ): Promise<void> {
    const report = await this.prismaService.financialReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
    for (const detail of dto.details) {
      if (detail.isDelete && !detail.detailId) {
        throw new BadRequestException('Geçersiz detay silme işlemi.');
      }
    }
    // Financial ids validation
    const financialIds = [...new Set(dto.details.map((d) => d.financialId))];
    const financialCount = await this.prismaService.financial.count({
      where: {
        id: { in: financialIds },
        reportId: report.id,
      },
    });
    if (financialCount != financialIds.length) {
      throw new BadRequestException('Geçersiz finansal analiz verileri.');
    }
    // Chain ids validation
    const chainIds = [
      ...new Set(
        dto.details
          .filter((d) => d.chainIds)
          .map((d) => d.chainIds)
          .flat(1),
      ),
    ];
    if (chainIds.length > 0) {
      if (!report.chainReportId) {
        throw new BadRequestException(
          'Değer zincir raporu seçmeniz gerekmektedir.',
        );
      }
      const chainCount = await this.prismaService.chain.count({
        where: {
          id: { in: chainIds },
          reportId: report.chainReportId,
        },
      });
      if (chainCount != chainIds.length) {
        throw new BadRequestException('Geçersiz değer zinciri verileri.');
      }
    }
    // Country ids validation
    const countryIds = [
      ...new Set(
        dto.details
          .filter((d) => d.countryIds)
          .map((d) => d.countryIds)
          .flat(1),
      ),
    ];
    if (countryIds.length > 0) {
      const countryCount = await this.prismaService.country.count({
        where: {
          id: { in: countryIds },
          organizationCountries: {
            some: { organizationId: user.organizationId },
          },
        },
      });
      if (countryCount != countryIds.length) {
        throw new BadRequestException('Geçersiz coğrafya verileri.');
      }
    }
    // Update or Delete validation
    const updateOrDeleteDetails = dto.details.filter((d) => d.detailId);
    const detailCount = await this.prismaService.financialDetail.count({
      where: {
        OR: updateOrDeleteDetails.map((d) => ({
          id: d.detailId,
          financialId: d.financialId,
        })),
        financial: {
          report: { id: report.id, organizationId: user.organizationId },
        },
      },
    });
    if (detailCount != updateOrDeleteDetails.length) {
      throw new BadRequestException('Geçersiz detay verileri.');
    }
  }

  async updateReport(
    user: UserDto,
    dto: UpdateFinancialReportDto,
  ): Promise<FinancialReport> {
    const report = await this.prismaService.financialReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { financials: true, helps: { include: { answers: true } } },
    });
    this.reportCompletedCheck(report);
    if (dto.chainReportId) {
      await this.prismaService.chainReport.findFirstOrThrow({
        where: { id: dto.chainReportId, organizationId: user.organizationId },
      });
    }
    if (dto.expiredAt && dto.expiredAt <= new Date()) {
      throw new BadRequestException('Geçersiz son tarih.');
    }
    if (dto.isCompleted) {
      if (new Date() < report.expiredAt) {
        if (dto.password) {
          await this.userService.checkPassword(user, dto.password);
        } else {
          throw new BadRequestException('Şifre girilmesi zorunludur.');
        }
      }
      const answers = report.helps.map((h) => h.answers).flat(1);
      if (answers.length == 0) {
        throw new BadRequestException(
          'Sorulara atanan kişilerden en az biri cevap vermediği için rapor tamamlanamaz.',
        );
      }
    }
    return report;
  }

  async resetReport(user: UserDto, dto: ResetFinancialReportDto) {
    await this.prismaService.financialReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isCompleted: true,
        reviews: { some: { status: ReviewStatusEnum.DECLINED } },
      },
    });
  }

  async updatePriority(
    user: UserDto,
    dto: UpdateFinancialPriorityDto,
  ): Promise<void> {
    await this.prismaService.financialReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isCompleted: true,
      },
    });
    const priorityTypes = dto.priorities.map((p) => p.type);
    if (priorityTypes.length != new Set(priorityTypes).size) {
      throw new BadRequestException(
        'Aynı tip öncelikten birden fazla giremezsiniz.',
      );
    }
    let low: FinancialPriorityDto;
    let medium: FinancialPriorityDto;
    let high: FinancialPriorityDto;
    for (const p of dto.priorities) {
      if (p.minValue > p.maxValue) {
        throw new BadRequestException('Geçersiz öncelik aralığı.');
      }
      if (p.type == PriorityTypeEnum.LOW) {
        low = p;
      } else if (p.type == PriorityTypeEnum.MEDIUM) {
        medium = p;
      } else if (p.type == PriorityTypeEnum.HIGH) {
        high = p;
      }
    }
    if (low && medium) {
      if (low.maxValue > medium.minValue) {
        throw new BadRequestException(
          'Geçersiz düşük ve orta öncelik aralığı.',
        );
      }
    }
    if (low && high) {
      if (low.maxValue > high.minValue) {
        throw new BadRequestException(
          'Geçersiz düşük ve yüksek öncelik aralığı.',
        );
      }
    }
    if (medium && high) {
      if (medium.maxValue > high.minValue) {
        throw new BadRequestException(
          'Geçersiz orta ve yüksek öncelik aralığı.',
        );
      }
    }
  }

  async deleteReport(
    user: UserDto,
    dto: DeleteFinancialReportDto,
  ): Promise<void> {
    const report = await this.prismaService.financialReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
  }
}
