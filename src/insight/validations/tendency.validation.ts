import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteTendencyReportDto } from '../dtos/tendency/delete-tendency-report.dto';
import { UpdateTendencyDto } from '../dtos/tendency/update-tendency.dto';
import { DeleteTendencyDto } from '../dtos/tendency/delete-tendency.dto';
import { UpdateTendencyReportDto } from '../dtos/tendency/update-tendency-report.dto';
import { CreateTendencyHelpDto } from '../dtos/tendency/create-tendency-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { CreateTendencyHelpAnswerDto } from '../dtos/tendency/create-tendency-help-answer.dto';
import { TendencyHelp, TendencyReport } from '@prisma/client';
import { GetTendencyReportDto } from '../dtos/tendency/get-tendency-report.dto';
import { GetTendencyReportUserDto } from '../dtos/tendency/get-tendency-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { CreateTendencyDto } from '../dtos/tendency/create-tendency.dto';
import { GetTendencyHelpDetailDto } from '../dtos/tendency/get-tendency-help-detail.dto';
import { GetCurrentTendencyDto } from '../dtos/tendency/get-current-tendency.dto';
import { UserService } from '../../common/services/user.service';
import { CompleteTendencyHelpAnswerDto } from '../dtos/tendency/complete-tendency-help-answer.dto';

@Injectable()
export class TendencyValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private userService: UserService,
  ) {}

  private reportCompletedCheck(report: TendencyReport): void {
    if (report.isCompleted) {
      throw new BadRequestException(
        'Trend analizi raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async getCurrentTendencies(
    user: UserDto,
    dto: GetCurrentTendencyDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetTendencyReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetTendencyReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetTendencyHelpDetailDto,
  ): Promise<TendencyHelp> {
    const help = await this.prismaService.tendencyHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        report: { organizationId: user.organizationId },
      },
    });
    return help;
  }

  async createTendency(user: UserDto, dto: CreateTendencyDto): Promise<void> {
    const report = await this.prismaService.tendencyReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
  }

  async createHelp(user: UserDto, dto: CreateTendencyHelpDto): Promise<void> {
    const report = await this.prismaService.tendencyReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
      include: { tendencies: { where: { isSelected: true } } },
    });
    this.reportCompletedCheck(report);
    if (report.tendencies.length == 0) {
      throw new BadRequestException('Trend analizi bulunamadı.');
    }
    for (const u of dto.users) {
      if (!u.isFinancial && !u.isImpact) {
        throw new BadRequestException(
          'Finansal önemlilik ve/veya etki analizi ataması seçilmelidir.',
        );
      }
    }
    const userIds = dto.users.map((u) => u.id);
    const userOrganizationsCount =
      await this.prismaService.userOrganization.count({
        where: {
          userId: { in: [...new Set(userIds)] },
          organizationId: user.organizationId,
        },
      });
    if (userOrganizationsCount !== userIds.length) {
      throw new BadRequestException('Geçersiz kullanıcılar.');
    }
    const helps = await this.prismaService.tendencyHelp.findMany({
      where: { reportId: report.id },
    });
    const financialHelps = helps.filter((h) => h.isFinancial);
    const impactHelps = helps.filter((h) => h.isImpact);
    const isFinancial = dto.users.some((u) => u.isFinancial);
    const isImpact = dto.users.some((u) => u.isImpact);
    if (isFinancial && financialHelps.length > 0) {
      throw new BadRequestException(
        'Bu rapor için zaten finansal önemlilik ataması oluşturulmuştur.',
      );
    }
    if (isImpact && impactHelps.length > 0) {
      throw new BadRequestException(
        'Bu rapor için zaten etki analizi ataması oluşturulmuştur.',
      );
    }
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateTendencyHelpAnswerDto,
  ): Promise<TendencyHelp> {
    const help = await this.prismaService.tendencyHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        OR: [
          { financialStatus: HelpStatusEnum.PENDING },
          { impactStatus: HelpStatusEnum.PENDING },
        ],
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(help.report);
    if (help.report.expiredAt && help.report.expiredAt < new Date()) {
      throw new BadRequestException(
        'Rapor süresi dolduğu için işlem yapılamaz.',
      );
    }
    const tendencyIds = dto.answers.map((a) => a.tendencyId);
    const tendencyCount = await this.prismaService.tendency.count({
      where: {
        id: { in: [...new Set(tendencyIds)] },
        reportId: help.reportId,
        isSelected: true,
      },
    });
    if (tendencyCount !== tendencyIds.length) {
      throw new BadRequestException('Geçersiz trend analizi cevapları.');
    }
    const financialAnswers = dto.answers.filter(
      (a) => a.financialQuestionAnswer,
    );
    const impactAnswers = dto.answers.filter((a) => a.impactQuestionAnswer);
    if (help.isFinancial) {
      if (help.financialStatus) {
        throw new BadRequestException(
          'Finansal önemlilik cevaplarını zaten tamamladınız.',
        );
      }
    } else {
      if (financialAnswers.length > 0) {
        throw new BadRequestException(
          'Bu rapora finansal önemlilik cevabı veremezsiniz.',
        );
      }
    }
    if (help.isImpact) {
      if (help.impactStatus) {
        throw new BadRequestException(
          'Etki analizi cevaplarını zaten tamamladınız.',
        );
      }
    } else {
      if (impactAnswers.length > 0) {
        throw new BadRequestException(
          'Bu rapora etki analizi cevabı veremezsiniz.',
        );
      }
    }
    return help;
  }

  async completeHelpAnswer(
    user: UserDto,
    dto: CompleteTendencyHelpAnswerDto,
  ): Promise<TendencyHelp> {
    const help = await this.prismaService.tendencyHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        OR: [
          { financialStatus: HelpStatusEnum.PENDING },
          { impactStatus: HelpStatusEnum.PENDING },
        ],
        report: { organizationId: user.organizationId },
      },
      include: { report: true, answers: true },
    });
    this.reportCompletedCheck(help.report);
    if (help.report.expiredAt && help.report.expiredAt < new Date()) {
      throw new BadRequestException(
        'Rapor süresi dolduğu için işlem yapılamaz.',
      );
    }
    const tendencies = await this.prismaService.tendency.findMany({
      where: {
        reportId: help.reportId,
        isSelected: true,
      },
    });
    if (help.isFinancial) {
      if (help.financialStatus) {
        throw new BadRequestException(
          'Finansal önemlilik cevaplarını zaten tamamladınız.',
        );
      } else {
        const answeredTendencyIds = help.answers
          .filter((a) => a.financialQuestionAnswer)
          .map((a) => a.tendencyId);
        const isMissing = tendencies.some(
          (t) => !answeredTendencyIds.includes(t.id),
        );
        if (isMissing) {
          throw new BadRequestException(
            'Finansal önemlilik sorularının cevapları eksik.',
          );
        }
      }
    }
    if (help.isImpact) {
      if (help.impactStatus) {
        throw new BadRequestException(
          'Etki analizi cevaplarını zaten tamamladınız.',
        );
      } else {
        const answeredTendencyIds = help.answers
          .filter((a) => a.impactQuestionAnswer)
          .map((a) => a.tendencyId);
        const isMissing = tendencies.some(
          (t) => !answeredTendencyIds.includes(t.id),
        );
        if (isMissing) {
          throw new BadRequestException(
            'Etki analizi sorularının cevapları eksik.',
          );
        }
      }
    }
    return help;
  }

  async updateTendency(user: UserDto, dto: UpdateTendencyDto): Promise<void> {
    const tendency = await this.prismaService.tendency.findFirstOrThrow({
      where: {
        id: dto.tendencyId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(tendency.report);
    if (tendency.isDefault) {
      throw new BadRequestException(
        'Sistem tarafından atanan varsayılan alanlar güncellenemez.',
      );
    }
  }

  async updateReport(
    user: UserDto,
    dto: UpdateTendencyReportDto,
  ): Promise<void> {
    const report = await this.prismaService.tendencyReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: {
        tendencies: true,
        financialReports: true,
        impactReports: true,
        helps: { include: { answers: true } },
      },
    });
    if (report.isReady) {
      throw new BadRequestException(
        'Rapor hazır hale getirildiği için işlem yapılamaz.',
      );
    }
    if (
      dto.selectedTendencyIds !== undefined ||
      dto.isCompleted !== undefined ||
      dto.expiredAt !== undefined
    ) {
      this.reportCompletedCheck(report);
    }
    if (report.isReady && (dto.financialThreshold || dto.impactThreshold)) {
      throw new BadRequestException(
        'Rapor hazır hale getirildiği için eşik değerleri değiştirilemez.',
      );
    }
    if (dto.expiredAt && dto.expiredAt <= new Date()) {
      throw new BadRequestException('Geçersiz son tarih.');
    }
    if (dto.selectedTendencyIds) {
      const tendencyCount = await this.prismaService.tendency.count({
        where: {
          id: { in: [...new Set(dto.selectedTendencyIds)] },
          reportId: report.id,
        },
      });
      if (tendencyCount !== dto.selectedTendencyIds.length) {
        throw new BadRequestException('Geçersiz trend analizi seçimi.');
      }
    }
    if (dto.isCompleted) {
      if (new Date() < report.expiredAt) {
        if (dto.password) {
          await this.userService.checkPassword(user, dto.password);
        } else {
          throw new BadRequestException('Şifre girilmesi zorunludur.');
        }
      }
      const answers = report.helps.map((h) => h.answers).flat(1);
      if (answers.length == 0) {
        throw new BadRequestException(
          'Sorulara atanan kişilerden en az biri cevap vermediği için rapor tamamlanamaz.',
        );
      }
    }
    if (dto.isReady) {
      if (
        report.financialThreshold === undefined &&
        report.impactThreshold === undefined
      ) {
        throw new BadRequestException(
          'Eşik değerleri tanımlanmadığı için rapor hazır hale getirilemez.',
        );
      }
      if (dto.password) {
        await this.userService.checkPassword(user, dto.password);
      } else {
        throw new BadRequestException('Şifre girilmesi zorunludur.');
      }
    }
  }

  async deleteTendency(user: UserDto, dto: DeleteTendencyDto): Promise<void> {
    const tendency = await this.prismaService.tendency.findFirstOrThrow({
      where: {
        id: dto.tendencyId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(tendency.report);
  }

  async deleteReport(
    user: UserDto,
    dto: DeleteTendencyReportDto,
  ): Promise<void> {
    const report = await this.prismaService.tendencyReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
  }
}
