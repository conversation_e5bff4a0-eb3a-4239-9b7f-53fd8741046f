import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { CreateMaterialityReportDto } from '../dtos/materiality/create-materiality-report.dto';
import { GetMaterialityReportDto } from '../dtos/materiality/get-materiality-report.dto';
import { GetMaterialityReportUserDto } from '../dtos/materiality/get-materiality-report-user.dto';
import { CreateMaterialityReportType } from '../types/materiality.type';
import { ChainService } from '../../analysis/services/chain.service';
import { GetMaterialityDto } from '../dtos/materiality/get-materiality.dto';

@Injectable()
export class MaterialityValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private chainService: ChainService,
  ) {}

  async getMaterialities(user: UserDto, dto: GetMaterialityDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetMaterialityReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetMaterialityReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async createReport(user: UserDto, dto: CreateMaterialityReportDto) {
    if (
      !dto.opinionReportId &&
      !dto.industryReportId &&
      !dto.tendencyReportId
    ) {
      throw new BadRequestException('En az bir rapor seçilmelidir.');
    }
    const chainReport = await this.chainService.getLastReport(
      user.organizationId,
      { isCompleted: true },
    );
    if (!chainReport) {
      throw new BadRequestException(
        'Öncelikle bir değer zinciri raporu tamamlamalısınız.',
      );
    }
    const reports: CreateMaterialityReportType = {
      chain: { report: chainReport },
    };
    if (dto.opinionReportId) {
      reports.opinion = await this.createReportFromOpinion(
        user,
        dto.opinionReportId,
      );
    }
    if (dto.industryReportId) {
      reports.industry = await this.createReportFromIndustry(
        user,
        dto.industryReportId,
      );
    }
    if (dto.tendencyReportId) {
      reports.tendency = await this.createReportFromTendency(
        user,
        dto.tendencyReportId,
      );
    }
    return reports;
  }

  private async createReportFromOpinion(
    user: UserDto,
    reportId: number,
  ): Promise<CreateMaterialityReportType['opinion']> {
    const report = await this.prismaService.opinionReport.findFirstOrThrow({
      where: {
        id: reportId,
        organizationId: user.organizationId,
        isReady: true,
      },
      include: {
        opinions: {
          where: { isSelected: true },
          include: { helpAnswers: true },
        },
        helps: true,
      },
    });
    const helpsMap = new Map();
    for (const ha of report.opinions.flatMap((op) => op.helpAnswers)) {
      if (!helpsMap.has(ha.helpId)) {
        const help = report.helps?.find((h) => h.id === ha.helpId);
        if (help) helpsMap.set(ha.helpId, help);
      }
    }
    const financialOpinions = report.financialThreshold
      ? report.opinions.filter((op) => {
          const validFinancialAnswers = op.helpAnswers.filter((answer) => {
            const help = helpsMap.get(answer.helpId);
            return (
              help &&
              help.isFinancial &&
              help.financialStatus &&
              answer.financialQuestionAnswer !== null &&
              answer.financialQuestionAnswer !== undefined
            );
          });
          const financialPoint =
            validFinancialAnswers.reduce(
              (sum, answer) => sum + answer.financialQuestionAnswer,
              0,
            ) /
            (validFinancialAnswers.length === 0
              ? 1
              : validFinancialAnswers.length);
          return financialPoint >= report.financialThreshold;
        })
      : [];
    const impactOpinions = report.impactThreshold
      ? report.opinions.filter((op) => {
          const validImpactAnswers = op.helpAnswers.filter((answer) => {
            const help = helpsMap.get(answer.helpId);
            return (
              help &&
              help.isImpact &&
              help.impactStatus &&
              answer.impactQuestionAnswer !== null &&
              answer.impactQuestionAnswer !== undefined
            );
          });
          const impactPoint =
            validImpactAnswers.reduce(
              (sum, answer) => sum + answer.impactQuestionAnswer,
              0,
            ) /
            (validImpactAnswers.length === 0 ? 1 : validImpactAnswers.length);
          return impactPoint >= report.impactThreshold;
        })
      : [];
    if (financialOpinions.length == 0 && impactOpinions.length == 0) {
      throw new BadRequestException(
        'Önemlilik raporu oluşturmak için eşik değerini geçen paydaş görüşü bulunamadı.',
      );
    }
    return {
      report: report,
      financialOpinions: financialOpinions,
      impactOpinions: impactOpinions,
    };
  }

  private async createReportFromIndustry(
    user: UserDto,
    reportId: number,
  ): Promise<CreateMaterialityReportType['industry']> {
    const report = await this.prismaService.industryReport.findFirstOrThrow({
      where: {
        id: reportId,
        organizationId: user.organizationId,
        isReady: true,
      },
      include: {
        industries: {
          where: { details: { some: { isSelected: true } } },
          include: {
            details: {
              where: { isSelected: true },
              include: { helpAnswers: true },
            },
          },
        },
        helps: true,
      },
    });
    const helpsMap = new Map();
    for (const ind of report.industries) {
      for (const detail of ind.details) {
        for (const ha of detail.helpAnswers) {
          if (!helpsMap.has(ha.helpId)) {
            const help = report.helps?.find((h) => h.id === ha.helpId);
            if (help) helpsMap.set(ha.helpId, help);
          }
        }
      }
    }
    const financialIndustries = report.financialThreshold
      ? structuredClone(report.industries).filter((ind) => {
          ind.details = ind.details.filter((detail) => {
            const validFinancialAnswers = detail.helpAnswers.filter(
              (answer) => {
                const help = helpsMap.get(answer.helpId);
                return (
                  help &&
                  help.isFinancial &&
                  help.financialStatus &&
                  answer.financialQuestionAnswer !== null &&
                  answer.financialQuestionAnswer !== undefined
                );
              },
            );
            const financialPoint =
              validFinancialAnswers.reduce(
                (sum, answer) => sum + answer.financialQuestionAnswer,
                0,
              ) /
              (validFinancialAnswers.length === 0
                ? 1
                : validFinancialAnswers.length);
            return financialPoint >= report.financialThreshold;
          });
          return ind.details.length > 0;
        })
      : [];
    const impactIndustries = report.impactThreshold
      ? structuredClone(report.industries).filter((ind) => {
          ind.details = ind.details.filter((detail) => {
            const validImpactAnswers = detail.helpAnswers.filter((answer) => {
              const help = helpsMap.get(answer.helpId);
              return (
                help &&
                help.isImpact &&
                help.impactStatus &&
                answer.impactQuestionAnswer !== null &&
                answer.impactQuestionAnswer !== undefined
              );
            });
            const impactPoint =
              validImpactAnswers.reduce(
                (sum, answer) => sum + answer.impactQuestionAnswer,
                0,
              ) /
              (validImpactAnswers.length === 0 ? 1 : validImpactAnswers.length);
            return impactPoint >= report.impactThreshold;
          });
          return ind.details.length > 0;
        })
      : [];
    if (financialIndustries.length == 0 && impactIndustries.length == 0) {
      throw new BadRequestException(
        'Önemlilik raporu oluşturmak için eşik değerini geçen sektör analizi verisi bulunamadı.',
      );
    }
    return {
      report: report,
      financialIndustries: financialIndustries,
      impactIndustries: impactIndustries,
    };
  }

  private async createReportFromTendency(
    user: UserDto,
    reportId: number,
  ): Promise<CreateMaterialityReportType['tendency']> {
    const report = await this.prismaService.tendencyReport.findFirstOrThrow({
      where: {
        id: reportId,
        organizationId: user.organizationId,
        isReady: true,
      },
      include: {
        tendencies: {
          include: { helpAnswers: true },
        },
        helps: true,
      },
    });
    const helpsMap = new Map();
    for (const ha of report.tendencies.flatMap((t) => t.helpAnswers)) {
      if (!helpsMap.has(ha.helpId)) {
        const help = report.helps?.find((h) => h.id === ha.helpId);
        if (help) helpsMap.set(ha.helpId, help);
      }
    }
    const financialTendencies = report.financialThreshold
      ? report.tendencies.filter((t) => {
          const validFinancialAnswers = t.helpAnswers.filter((answer) => {
            const help = helpsMap.get(answer.helpId);
            return (
              help &&
              help.isFinancial &&
              help.financialStatus &&
              answer.financialQuestionAnswer !== null &&
              answer.financialQuestionAnswer !== undefined
            );
          });
          const financialPoint =
            validFinancialAnswers.reduce(
              (sum, answer) => sum + answer.financialQuestionAnswer,
              0,
            ) /
            (validFinancialAnswers.length === 0
              ? 1
              : validFinancialAnswers.length);
          return financialPoint >= report.financialThreshold;
        })
      : [];
    const impactTendencies = report.impactThreshold
      ? report.tendencies.filter((t) => {
          const validImpactAnswers = t.helpAnswers.filter((answer) => {
            const help = helpsMap.get(answer.helpId);
            return (
              help &&
              help.isImpact &&
              help.impactStatus &&
              answer.impactQuestionAnswer !== null &&
              answer.impactQuestionAnswer !== undefined
            );
          });
          const impactPoint =
            validImpactAnswers.reduce(
              (sum, answer) => sum + answer.impactQuestionAnswer,
              0,
            ) /
            (validImpactAnswers.length === 0 ? 1 : validImpactAnswers.length);
          return impactPoint >= report.impactThreshold;
        })
      : [];
    if (financialTendencies.length == 0 && impactTendencies.length == 0) {
      throw new BadRequestException(
        'Önemlilik raporu oluşturmak için eşik değerini geçen trend analizi verisi bulunamadı.',
      );
    }
    return {
      report: report,
      financialTendencies: financialTendencies,
      impactTendencies: impactTendencies,
    };
  }
}
