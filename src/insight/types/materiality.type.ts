import {
  ChainReport,
  IndustryReport,
  Opinion,
  OpinionReport,
  Prisma,
  Tendency,
  TendencyReport,
} from '@prisma/client';

export interface CreateMaterialityReportType {
  chain: { report: ChainReport };
  opinion?: {
    report: OpinionReport;
    financialOpinions: Opinion[];
    impactOpinions: Opinion[];
  };
  industry?: {
    report: IndustryReport;
    financialIndustries: Prisma.IndustryGetPayload<{
      include: { details: true };
    }>[];
    impactIndustries: Prisma.IndustryGetPayload<{
      include: { details: true };
    }>[];
  };
  tendency?: {
    report: TendencyReport;
    financialTendencies: Tendency[];
    impactTendencies: Tendency[];
  };
}
