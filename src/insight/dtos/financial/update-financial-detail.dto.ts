import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { FinancialCategoryTypeEnum } from '../../enums/financial-category-type.enum';
import { PeriodTypeEnum } from '../../enums/period-type.enum';
import { Type } from 'class-transformer';

class UpdateFinancialDetailDataDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  financialId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  detailId?: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  categoryDescription?: string;

  @ApiProperty({
    type: Number,
    enum: FinancialCategoryTypeEnum,
    required: false,
  })
  @IsEnum(FinancialCategoryTypeEnum)
  @IsOptional()
  categoryType?: FinancialCategoryTypeEnum;

  @ApiProperty({ type: Number, enum: PeriodTypeEnum, required: false })
  @IsEnum(PeriodTypeEnum)
  @IsOptional()
  periodType?: PeriodTypeEnum;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsNumber({}, { each: true })
  @IsOptional()
  chainIds?: number[];

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsNumber({}, { each: true })
  @IsOptional()
  countryIds?: number[];

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isDelete?: boolean;
}

export class UpdateFinancialDetailDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: UpdateFinancialDetailDataDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => UpdateFinancialDetailDataDto)
  details: UpdateFinancialDetailDataDto[];
}
