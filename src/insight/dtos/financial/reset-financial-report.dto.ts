import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber } from 'class-validator';
import { MaterialityResetEnum } from '../../enums/materiality-reset.enum';

export class ResetFinancialReportDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: Number, enum: MaterialityResetEnum })
  @IsEnum(MaterialityResetEnum)
  resetType: MaterialityResetEnum;
}
