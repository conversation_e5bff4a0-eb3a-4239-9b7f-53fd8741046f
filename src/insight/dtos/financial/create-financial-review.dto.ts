import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class FinancialReviewDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reviewerUserId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  description?: string;
}

export class CreateFinancialReviewDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: FinancialReviewDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => FinancialReviewDto)
  reviews: FinancialReviewDto[];
}
