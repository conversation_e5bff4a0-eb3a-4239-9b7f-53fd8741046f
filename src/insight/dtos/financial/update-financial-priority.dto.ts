import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNumber,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PriorityTypeEnum } from '../../enums/priority-type.enum';

export class FinancialPriorityDto {
  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(25)
  minValue: number;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(25)
  maxValue: number;

  @ApiProperty({ type: Number, enum: PriorityTypeEnum })
  @IsEnum(PriorityTypeEnum)
  type: PriorityTypeEnum;
}

export class UpdateFinancialPriorityDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: FinancialPriorityDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(3)
  @ValidateNested({ each: true })
  @Type(() => FinancialPriorityDto)
  priorities: FinancialPriorityDto[];
}
