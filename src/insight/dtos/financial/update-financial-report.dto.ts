import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateFinancialReportDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  chainReportId?: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;

  @ApiProperty({ type: Date, required: false })
  @Transform(({ value }) => new Date(value))
  @IsDate()
  @IsOptional()
  expiredAt?: Date;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  password?: string;
}
