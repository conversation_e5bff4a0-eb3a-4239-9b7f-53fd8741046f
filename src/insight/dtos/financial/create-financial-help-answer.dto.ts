import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  Max,
  <PERSON>,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class FinancialHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  detailId: number;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  severity: number;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  possibility: number;
}

export class CreateFinancialHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  helpId: number;

  @ApiProperty({ type: FinancialHelpAnswerDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => FinancialHelpAnswerDto)
  answers: FinancialHelpAnswerDto[];
}
