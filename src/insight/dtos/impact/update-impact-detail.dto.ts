import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { ImpactCategoryTypeEnum } from '../../enums/impact-category-type.enum';
import { Type } from 'class-transformer';
import { ImpactRealityTypeEnum } from '../../enums/impact-reality-type.enum';

class UpdateImpactDetailDataDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  impactId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  detailId?: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  categoryDescription?: string;

  @ApiProperty({
    type: Number,
    enum: ImpactCategoryTypeEnum,
    required: false,
  })
  @IsEnum(ImpactCategoryTypeEnum)
  @IsOptional()
  categoryType?: ImpactCategoryTypeEnum;

  @ApiProperty({ type: Number, enum: ImpactRealityTypeEnum, required: false })
  @IsEnum(ImpactRealityTypeEnum)
  @IsOptional()
  realityType?: ImpactRealityTypeEnum;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsNumber({}, { each: true })
  @IsOptional()
  chainIds?: number[];

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsNumber({}, { each: true })
  @IsOptional()
  countryIds?: number[];

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isDelete?: boolean;
}

export class UpdateImpactDetailDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: UpdateImpactDetailDataDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => UpdateImpactDetailDataDto)
  details: UpdateImpactDetailDataDto[];
}
