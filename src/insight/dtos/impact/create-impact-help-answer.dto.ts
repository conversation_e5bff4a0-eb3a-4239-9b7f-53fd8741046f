import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  <PERSON>,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class ImpactHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  detailId: number;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  scale: number;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  extent: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  @IsOptional()
  reparation?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  @IsOptional()
  possibility?: number;
}

export class CreateImpactHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  helpId: number;

  @ApiProperty({ type: ImpactHelpAnswerDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => ImpactHelpAnswerDto)
  answers: ImpactHelpAnswerDto[];
}
