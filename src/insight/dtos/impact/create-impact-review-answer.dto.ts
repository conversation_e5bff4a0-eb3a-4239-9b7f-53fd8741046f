import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { ReviewStatusEnum } from '../../enums/review-status.enum';

export class CreateImpactReviewAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reviewId: number;

  @ApiProperty({ type: Number, enum: ReviewStatusEnum })
  @IsEnum(ReviewStatusEnum)
  status: ReviewStatusEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  description?: string;
}
