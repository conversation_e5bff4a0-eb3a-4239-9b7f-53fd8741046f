import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { CurrentTabEnum } from '../../enums/current-tab.enum';

export class GetCurrentInsightDto {
  @ApiProperty({ type: String, enum: CurrentTabEnum, required: false })
  @IsEnum(CurrentTabEnum)
  @IsOptional()
  currentTab?: CurrentTabEnum;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  subsidiaryId?: number;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  reportId?: number;
}
