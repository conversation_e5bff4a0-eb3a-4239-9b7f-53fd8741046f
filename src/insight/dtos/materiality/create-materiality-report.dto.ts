import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';

export class CreateMaterialityReportDto {
  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  opinionReportId?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  industryReportId?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  tendencyReportId?: number;
}
