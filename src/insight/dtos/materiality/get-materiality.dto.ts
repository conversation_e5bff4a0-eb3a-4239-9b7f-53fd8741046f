import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetMaterialityDto {
  @ApiProperty({ type: Number })
  @Transform(({ value }) => +value)
  @IsNumber()
  code: number;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  subsidiaryId?: number;
}
