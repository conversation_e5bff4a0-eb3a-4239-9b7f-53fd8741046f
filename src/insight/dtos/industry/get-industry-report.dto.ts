import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetIndustryReportDto {
  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  subsidiaryId?: number;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  isReady?: boolean;
}
