import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class IndustryHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  detailId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  @IsOptional()
  financialQuestionAnswer?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  @IsOptional()
  impactQuestionAnswer?: number;
}

export class CreateIndustryHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  helpId: number;

  @ApiProperty({ type: IndustryHelpAnswerDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => IndustryHelpAnswerDto)
  answers: IndustryHelpAnswerDto[];
}
