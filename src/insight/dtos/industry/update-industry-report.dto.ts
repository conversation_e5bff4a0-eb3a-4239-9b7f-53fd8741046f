import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsDate,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Max,
  Min,
  MinLength,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateIndustryReportDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  selectedDetailIds?: number[];

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(0)
  @Max(5)
  @IsOptional()
  financialThreshold?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(0)
  @Max(5)
  @IsOptional()
  impactThreshold?: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isReady?: boolean;

  @ApiProperty({ type: Date, required: false })
  @Transform(({ value }) => new Date(value))
  @IsDate()
  @IsOptional()
  expiredAt?: Date;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  password?: string;
}
