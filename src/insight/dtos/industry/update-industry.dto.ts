import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class UpdateIndustryDetailDto {
  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  id?: number;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  priorityIssue: string;

  @ApiProperty({ type: String, isArray: true })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  focusAreas: string[];
}

export class UpdateIndustryDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  industryId: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @IsNumber({}, { each: true })
  @ArrayMinSize(1)
  @IsOptional()
  sectorIds?: number[];

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @IsNumber({}, { each: true })
  @ArrayMinSize(1)
  @IsOptional()
  sourceIds?: number[];

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  sourceText?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;

  @ApiProperty({ type: UpdateIndustryDetailDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => UpdateIndustryDetailDto)
  @IsArray()
  @ArrayMinSize(1)
  details: UpdateIndustryDetailDto[];
}
