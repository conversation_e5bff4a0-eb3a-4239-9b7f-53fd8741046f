import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class CreateIndustryDetailDto {
  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  priorityIssue: string;

  @ApiProperty({ type: String, isArray: true })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  focusAreas: string[];
}

export class CreateIndustryDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: Number, isArray: true })
  @IsArray()
  @IsNumber({}, { each: true })
  @ArrayMinSize(1)
  sectorIds: number[];

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @IsNumber({}, { each: true })
  @ArrayMinSize(1)
  @IsOptional()
  sourceIds?: number[];

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  sourceText?: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  description: string;

  @ApiProperty({ type: CreateIndustryDetailDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => CreateIndustryDetailDto)
  @IsArray()
  @ArrayMinSize(1)
  details: CreateIndustryDetailDto[];
}
