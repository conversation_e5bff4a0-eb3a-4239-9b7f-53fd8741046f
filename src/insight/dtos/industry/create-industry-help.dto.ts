import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class CreateIndustryHelpUserDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  id: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isFinancial?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isImpact?: boolean;
}

export class CreateIndustryHelpDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: CreateIndustryHelpUserDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => CreateIndustryHelpUserDto)
  @IsArray()
  @ArrayMinSize(1)
  users: CreateIndustryHelpUserDto[];
}
