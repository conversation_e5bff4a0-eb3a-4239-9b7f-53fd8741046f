import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON>rray, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetIndustryDto {
  @ApiProperty({ type: Number })
  @Transform(({ value }) => +value)
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @Transform(({ value }) =>
    Array.isArray(value) ? value.map(Number) : [+value],
  )
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  sectorIds?: number[];
}
