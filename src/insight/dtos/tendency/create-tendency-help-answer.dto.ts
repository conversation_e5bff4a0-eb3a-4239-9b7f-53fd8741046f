import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  <PERSON>,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class TendencyHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  tendencyId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  @IsOptional()
  financialQuestionAnswer?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  @IsOptional()
  impactQuestionAnswer?: number;
}

export class CreateTendencyHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  helpId: number;

  @ApiProperty({ type: TendencyHelpAnswerDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => TendencyHelpAnswerDto)
  answers: TendencyHelpAnswerDto[];
}
