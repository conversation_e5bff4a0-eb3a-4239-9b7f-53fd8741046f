import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class CreateTendencyHelpUserDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  id: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isFinancial?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isImpact?: boolean;
}

export class CreateTendencyHelpDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: CreateTendencyHelpUserDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => CreateTendencyHelpUserDto)
  @IsArray()
  @ArrayMinSize(1)
  users: CreateTendencyHelpUserDto[];
}
