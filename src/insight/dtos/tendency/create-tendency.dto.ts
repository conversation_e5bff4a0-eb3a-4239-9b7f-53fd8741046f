import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsString, MinLength } from 'class-validator';

export class CreateTendencyDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  focusArea: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  topic: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  source: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  description: string;
}
