import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsString, MinLength } from 'class-validator';

export class UpdateTendencyDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  tendencyId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  focusArea?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  topic?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  source?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;
}
