import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsString, MinLength } from 'class-validator';

export class CreateOpinionDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  focusArea: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  priorityIssue: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  subPriorityIssue: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  minorPriorityIssue: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  description: string;
}
