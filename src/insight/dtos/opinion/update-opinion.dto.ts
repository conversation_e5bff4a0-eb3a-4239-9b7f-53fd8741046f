import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, MinLength } from 'class-validator';

export class UpdateOpinionDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  opinionId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  focusArea?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  priorityIssue?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  subPriorityIssue?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  minorPriorityIssue?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;
}
