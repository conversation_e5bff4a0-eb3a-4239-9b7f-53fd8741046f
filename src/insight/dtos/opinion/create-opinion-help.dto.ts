import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class CreateOpinionHelpStakeholderUserDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  id: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isFinancial?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isImpact?: boolean;
}

export class CreateOpinionHelpDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: CreateOpinionHelpStakeholderUserDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => CreateOpinionHelpStakeholderUserDto)
  @IsArray()
  @ArrayMinSize(1)
  stakeholderUsers: CreateOpinionHelpStakeholderUserDto[];
}
