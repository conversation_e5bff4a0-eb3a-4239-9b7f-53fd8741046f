import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class OpinionHelpAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  opinionId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  @IsOptional()
  financialQuestionAnswer?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @Max(5)
  @IsOptional()
  impactQuestionAnswer?: number;
}

export class CreateOpinionHelpAnswerDto {
  @ApiProperty({ type: String })
  @IsString()
  token: string;

  @ApiProperty({ type: OpinionHelpAnswerDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => OpinionHelpAnswerDto)
  answers: OpinionHelpAnswerDto[];
}
