import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { StakeholderTypeEnum } from '../../../enums/stakeholder-type.enum';

export class GetStakeholderDto {
  @ApiProperty({ type: Number, enum: StakeholderTypeEnum, required: false })
  @Transform(({ value }) => +value)
  @IsEnum(StakeholderTypeEnum)
  @IsOptional()
  type?: StakeholderTypeEnum;
}
