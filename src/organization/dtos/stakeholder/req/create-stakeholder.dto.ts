import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, MinLength } from 'class-validator';
import { StakeholderTypeEnum } from '../../../enums/stakeholder-type.enum';

export class CreateStakeholderDto {
  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ type: Number, enum: StakeholderTypeEnum })
  @IsEnum(StakeholderTypeEnum)
  type: StakeholderTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  expectation?: string;
}
