import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateStakeholderUserExcelDto {
  @ApiProperty({ type: String, format: 'binary' })
  file: Express.Multer.File;

  @ApiProperty({ type: Number })
  @Transform(({ value }) => +value)
  @IsNumber()
  stakeholderId: number;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  isCustom?: boolean;
}
