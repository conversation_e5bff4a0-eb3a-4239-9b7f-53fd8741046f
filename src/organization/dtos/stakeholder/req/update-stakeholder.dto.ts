import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { StakeholderMethodTypeEnum } from '../../../enums/stakeholder-method-type.enum';
import { StakeholderFrequencyTypeEnum } from '../../../enums/stakeholder-frequency-type.enum';
import { StakeholderTypeEnum } from '../../../enums/stakeholder-type.enum';
import { Type } from 'class-transformer';

class UpdateStakeholderCommunicationDto {
  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  id?: number;

  @ApiProperty({
    type: Number,
    enum: StakeholderMethodTypeEnum,
    required: false,
  })
  @IsEnum(StakeholderMethodTypeEnum)
  @IsOptional()
  methodType?: StakeholderMethodTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  methodText?: string;

  @ApiProperty({
    type: Number,
    enum: StakeholderFrequencyTypeEnum,
    required: false,
  })
  @IsEnum(StakeholderFrequencyTypeEnum)
  @IsOptional()
  frequencyType?: StakeholderFrequencyTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  frequencyText?: string;
}

export class UpdateStakeholderDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  stakeholderId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: Number, enum: StakeholderTypeEnum, required: false })
  @IsEnum(StakeholderTypeEnum)
  @IsOptional()
  type?: StakeholderTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  expectation?: string;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isCustom?: boolean;

  @ApiProperty({
    type: UpdateStakeholderCommunicationDto,
    isArray: true,
    required: false,
  })
  @ValidateNested({ each: true })
  @Type(() => UpdateStakeholderCommunicationDto)
  @IsArray()
  @IsOptional()
  communications?: UpdateStakeholderCommunicationDto[];
}
