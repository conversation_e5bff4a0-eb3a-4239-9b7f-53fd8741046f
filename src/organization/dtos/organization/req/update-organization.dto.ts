import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class UpdateOrganizationCountryDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  countryId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  currencyId: number;
}

export class UpdateOrganizationDto {
  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  address?: string;

  @ApiProperty({ type: String, required: false })
  @IsUrl()
  @IsOptional()
  image?: string;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  employeeNumber?: number;

  @ApiProperty({
    type: UpdateOrganizationCountryDto,
    isArray: true,
    required: false,
  })
  @ValidateNested({ each: true })
  @Type(() => UpdateOrganizationCountryDto)
  @IsArray()
  @IsOptional()
  countries?: UpdateOrganizationCountryDto[];

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  sectorIds?: number[];
}
