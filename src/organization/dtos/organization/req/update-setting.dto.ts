import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';
import { OrganizationSettingEnum } from '../../../enums/organization-setting.enum';

export class UpdateSettingDto {
  @ApiProperty({ type: String, enum: OrganizationSettingEnum })
  @IsEnum(OrganizationSettingEnum)
  key: OrganizationSettingEnum;

  @ApiProperty({ type: String })
  @IsString()
  value: string;
}
