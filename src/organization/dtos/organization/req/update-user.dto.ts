import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class UpdateUserDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  userId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  roleId?: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsNumber({}, { each: true })
  @IsOptional()
  departmentIds?: number[];
}
