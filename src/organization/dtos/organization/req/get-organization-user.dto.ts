import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetOrganizationUserDto {
  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value == 'true')
  @IsBoolean()
  @IsOptional()
  showPending?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value == 'true')
  @IsBoolean()
  @IsOptional()
  showSubsidiaries?: boolean;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  subsidiaryId?: number;
}
