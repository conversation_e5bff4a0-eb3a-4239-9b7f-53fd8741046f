import { Expose, Transform, Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { OrganizationTypeEnum } from '../../../enums/organization-type.enum';

class OrganizationDto {
  @Expose()
  @IsNumber()
  id: number;

  @Expose()
  @IsString()
  name: string;

  @Expose()
  @IsEnum(OrganizationTypeEnum)
  organizationType: OrganizationTypeEnum;
}

export class OrganizationUserResDto {
  @Expose()
  @IsNumber()
  @IsOptional()
  id?: number;

  @Expose({ groups: ['userProfile'] })
  @IsString()
  @IsOptional()
  name?: string;

  @Expose({ groups: ['userProfile'] })
  @IsString()
  @IsOptional()
  surname?: string;

  @Expose()
  @IsString()
  @Transform(({ obj }) => {
    if (!obj.name) {
      return undefined;
    }
    return obj.name + (obj.surname ? ' ' + obj.surname : '');
  })
  @IsOptional()
  fullName?: string;

  @Expose()
  @IsString()
  email: string;

  @Expose()
  @IsString()
  phone: string;

  @Expose()
  @IsString()
  @IsOptional()
  image?: string;

  @Expose()
  @IsString()
  @IsOptional()
  description?: string;

  @Expose()
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @Expose()
  @IsBoolean()
  @IsOptional()
  isPending?: boolean;

  @Expose()
  @IsString()
  @IsOptional()
  lastActivity?: Date;

  @Expose()
  @IsString()
  createdAt: Date;

  @Expose()
  @IsString()
  @IsOptional()
  title?: string;

  @Expose()
  @IsEnum(OrganizationTypeEnum)
  @IsOptional()
  organizationType?: OrganizationTypeEnum;

  @Expose()
  @ValidateNested()
  @Type(() => OrganizationDto)
  @IsOptional()
  organization?: OrganizationDto;

  @Expose()
  @ValidateNested()
  @Type(() => OrganizationDto)
  @IsOptional()
  mainOrganization?: OrganizationDto;
}
