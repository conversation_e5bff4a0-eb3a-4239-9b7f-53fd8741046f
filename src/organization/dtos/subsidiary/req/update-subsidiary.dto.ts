import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateSubsidiaryDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  id: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  organizationName?: string;

  @ApiProperty({ type: String, required: false })
  @Transform(({ value }) => value.toLowerCase())
  @IsEmail()
  @IsOptional()
  managerEmail?: string;
}
