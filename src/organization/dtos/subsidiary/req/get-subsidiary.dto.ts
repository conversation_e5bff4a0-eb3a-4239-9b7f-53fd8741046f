import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetSubsidiaryDto {
  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  includeMainCompany?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;
}
