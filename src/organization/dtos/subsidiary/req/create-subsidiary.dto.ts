import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayUnique,
  IsArray,
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import { OrganizationTypeEnum } from '../../../enums/organization-type.enum';
import { Transform } from 'class-transformer';
import { AffiliateTypeEnum } from '../../../enums/affiliate-type.enum';

export class CreateSubsidiaryDto {
  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  organizationName: string;

  @ApiProperty({ type: String })
  @Transform(({ value }) => value.toLowerCase())
  @IsEmail()
  email: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  surname?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  phone?: string;

  @ApiProperty({ type: Number, enum: OrganizationTypeEnum })
  @IsEnum(OrganizationTypeEnum)
  organizationType: OrganizationTypeEnum;

  @ApiProperty({ type: Number, enum: AffiliateTypeEnum, required: false })
  @IsEnum(AffiliateTypeEnum)
  @IsOptional()
  affiliateType?: AffiliateTypeEnum;

  @ApiProperty({ type: Number, isArray: true })
  @IsArray()
  @ArrayUnique()
  @IsNumber({}, { each: true })
  permissionIds: number[];
}
