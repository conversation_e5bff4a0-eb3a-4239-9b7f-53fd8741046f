import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { StakeholderTypeEnum } from '../../../enums/stakeholder-type.enum';

export class GetStakeholderDto {
  @ApiProperty({ type: Number })
  @Transform(({ value }) => +value)
  @IsNumber()
  subsidiaryId: number;

  @ApiProperty({ type: Number, enum: StakeholderTypeEnum, required: false })
  @Transform(({ value }) => +value)
  @IsEnum(StakeholderTypeEnum)
  @IsOptional()
  type?: StakeholderTypeEnum;
}
