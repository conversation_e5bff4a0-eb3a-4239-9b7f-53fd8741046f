import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { OrganizationTypeEnum } from '../../../enums/organization-type.enum';

export class GetSubsidiaryDetailDto {
  @ApiProperty({ type: Number })
  @Transform(({ value }) => +value)
  @IsNumber()
  id: number;

  @ApiProperty({ type: Number, enum: OrganizationTypeEnum })
  @Transform(({ value }) => +value)
  @IsEnum(OrganizationTypeEnum)
  organizationType: OrganizationTypeEnum;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  isDeleted?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value == 'true')
  @IsBoolean()
  @IsOptional()
  showPending?: boolean;
}
