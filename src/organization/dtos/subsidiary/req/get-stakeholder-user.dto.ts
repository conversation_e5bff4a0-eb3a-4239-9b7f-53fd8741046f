import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetStakeholderUserDto {
  @ApiProperty({ type: Number })
  @Transform(({ value }) => +value)
  @IsNumber()
  subsidiaryId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  @Transform(({ value }) => +value)
  stakeholderId: number;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value == 'true')
  @IsBoolean()
  @IsOptional()
  isCustom?: boolean;
}
