import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { Organization } from '@prisma/client';
import { UtilService } from '../../common/services/util.service';
import { UserDto } from '../../common/dtos/user.dto';
import { UpdateOrganizationDto } from '../dtos/organization/req/update-organization.dto';
import { UpdateUserDto } from '../dtos/organization/req/update-user.dto';
import { InviteUserDto } from '../dtos/organization/req/invite-user.dto';
import { OrganizationTypeEnum } from '../enums/organization-type.enum';
import { plainToInstance } from 'class-transformer';
import { OrganizationUserResDto } from '../dtos/organization/res/organization-user.res.dto';
import { GetOrganizationUserDto } from '../dtos/organization/req/get-organization-user.dto';
import { DeleteOrganizationDto } from '../dtos/organization/req/delete-organization.dto';
import { UserService } from '../../common/services/user.service';
import { DeleteInvitedUserDto } from '../dtos/organization/req/delete-invited-user.dto';
import { DeleteOrganizationUserDto } from '../dtos/organization/req/delete-organization-user.dto';
import { FileManagerService } from '../../file-manager/file-manager.service';
import { MailService } from '../../mail/mail.service';
import { UpdateSettingDto } from '../dtos/organization/req/update-setting.dto';
import { OrganizationSettingEnum } from '../enums/organization-setting.enum';
import { QuickTypeEnum } from '../../quick-start/enums/quick-type.enum';
import { QuickStartService } from '../../quick-start/quick-start.service';
import { Request } from 'express';
import { SubsidiaryService } from './subsidiary.service';
import { ApiClientService } from '../../common/services/api-client.service';
import { PermissionEnum } from '../../common/enums/permission.enum';
import { HelpStatusEnum } from '../../analysis/enums/help-status.enum';
import { UserPendingTypeEnum } from '../../mail/enums/user-pending-type.enum';
import { addMonths } from 'date-fns';
import { RoleEnum } from '../../common/enums/role.enum';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class OrganizationService {
  private readonly logger = new Logger(OrganizationService.name);
  private readonly FRONT_URL: string;

  constructor(
    private prismaService: PrismaService,
    private configService: ConfigService,
    private subsidiaryService: SubsidiaryService,
    private userService: UserService,
    private fileManagerService: FileManagerService,
    private mailService: MailService,
    private quickStartService: QuickStartService,
    private apiClientService: ApiClientService,
    private utilService: UtilService,
  ) {
    this.FRONT_URL = this.configService.getOrThrow('FRONT_URL');
  }

  async getOrganizations(user: UserDto) {
    const organizations = await this.prismaService.organization.findMany({
      select: { id: true, name: true, image: true, organizationType: true },
      where: { userOrganizations: { some: { userId: user.id } } },
    });
    return organizations;
  }

  async getOrganizationProfile(user: UserDto) {
    const organization = await this.prismaService.organization.findFirstOrThrow(
      {
        select: {
          id: true,
          name: true,
          image: true,
          address: true,
          employeeNumber: true,
          materialType: true,
          organizationType: true,
          affiliateType: true,
          createdAt: true,
          relationOrganizations: true,
          countries: { select: { countryId: true, currencyId: true } },
          settings: { select: { key: true, value: true } },
          sectors: { select: { sector: { select: { id: true, name: true } } } },
        },
        where: { id: user.organizationId },
      },
    );
    organization['affiliateCount'] = organization.relationOrganizations.filter(
      (ro) => ro.relationType == OrganizationTypeEnum.AFFILIATE,
    ).length;
    organization['supplierCount'] = organization.relationOrganizations.filter(
      (ro) => ro.relationType == OrganizationTypeEnum.SUPPLIER,
    ).length;
    const orgRelation = await this.prismaService.organizationRelation.findFirst(
      {
        select: {
          relationType: true,
          mainOrganization: {
            select: {
              id: true,
              name: true,
              organizationType: true,
              affiliateType: true,
            },
          },
        },
        where: { relationOrganizationId: user.organizationId },
      },
    );
    organization['mainOrganization'] = null;
    if (orgRelation) {
      organization['mainOrganization'] = orgRelation.mainOrganization;
    }
    const isCarbonAccess = user.permissions.some(
      (p) => p.key == PermissionEnum.CARBON,
    );
    if (isCarbonAccess) {
      organization['facilityCount'] = (
        await this.apiClientService.getFacilities(user)
      ).length;
    }
    organization['sectorList'] = organization.sectors.map((s) => s.sector);
    delete organization.relationOrganizations;
    delete organization.sectors;
    return organization;
  }

  async getOrganizationUsers(
    user: UserDto,
    dto: GetOrganizationUserDto,
  ): Promise<OrganizationUserResDto[]> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const myCompany = await this.prismaService.organization.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        organizationType: true,
      },
      where: { id: dto.subsidiaryId },
    });
    const userList = [];
    const orgUsers = await this.prismaService.user.findMany({
      where: {
        userOrganizations: {
          some: {
            organizationId: dto.subsidiaryId,
            role: { key: { not: RoleEnum.EMPLOYEE } },
          },
        },
      },
      include: {
        userOrganizations: { where: { organizationId: dto.subsidiaryId } },
      },
    });
    for (const u of orgUsers) {
      u['title'] = u.userOrganizations[0].title;
      u['organization'] = myCompany;
    }
    userList.push(...orgUsers);
    if (dto.showSubsidiaries) {
      const affiliates = await this.prismaService.organization.findMany({
        select: {
          id: true,
          name: true,
          organizationType: true,
          userOrganizations: {
            where: { role: { key: { not: RoleEnum.EMPLOYEE } } },
            include: { user: true },
          },
          mainOrganizations: { select: { mainOrganization: true } },
        },
        where: {
          organizationType: OrganizationTypeEnum.AFFILIATE,
          mainOrganizations: {
            some: { mainOrganizationId: dto.subsidiaryId },
          },
        },
      });
      const affiliateUsers = affiliates
        .map((org) => {
          return org.userOrganizations.map((uo) => {
            uo.user['title'] = uo.title;
            uo.user['organization'] = org;
            uo.user['mainOrganization'] =
              org.mainOrganizations[0].mainOrganization;
            return uo.user;
          });
        })
        .flat(1);
      userList.push(...affiliateUsers);
      const suppliers = await this.prismaService.organization.findMany({
        select: {
          id: true,
          name: true,
          organizationType: true,
          userOrganizations: {
            where: { role: { key: { not: RoleEnum.EMPLOYEE } } },
            include: { user: true },
          },
          mainOrganizations: { select: { mainOrganization: true } },
        },
        where: {
          organizationType: OrganizationTypeEnum.SUPPLIER,
          mainOrganizations: {
            some: { mainOrganizationId: dto.subsidiaryId },
          },
        },
      });
      const supplierUsers = suppliers
        .map((org) => {
          return org.userOrganizations.map((uo) => {
            uo.user['title'] = uo.title;
            uo.user['organization'] = org;
            uo.user['mainOrganization'] =
              org.mainOrganizations[0].mainOrganization;
            return uo.user;
          });
        })
        .flat(1);
      userList.push(...supplierUsers);
    }
    this.utilService.moveToBeginningOfObject(user.id, userList, 'id');
    const result = plainToInstance(OrganizationUserResDto, userList, {
      excludeExtraneousValues: true,
    });
    if (dto.showPending) {
      const pendingUsers = await this.prismaService.userPending.findMany({
        where: {
          organizationId: dto.subsidiaryId,
          OR: [{ roleId: null }, { role: { key: { not: RoleEnum.EMPLOYEE } } }],
        },
        orderBy: { createdAt: 'asc' },
      });
      const pendingResult = plainToInstance(
        OrganizationUserResDto,
        pendingUsers,
        {
          excludeExtraneousValues: true,
        },
      );
      pendingResult.forEach((p) => (p.isPending = true));
      result.push(...pendingResult);
    }
    return result;
  }

  async getOrganizationCountries(user: UserDto) {
    const countries = await this.prismaService.country.findMany({
      select: { id: true, name: true },
      where: {
        organizationCountries: {
          some: { organizationId: user.organizationId },
        },
      },
      orderBy: { name: 'asc' },
    });
    return countries;
  }

  async getOrganizationIds(
    user: UserDto,
    options?: { isDeleted?: boolean },
  ): Promise<number[]> {
    const organizationIds = (
      await this.subsidiaryService.getSubsidiaries(user, {
        includeMainCompany: true,
        isDeleted: options?.isDeleted,
      })
    ).map((s) => s.id);
    return organizationIds;
  }

  async getSources() {
    const sources = await this.prismaService.sectorSource.findMany({
      select: { id: true, name: true },
    });
    return sources;
  }

  async getSectors() {
    const sources = await this.prismaService.sectorSource.findMany({
      select: { id: true, name: true },
    });
    const sectors = await this.prismaService.sector.findMany({
      select: { id: true, name: true, sourceIds: true },
      orderBy: { name: 'asc' },
    });
    for (const sector of sectors) {
      sector['sources'] = sources.filter((source) =>
        this.utilService.jsonToNumberList(sector.sourceIds).includes(source.id),
      );
      delete sector.sourceIds;
    }
    return sectors;
  }

  async inviteUser(user: UserDto, dto: InviteUserDto, request: Request) {
    const permissionIds = (
      await this.prismaService.userPermission.findMany({
        where: {
          organizationId: user.organizationId,
          userId: user.id,
        },
      })
    ).map((up) => up.permissionId);
    const token = this.utilService.getRandomString(
      this.utilService.getRandomInteger(100, 150),
    );
    const pendingUser = await this.prismaService.$transaction(async (tx) => {
      const pendingUser = await tx.userPending.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          type: UserPendingTypeEnum.INVITE_TO_ORGANIZATION,
          email: dto.email,
          name: dto.name,
          surname: dto.surname,
          phone: dto.phone,
          permissionIds: permissionIds,
          token: token,
          tokenExpiredAt: addMonths(new Date(), 1),
        },
      });
      await this.quickStartService.completeQuickStart(
        user,
        QuickTypeEnum.ORGANIZATION_INVITE,
        tx,
      );
      return pendingUser;
    });
    const url =
      this.FRONT_URL + `/invitation?token=${token}&email=${pendingUser.email}`;
    this.mailService
      .sendOrganizationInvite({
        pending: pendingUser,
        url: url,
      })
      .catch((err) => this.logger.error(err));
    return { url: url };
  }

  async updateOrganization(
    user: UserDto,
    dto: UpdateOrganizationDto,
  ): Promise<void> {
    const organization = await this.prismaService.organization.findFirstOrThrow(
      {
        where: { id: user.organizationId },
      },
    );
    await this.prismaService.$transaction(async (tx) => {
      const updatedOrganization = await tx.organization.update({
        where: { id: user.organizationId },
        data: {
          name: dto.name,
          address: dto.address,
          image: dto.image,
          employeeNumber: dto.employeeNumber,
        },
      });
      if (dto.countries?.length === 0) {
        await tx.organizationCountry.deleteMany({
          where: { organizationId: user.organizationId },
        });
      }
      if (dto.countries?.length > 0) {
        const orgCountries = await tx.organizationCountry.findMany({
          where: {
            organizationId: user.organizationId,
            OR: dto.countries.map((c) => {
              return {
                AND: [{ countryId: c.countryId, currencyId: c.currencyId }],
              };
            }),
          },
        });
        const createCountries = dto.countries.filter(
          (c) =>
            !orgCountries.some(
              (oc) =>
                oc.countryId == c.countryId && oc.currencyId == c.currencyId,
            ),
        );
        await tx.organizationCountry.deleteMany({
          where: {
            id: { notIn: orgCountries.map((oc) => oc.id) },
            organizationId: user.organizationId,
          },
        });
        await tx.organizationCountry.createMany({
          data: createCountries.map((c) => ({
            organizationId: user.organizationId,
            countryId: c.countryId,
            currencyId: c.currencyId,
          })),
        });
      }
      if (dto.sectorIds?.length === 0) {
        await tx.organizationSector.deleteMany({
          where: { organizationId: user.organizationId },
        });
      }
      if (dto.sectorIds?.length > 0) {
        const orgSectors = await tx.organizationSector.findMany({
          where: {
            organizationId: user.organizationId,
            sectorId: { in: dto.sectorIds },
          },
        });
        const createSectorIds = dto.sectorIds.filter(
          (sectorId) => !orgSectors.some((os) => os.sectorId == sectorId),
        );
        await tx.organizationSector.deleteMany({
          where: {
            id: { notIn: orgSectors.map((os) => os.id) },
            organizationId: user.organizationId,
          },
        });
        await tx.organizationSector.createMany({
          data: createSectorIds.map((sectorId) => ({
            organizationId: user.organizationId,
            sectorId: sectorId,
          })),
        });
      }
      await this.quickStartService.completeQuickStart(
        user,
        QuickTypeEnum.ORGANIZATION_PROFILE_COMPLETE,
        tx,
      );
      const isCarbonAccess = user.permissions.some(
        (p) => p.key == PermissionEnum.CARBON,
      );
      if (isCarbonAccess) {
        await this.apiClientService.updateOrganization(
          user,
          updatedOrganization,
        );
      }
    });
    if (
      organization.image &&
      (dto.image === null || (dto.image && dto.image != organization.image))
    ) {
      this.fileManagerService
        .deleteFile(user, { url: organization.image }, true)
        .catch((err) => this.logger.error(err));
    }
  }

  async updateUser(user: UserDto, dto: UpdateUserDto): Promise<void> {
    await this.prismaService.userOrganization.updateMany({
      where: { userId: dto.userId, organizationId: user.organizationId },
      data: { roleId: dto.roleId, title: dto.title },
    });
  }

  async updateSettings(user: UserDto, dto: UpdateSettingDto): Promise<void> {
    if (
      dto.key == OrganizationSettingEnum.INTERNAL_PERCENT ||
      dto.key == OrganizationSettingEnum.EXTERNAL_PERCENT
    ) {
      return await this.updateStakeholderSettings(user, dto);
    }
    const setting = await this.prismaService.organizationSetting.findFirst({
      where: {
        organizationId: user.organizationId,
        key: dto.key,
      },
    });
    await this.prismaService.organizationSetting.upsert({
      where: { id: setting?.id ?? 0 },
      create: {
        organizationId: user.organizationId,
        key: dto.key,
        value: dto.value,
      },
      update: { value: dto.value },
    });
  }

  private async updateStakeholderSettings(
    user: UserDto,
    dto: UpdateSettingDto,
  ): Promise<void> {
    const internalSetting =
      await this.prismaService.organizationSetting.findFirst({
        where: {
          organizationId: user.organizationId,
          key: OrganizationSettingEnum.INTERNAL_PERCENT,
        },
      });
    const externalSetting =
      await this.prismaService.organizationSetting.findFirst({
        where: {
          organizationId: user.organizationId,
          key: OrganizationSettingEnum.EXTERNAL_PERCENT,
        },
      });
    let internalPercent = 0;
    let externalPercent = 0;
    if (dto.key == OrganizationSettingEnum.INTERNAL_PERCENT) {
      internalPercent = +dto.value;
      externalPercent = 1 - internalPercent;
    } else if (dto.key == OrganizationSettingEnum.EXTERNAL_PERCENT) {
      externalPercent = +dto.value;
      internalPercent = 1 - externalPercent;
    }
    await this.prismaService.organizationSetting.upsert({
      where: { id: internalSetting?.id ?? 0 },
      create: {
        organizationId: user.organizationId,
        key: OrganizationSettingEnum.INTERNAL_PERCENT,
        value: internalPercent.toFixed(2),
      },
      update: { value: internalPercent.toFixed(2) },
    });
    await this.prismaService.organizationSetting.upsert({
      where: { id: externalSetting?.id ?? 0 },
      create: {
        organizationId: user.organizationId,
        key: OrganizationSettingEnum.EXTERNAL_PERCENT,
        value: externalPercent.toFixed(2),
      },
      update: { value: externalPercent.toFixed(2) },
    });
  }

  async deleteOrganization(
    user: UserDto,
    dto: DeleteOrganizationDto,
    request: Request,
    otherOrganizations: Organization[],
  ) {
    const accessToken = await this.prismaService.$transaction(async (tx) => {
      await tx.organization.delete({
        where: { id: user.organizationId },
      });
      await tx.userOrganization.deleteMany({
        where: { organizationId: user.organizationId },
      });
      await tx.userPending.deleteMany({
        where: { organizationId: user.organizationId },
      });
      return await this.userService.createAccessToken({
        userId: user.id,
        organizationId: otherOrganizations[0].id,
        request: request,
        tx: tx,
      });
    });
    return {
      organizationId: otherOrganizations[0].id,
      organizationName: otherOrganizations[0].name,
      accessToken: accessToken,
    };
  }

  async deleteInvitedUser(
    user: UserDto,
    dto: DeleteInvitedUserDto,
  ): Promise<void> {
    await this.prismaService.userPending.deleteMany({
      where: { organizationId: user.organizationId, email: dto.email },
    });
  }

  async deleteOrganizationUser(user: UserDto, dto: DeleteOrganizationUserDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.situationHelp.deleteMany({
        where: {
          helpedUserId: dto.userId,
          status: HelpStatusEnum.PENDING,
          report: { organizationId: user.organizationId },
        },
      });
      await tx.trendHelp.deleteMany({
        where: {
          helpedUserId: dto.userId,
          status: HelpStatusEnum.PENDING,
          trend: { report: { organizationId: user.organizationId } },
        },
      });
      await tx.riskHelp.deleteMany({
        where: {
          helpedUserId: dto.userId,
          status: HelpStatusEnum.PENDING,
          risk: { report: { organizationId: user.organizationId } },
        },
      });
      await tx.chainHelp.deleteMany({
        where: {
          helpedUserId: dto.userId,
          status: HelpStatusEnum.PENDING,
          chain: { report: { organizationId: user.organizationId } },
        },
      });
      await tx.capitalHelp.deleteMany({
        where: {
          helpedUserId: dto.userId,
          status: HelpStatusEnum.PENDING,
          capital: { report: { organizationId: user.organizationId } },
        },
      });
      await tx.userSession.deleteMany({
        where: { organizationId: user.organizationId, userId: dto.userId },
      });
      await tx.userOrganization.deleteMany({
        where: { organizationId: user.organizationId, userId: dto.userId },
      });
    });
  }
}
