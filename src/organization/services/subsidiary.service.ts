import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { OrganizationTypeEnum } from '../enums/organization-type.enum';
import { UserDto } from '../../common/dtos/user.dto';
import { CreateSubsidiaryDto } from '../dtos/subsidiary/req/create-subsidiary.dto';
import { UtilService } from '../../common/services/util.service';
import { MailService } from '../../mail/mail.service';
import { RoleEnum } from '../../common/enums/role.enum';
import { ApiClientService } from '../../common/services/api-client.service';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { StakeholderService } from './stakeholder.service';
import { GetSubsidiaryDetailDto } from '../dtos/subsidiary/req/get-subsidiary-detail.dto';
import { UpdateSubsidiaryDto } from '../dtos/subsidiary/req/update-subsidiary.dto';
import { DeleteSubsidiaryDto } from '../dtos/subsidiary/req/delete-subsidiary.dto';
import { RecoverSubsidiaryDto } from '../dtos/subsidiary/req/recover-subsidiary.dto';
import { GetAffiliateDto } from '../dtos/subsidiary/req/get-affiliate.dto';
import { GetSupplierDto } from '../dtos/subsidiary/req/get-supplier.dto';
import { GetSubsidiaryReportDto } from '../dtos/subsidiary/req/get-subsidiary-report.dto';
import { PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { GetStakeholderDto } from '../dtos/subsidiary/req/get-stakeholder.dto';
import { GetStakeholderUserDto } from '../dtos/subsidiary/req/get-stakeholder-user.dto';
import { ProcedureService } from '../../procedure/procedure.service';
import { GetProcedureCategoryDto } from '../dtos/subsidiary/req/get-procedure-category.dto';
import { GetProcedurePolicyDto } from '../dtos/subsidiary/req/get-procedure-policy.dto';
import { QuickTypeEnum } from '../../quick-start/enums/quick-type.enum';
import { QuickStartService } from '../../quick-start/quick-start.service';
import { GetSubsidiaryDto } from '../dtos/subsidiary/req/get-subsidiary.dto';
import { GetSubsidiaryReportFileDto } from '../dtos/subsidiary/req/get-subsidiary-report-file.dto';
import { UserPendingTypeEnum } from '../../mail/enums/user-pending-type.enum';
import { Request } from 'express';
import { PermissionEnum } from '../../common/enums/permission.enum';
import { addMonths } from 'date-fns';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SubsidiaryService {
  private readonly logger = new Logger(SubsidiaryService.name);
  private readonly FRONT_URL: string;

  constructor(
    private prismaService: PrismaService,
    private configService: ConfigService,
    private mailService: MailService,
    private apiClientService: ApiClientService,
    private stakeholderService: StakeholderService,
    private procedureService: ProcedureService,
    private quickStartService: QuickStartService,
    private utilService: UtilService,
  ) {
    this.FRONT_URL = this.configService.getOrThrow('FRONT_URL');
  }

  async getSubsidiaries(user: UserDto, dto: GetSubsidiaryDto) {
    const subsidiaries = await this.prismaService.organization.findMany({
      select: { id: true, name: true, organizationType: true },
      where: {
        organizationType: {
          in: [OrganizationTypeEnum.AFFILIATE, OrganizationTypeEnum.SUPPLIER],
        },
        mainOrganizations: {
          some: { mainOrganizationId: user.organizationId },
        },
        deletedAt: dto.isDeleted ? {} : undefined,
      },
      orderBy: { organizationType: 'asc' },
    });
    if (dto.includeMainCompany) {
      const mainCompany =
        await this.prismaService.organization.findFirstOrThrow({
          select: { id: true, name: true, organizationType: true },
          where: { id: user.organizationId },
        });
      subsidiaries.unshift(mainCompany);
    }
    return subsidiaries;
  }

  async getAffiliates(user: UserDto, dto: GetAffiliateDto) {
    const affiliates = await this.prismaService.organization.findMany({
      select: {
        id: true,
        name: true,
        address: true,
        image: true,
        employeeNumber: true,
        organizationType: true,
        affiliateType: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
        userOrganizations: {
          select: {
            user: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                phone: true,
                userPermissions: { select: { permission: true } },
              },
            },
          },
          where: {
            user: {},
            role: { key: RoleEnum.MANAGER },
            deletedAt: dto.isDeleted ? {} : undefined,
          },
        },
        pendingUsers: {
          select: {
            name: true,
            surname: true,
            email: true,
            phone: true,
          },
          where: { role: { key: RoleEnum.MANAGER } },
        },
        relationOrganizations: {
          select: {
            relationOrganization: { select: { id: true, name: true } },
          },
          where: { deletedAt: dto.isDeleted ? {} : undefined },
        },
        carbonSession: true,
        countries: {
          select: {
            country: { select: { name: true } },
            currency: { select: { name: true } },
          },
        },
        sectors: { select: { sector: { select: { id: true, name: true } } } },
      },
      where: {
        id: dto.id,
        deletedAt: dto.isDeleted ? {} : undefined,
        organizationType: OrganizationTypeEnum.AFFILIATE,
        mainOrganizations: {
          some: {
            mainOrganizationId: user.organizationId,
            deletedAt: dto.isDeleted ? {} : undefined,
          },
        },
      },
    });
    const carbonOrganizationIds = affiliates
      .filter((s) => s.carbonSession)
      .map((a) => a.carbonSession.carbonOrganizationId);
    const { reports } = await this.apiClientService.getReports(
      carbonOrganizationIds,
    );
    const facilityCounts =
      await this.apiClientService.getSubsidiaryFacilityCounts(
        carbonOrganizationIds,
      );
    for (const a of affiliates) {
      a['hasProcedurePermission'] = false;
      if (a.userOrganizations.length > 0) {
        a['manager'] = plainToInstance(
          UserDataDto,
          a.userOrganizations[0].user,
          { excludeExtraneousValues: true, groups: ['contact'] },
        );
        a['hasProcedurePermission'] =
          a.userOrganizations[0].user.userPermissions.some(
            (up) => up.permission.key == PermissionEnum.PROCEDURE,
          );
      } else {
        a['manager'] =
          a.pendingUsers.length > 0
            ? plainToInstance(UserDataDto, a.pendingUsers[0], {
                excludeExtraneousValues: true,
                groups: ['contact'],
              })
            : undefined;
        if (a['manager']) {
          a['manager'].isPending = true;
        }
      }
      a['suppliers'] = a.relationOrganizations.map(
        (r) => r.relationOrganization,
      );
      let totalGasDouble = 0;
      if (a.carbonSession) {
        totalGasDouble =
          reports
            .filter((r) => r.companyId == a.carbonSession.carbonOrganizationId)
            .reduce((sum, r) => sum + r.totalGasDouble, 0) ?? 0;
        a['facilityCount'] =
          facilityCounts.find(
            (f) => f.companyId == a.carbonSession.carbonOrganizationId,
          )?.facilityCount ?? 0;
      }
      a['totalGas'] = this.utilService.numberFormat(totalGasDouble);
      a['countryList'] = a.countries.map((c) => {
        return { country: c.country.name, currency: c.currency.name };
      });
      a['sectorList'] = a.sectors.map((s) => s.sector);
      delete a.userOrganizations;
      delete a.pendingUsers;
      delete a.relationOrganizations;
      delete a.carbonSession;
      delete a.countries;
      delete a.sectors;
    }
    return affiliates;
  }

  async getSuppliers(user: UserDto, dto: GetSupplierDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const suppliers = await this.prismaService.organization.findMany({
      select: {
        id: true,
        name: true,
        address: true,
        image: true,
        employeeNumber: true,
        organizationType: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
        userOrganizations: {
          select: {
            user: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                phone: true,
                userPermissions: { select: { permission: true } },
              },
            },
          },
          where: {
            role: { key: RoleEnum.MANAGER },
            deletedAt: dto.isDeleted ? {} : undefined,
          },
        },
        pendingUsers: {
          select: {
            name: true,
            surname: true,
            email: true,
            phone: true,
          },
          where: { role: { key: RoleEnum.MANAGER } },
        },
        mainOrganizations: {
          select: {
            mainOrganization: {
              select: { id: true, name: true, organizationType: true },
            },
          },
          where: { deletedAt: dto.isDeleted ? {} : undefined },
        },
        carbonSession: true,
        countries: {
          select: {
            country: { select: { name: true } },
            currency: { select: { name: true } },
          },
        },
        sectors: { select: { sector: { select: { id: true, name: true } } } },
      },
      where: {
        id: dto.id,
        deletedAt: dto.isDeleted ? {} : undefined,
        organizationType: OrganizationTypeEnum.SUPPLIER,
        mainOrganizations: {
          some: {
            mainOrganizationId: dto.subsidiaryId,
            deletedAt: dto.isDeleted ? {} : undefined,
          },
        },
      },
    });
    const carbonOrganizationIds = suppliers
      .filter((s) => s.carbonSession)
      .map((s) => s.carbonSession.carbonOrganizationId);
    const { reports } = await this.apiClientService.getReports(
      carbonOrganizationIds,
    );
    const facilityCounts =
      await this.apiClientService.getSubsidiaryFacilityCounts(
        carbonOrganizationIds,
      );
    for (const s of suppliers) {
      s['hasProcedurePermission'] = false;
      if (s.userOrganizations.length > 0) {
        s['manager'] = plainToInstance(
          UserDataDto,
          s.userOrganizations[0].user,
          { excludeExtraneousValues: true, groups: ['contact'] },
        );
        s['hasProcedurePermission'] =
          s.userOrganizations[0].user.userPermissions.some(
            (up) => up.permission.key == PermissionEnum.PROCEDURE,
          );
      } else {
        s['manager'] =
          s.pendingUsers.length > 0
            ? plainToInstance(UserDataDto, s.pendingUsers[0], {
                excludeExtraneousValues: true,
                groups: ['contact'],
              })
            : undefined;
        if (s['manager']) {
          s['manager'].isPending = true;
        }
      }
      s['mainOrganization'] = s.mainOrganizations[0].mainOrganization;
      let totalGasDouble = 0;
      if (s.carbonSession) {
        totalGasDouble =
          reports
            .filter((r) => r.companyId == s.carbonSession.carbonOrganizationId)
            .reduce((sum, r) => sum + r.totalGasDouble, 0) ?? 0;
        s['facilityCount'] =
          facilityCounts.find(
            (f) => f.companyId == s.carbonSession.carbonOrganizationId,
          )?.facilityCount ?? 0;
      }
      s['totalGas'] = this.utilService.numberFormat(totalGasDouble);
      s['countryList'] = s.countries.map((c) => {
        return { country: c.country.name, currency: c.currency.name };
      });
      s['sectorList'] = s.sectors.map((s) => s.sector);
      delete s.userOrganizations;
      delete s.pendingUsers;
      delete s.mainOrganizations;
      delete s.carbonSession;
      delete s.countries;
      delete s.sectors;
    }
    return suppliers;
  }

  async getSubsidiaryDetail(user: UserDto, dto: GetSubsidiaryDetailDto) {
    let subsidiary: any;
    if (dto.organizationType == OrganizationTypeEnum.AFFILIATE) {
      const affiliates = await this.getAffiliates(user, {
        id: dto.id,
        isDeleted: dto.isDeleted,
      });
      subsidiary = affiliates[0];
    } else {
      const suppliers = await this.getSuppliers(user, {
        id: dto.id,
        subsidiaryId: user.organizationId,
        isDeleted: dto.isDeleted,
      });
      subsidiary = suppliers[0];
    }
    if (!subsidiary) {
      throw new BadRequestException('Alt şirket bulunamadı.');
    }
    const users = await this.prismaService.user.findMany({
      where: {
        userOrganizations: {
          some: {
            organizationId: dto.id,
            deletedAt: dto.isDeleted ? {} : undefined,
          },
        },
      },
      include: { userOrganizations: { where: { organizationId: dto.id } } },
    });
    subsidiary['users'] = plainToInstance(UserDataDto, users, {
      excludeExtraneousValues: true,
      groups: ['contact', 'sensitiveField'],
    });
    if (dto.showPending) {
      const pendingUsers = await this.prismaService.userPending.findMany({
        where: { organizationId: dto.id },
        orderBy: { createdAt: 'asc' },
      });
      const pendingDto = plainToInstance(UserDataDto, pendingUsers, {
        excludeExtraneousValues: true,
        groups: ['contact', 'sensitiveField'],
      });
      pendingDto.forEach((p) => (p['isPending'] = true));
      subsidiary['users'].push(...pendingDto);
    }
    return subsidiary;
  }

  async getSubsidiaryReports(user: UserDto, dto: GetSubsidiaryReportDto) {
    const { organizations, carbonOrganizationIds } =
      await this.getCarbonOrganizations(user, {
        organizationId: dto.subsidiaryId,
      });
    const { years, reports } = await this.apiClientService.getReports(
      carbonOrganizationIds,
      dto.year,
    );
    for (const r of reports) {
      const org = organizations.find(
        (org) => org.carbonSession.carbonOrganizationId == r.companyId,
      );
      r['organizationId'] = org.id;
      r['organizationName'] = org.name;
      r['organizationType'] = org.organizationType;
      delete r.companyId;
    }
    if (reports.length > 0) {
      await this.quickStartService.completeQuickStart(
        user,
        QuickTypeEnum.CARBON_GET_REPORTS,
      );
    }
    return { years: years, reports: reports };
  }

  async getSubsidiaryReportFiles(
    user: UserDto,
    dto: GetSubsidiaryReportFileDto,
  ) {
    const { organizations, carbonOrganizationIds } =
      await this.getCarbonOrganizations(user, {
        organizationId: dto.subsidiaryId,
      });
    const reports = await this.apiClientService.getSubsidiaryReportFiles(
      carbonOrganizationIds,
    );
    const usersList = await this.prismaService.user.findMany({
      select: { id: true, name: true, surname: true, email: true, image: true },
      where: { email: { in: reports.map((r) => r.email) } },
    });
    for (const r of reports) {
      const org = organizations.find(
        (org) => org.carbonSession.carbonOrganizationId == r.companyId,
      );
      const reportUser = usersList.find((u) => u.email == r.email);
      r['organizationId'] = org.id;
      r['organizationName'] = org.name;
      r['organizationType'] = org.organizationType;
      if (reportUser) {
        r['user'] = plainToInstance(UserDataDto, reportUser, {
          excludeExtraneousValues: true,
        });
      } else {
        r['user'] = { email: r.email };
      }
      delete r.companyId;
      delete r.email;
    }
    return reports;
  }

  private async getCarbonOrganizations(
    user: UserDto,
    options?: { organizationId?: number },
  ) {
    const myOrganization =
      await this.prismaService.organization.findFirstOrThrow({
        where: { id: user.organizationId },
        include: { carbonSession: true },
      });
    const subsidiaries = await this.prismaService.organization.findMany({
      where: {
        id: options?.organizationId,
        mainOrganizations: {
          some: { mainOrganizationId: user.organizationId },
        },
      },
      include: { carbonSession: true },
    });
    const affiliateIds = subsidiaries
      .filter((org) => org.organizationType == OrganizationTypeEnum.AFFILIATE)
      .map((org) => org.id);
    const suppliersOfAffiliates =
      await this.prismaService.organization.findMany({
        where: {
          id: options?.organizationId,
          mainOrganizations: {
            some: {
              mainOrganizationId: { in: affiliateIds },
            },
          },
        },
        include: { carbonSession: true },
      });
    const organizations = [...subsidiaries, ...suppliersOfAffiliates].filter(
      (org) => org.carbonSession,
    );
    if (
      myOrganization.carbonSession &&
      (!options?.organizationId || options.organizationId == myOrganization.id)
    ) {
      organizations.push(myOrganization);
    }
    const carbonOrganizationIds: number[] = organizations.map(
      (org) => org.carbonSession.carbonOrganizationId,
    );
    return {
      organizations: organizations,
      carbonOrganizationIds: carbonOrganizationIds,
    };
  }

  async getStakeholders(user: UserDto, dto: GetStakeholderDto) {
    return await this.stakeholderService.getStakeholders(dto.subsidiaryId, {
      type: dto.type,
    });
  }

  async getStakeholderUsers(user: UserDto, dto: GetStakeholderUserDto) {
    return await this.stakeholderService.getStakeholderUsers(dto.subsidiaryId, {
      stakeholderId: dto.stakeholderId,
      isCustom: dto.isCustom,
    });
  }

  async getProcedureCategories(user: UserDto, dto: GetProcedureCategoryDto) {
    return await this.procedureService.getCategories(dto.subsidiaryId, {
      procedureSlug: dto.procedureSlug,
    });
  }

  async getProcedurePolicies(user: UserDto, dto: GetProcedurePolicyDto) {
    return await this.procedureService.getOrganizationProcedurePolicy(
      dto.subsidiaryId,
      {
        categoryId: dto.categoryId,
        isCustom: dto.isCustom,
      },
    );
  }

  async createSubsidiary(user: UserDto, dto: CreateSubsidiaryDto) {
    const managerRole = await this.prismaService.role.findFirstOrThrow({
      where: { key: RoleEnum.MANAGER },
    });
    const inviterUserPermissions =
      await this.prismaService.userPermission.findMany({
        where: { userId: user.id, organizationId: user.organizationId },
      });
    const token = this.utilService.getRandomString(
      this.utilService.getRandomInteger(100, 150),
    );
    const mainOrganization =
      await this.prismaService.organization.findFirstOrThrow({
        where: { id: user.organizationId },
      });
    const { organization, pendingUser } = await this.prismaService.$transaction(
      async (tx) => {
        const organization = await tx.organization.create({
          data: {
            name: dto.organizationName,
            materialType: mainOrganization.materialType,
            organizationType: dto.organizationType,
            affiliateType: dto.affiliateType,
            mainOrganizations: {
              create: {
                mainOrganizationId: user.organizationId,
                relationType: dto.organizationType,
              },
            },
          },
        });
        await tx.userOrganization.create({
          data: {
            userId: user.id,
            organizationId: organization.id,
            roleId: managerRole.id,
          },
        });
        await tx.userPermission.createMany({
          data: inviterUserPermissions.map((up) => {
            return {
              userId: user.id,
              organizationId: organization.id,
              permissionId: up.permissionId,
            };
          }),
        });
        const pendingUser = await tx.userPending.create({
          data: {
            createdUserId: user.id,
            organizationId: organization.id,
            roleId: managerRole.id,
            type: UserPendingTypeEnum.CREATE_SUBSIDIARY,
            email: dto.email,
            name: dto.name,
            surname: dto.surname,
            phone: dto.phone,
            permissionIds: dto.permissionIds,
            token: token,
            tokenExpiredAt: addMonths(new Date(), 1),
          },
        });
        if (dto.organizationType == OrganizationTypeEnum.AFFILIATE) {
          await this.quickStartService.completeQuickStart(
            user,
            QuickTypeEnum.AFFILIATE_CREATE,
            tx,
          );
        } else if (dto.organizationType == OrganizationTypeEnum.SUPPLIER) {
          await this.quickStartService.completeQuickStart(
            user,
            QuickTypeEnum.SUPPLIER_CREATE,
            tx,
          );
        }
        return { organization: organization, pendingUser: pendingUser };
      },
    );
    const url =
      this.FRONT_URL + `/invitation?token=${token}&email=${pendingUser.email}`;
    this.mailService
      .sendOrganizationInvite({
        url: url,
        pending: pendingUser,
      })
      .catch((err) => this.logger.error(err));
    return {
      id: organization.id,
      name: organization.name,
      image: organization.image,
      invitationUrl: url,
    };
  }

  async inviteManager(
    user: UserDto,
    dto: UpdateSubsidiaryDto,
    request: Request,
    tx: Omit<PrismaClient, ITXClientDenyList>,
  ): Promise<string> {
    const managerRole = await tx.role.findFirstOrThrow({
      where: { key: RoleEnum.MANAGER },
    });
    const managerUser = await tx.user.findFirstOrThrow({
      select: { userPermissions: { where: { organizationId: dto.id } } },
      where: {
        userOrganizations: {
          some: { organizationId: dto.id, roleId: managerRole.id },
        },
      },
    });
    const permissionIds = managerUser.userPermissions.map(
      (up) => up.permissionId,
    );
    const token = this.utilService.getRandomString(
      this.utilService.getRandomInteger(100, 150),
    );
    const pendingUser = await tx.userPending.create({
      data: {
        createdUserId: user.id,
        organizationId: dto.id,
        roleId: managerRole.id,
        type: UserPendingTypeEnum.INVITE_SUBSIDIARY_MANAGER,
        email: dto.managerEmail,
        permissionIds: permissionIds,
        token: token,
        tokenExpiredAt: addMonths(new Date(), 1),
      },
    });
    const url =
      this.FRONT_URL + `/invitation?token=${token}&email=${pendingUser.email}`;
    this.mailService
      .sendOrganizationInvite({
        pending: pendingUser,
        url: url,
      })
      .catch((err) => this.logger.error(err));
    return url;
  }

  async updateSubsidiary(
    user: UserDto,
    dto: UpdateSubsidiaryDto,
    request: Request,
    managerEmailInOrg?: boolean,
  ) {
    let invitationUrl: string = null;
    await this.prismaService.$transaction(async (tx) => {
      await tx.organization.update({
        where: { id: dto.id },
        data: { name: dto.organizationName },
      });
      if (dto.managerEmail && managerEmailInOrg) {
        const orgUsers = await tx.userOrganization.findMany({
          where: { organizationId: dto.id },
          include: { user: true, role: true },
        });
        const oldManager = orgUsers.find(
          (ou) => ou.role.key == RoleEnum.MANAGER,
        );
        const newManager = orgUsers.find(
          (ou) => ou.user.email == dto.managerEmail,
        );
        if (oldManager.user.email == dto.managerEmail) {
          throw new BadRequestException('Kullanıcı zaten yönetici.');
        }
        await tx.userOrganization.update({
          where: { id: oldManager.id, organizationId: dto.id },
          data: { role: { connect: { key: RoleEnum.ASSISTANT } } },
        });
        await tx.userOrganization.update({
          where: { id: newManager.id, organizationId: dto.id },
          data: { role: { connect: { key: RoleEnum.MANAGER } } },
        });
      } else if (dto.managerEmail && !managerEmailInOrg) {
        invitationUrl = await this.inviteManager(user, dto, request, tx);
      }
    });
    return { invitationUrl: invitationUrl };
  }

  async recoverSubsidiary(
    user: UserDto,
    dto: RecoverSubsidiaryDto,
  ): Promise<void> {
    const supplierIds = (
      await this.prismaService.organizationRelation.findMany({
        where: {
          mainOrganizationId: dto.id,
          deletedAt: {},
        },
      })
    ).map((or) => or.relationOrganizationId);
    const recoverOrgIds = [dto.id, ...supplierIds];
    await this.prismaService.$transaction(async (tx) => {
      await tx.organizationRelation.updateMany({
        where: {
          OR: [
            {
              mainOrganizationId: user.organizationId,
              relationOrganizationId: dto.id,
            },
            { mainOrganizationId: dto.id },
          ],
          deletedAt: {},
        },
        data: { deletedAt: null },
      });
      await tx.userOrganization.updateMany({
        where: {
          organizationId: { in: recoverOrgIds },
          deletedAt: {},
        },
        data: { deletedAt: null },
      });
      await tx.organization.updateMany({
        where: {
          id: { in: recoverOrgIds },
          deletedAt: {},
        },
        data: { deletedAt: null },
      });
    });
  }

  async deleteSubsidiary(
    user: UserDto,
    dto: DeleteSubsidiaryDto,
  ): Promise<void> {
    const supplierIds = (
      await this.prismaService.organizationRelation.findMany({
        where: { mainOrganizationId: dto.id },
      })
    ).map((or) => or.relationOrganizationId);
    const deleteOrgIds = [dto.id, ...supplierIds];
    await this.prismaService.$transaction(async (tx) => {
      await tx.organizationRelation.deleteMany({
        where: {
          OR: [
            {
              mainOrganizationId: user.organizationId,
              relationOrganizationId: dto.id,
            },
            { mainOrganizationId: dto.id },
          ],
        },
      });
      await tx.userOrganization.deleteMany({
        where: { organizationId: { in: deleteOrgIds } },
      });
      await tx.organization.deleteMany({
        where: { id: { in: deleteOrgIds } },
      });
    });
  }
}
