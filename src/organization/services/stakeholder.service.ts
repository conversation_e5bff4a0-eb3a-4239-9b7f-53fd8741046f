import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { CreateStakeholderDto } from '../dtos/stakeholder/req/create-stakeholder.dto';
import { UpdateStakeholderDto } from '../dtos/stakeholder/req/update-stakeholder.dto';
import { Prisma, PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { CreateStakeholderUserDto } from '../dtos/stakeholder/req/create-stakeholder-user.dto';
import { DeleteStakeholderUserDto } from '../dtos/stakeholder/req/delete-stakeholder-user.dto';
import { GetStakeholderUserDto } from '../dtos/stakeholder/req/get-stakeholder-user.dto';
import { UpdateStakeholderUserDto } from '../dtos/stakeholder/req/update-stakeholder-user.dto';
import { StakeholderValidation } from '../validations/stakeholder.validation';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { GetStakeholderDto } from '../dtos/stakeholder/req/get-stakeholder.dto';
import { DeleteStakeholderDto } from '../dtos/stakeholder/req/delete-stakeholder.dto';
import * as XLSX from 'xlsx';
import { CreateStakeholderUserExcelDto } from '../dtos/stakeholder/req/create-stakeholder-user-excel.dto';

@Injectable()
export class StakeholderService {
  constructor(
    private prismaService: PrismaService,
    private stakeholderValidation: StakeholderValidation,
  ) {}

  async getStakeholders(organizationId: number, dto: GetStakeholderDto) {
    let stakeholders = await this.prismaService.stakeholder.findMany({
      select: {
        id: true,
        defaultId: true,
        name: true,
        type: true,
        expectation: true,
        updatedAt: true,
        deletedAt: true,
        communications: {
          select: {
            id: true,
            methodType: true,
            methodText: true,
            frequencyType: true,
            frequencyText: true,
          },
        },
        stakeholderUsers: { select: { id: true } },
      },
      where: {
        organizationId: organizationId,
        type: dto.type,
        deletedAt: {},
        OR: [
          { deletedAt: null },
          { defaultId: { not: null }, deletedAt: { not: null } },
        ],
      },
    });
    const existDefaultIds = stakeholders
      .filter((s) => s.defaultId)
      .map((s) => s.defaultId);
    stakeholders = stakeholders.filter((s) => !s.deletedAt);
    const defaults = await this.prismaService.stakeholderDefault.findMany({
      select: { id: true, name: true, type: true },
      where: { id: { notIn: existDefaultIds }, type: dto.type },
    });
    for (const s of stakeholders) {
      s['userCount'] = s.stakeholderUsers.length;
      s['isCustom'] = true;
      delete s.stakeholderUsers;
      delete s.deletedAt;
    }
    for (const d of defaults) {
      d['userCount'] = 0;
      d['isCustom'] = false;
    }
    return [...stakeholders, ...defaults];
  }

  async getStakeholderUsers(
    organizationId: number,
    dto: GetStakeholderUserDto,
  ) {
    await this.stakeholderValidation.getStakeholderUsers(organizationId, dto);
    if (!dto.isCustom) {
      const stakeholder =
        await this.prismaService.stakeholderDefault.findFirstOrThrow({
          select: {
            id: true,
            name: true,
            type: true,
          },
          where: { id: dto.stakeholderId },
        });
      stakeholder['stakeholderUsers'] = [];
      return stakeholder;
    }
    const stakeholder = await this.prismaService.stakeholder.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        type: true,
        expectation: true,
        communications: {
          select: {
            id: true,
            methodType: true,
            methodText: true,
            frequencyType: true,
            frequencyText: true,
          },
        },
        stakeholderUsers: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            title: true,
            companyName: true,
            phone: true,
            updatedAt: true,
            createdUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
        },
      },
      where: {
        id: dto.stakeholderId,
        organizationId: organizationId,
      },
    });
    for (const u of stakeholder.stakeholderUsers) {
      u['createdUserInfo'] = plainToInstance(UserDataDto, u.createdUser, {
        excludeExtraneousValues: true,
        groups: ['contact'],
      });
      delete u.createdUser;
    }
    return stakeholder;
  }

  async createStakeholder(
    user: UserDto,
    dto: CreateStakeholderDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      defaultId?: number;
      isDelete?: boolean;
    },
  ) {
    await this.stakeholderValidation.createStakeholder(user, dto, options);
    const dbService = options?.tx ?? this.prismaService;
    const stakeholder = await dbService.stakeholder.create({
      data: {
        organizationId: user.organizationId,
        createdUserId: user.id,
        defaultId: options?.defaultId,
        name: dto.name,
        type: dto.type,
        expectation: dto.expectation,
        deletedAt: options?.isDelete ? new Date() : null,
      },
    });
    return stakeholder;
  }

  async createStakeholderUsers(
    user: UserDto,
    dtos: CreateStakeholderUserDto[],
  ) {
    await this.stakeholderValidation.createStakeholderUsers(user, dtos);

    const defaults = await this.prismaService.stakeholderDefault.findMany();
    let stakeholderId = dtos[0].stakeholderId;
    await this.prismaService.$transaction(async (tx) => {
      if (!dtos[0].isCustom) {
        const defaultStakeholder = defaults.find(
          (d) => d.id == dtos[0].stakeholderId,
        );
        const stakeholder = await this.createStakeholder(
          user,
          {
            name: defaultStakeholder.name,
            type: defaultStakeholder.type,
          },
          {
            tx: tx,
            defaultId: defaultStakeholder.id,
          },
        );
        stakeholderId = stakeholder.id;
      }
      await tx.stakeholderUser.createMany({
        data: dtos.map(
          (dto) =>
            ({
              createdUserId: user.id,
              stakeholderId: stakeholderId,
              name: dto.name,
              surname: dto.surname,
              email: dto.email,
              title: dto.title,
              companyName: dto.companyName,
              phone: dto.phone,
            }) as Prisma.StakeholderUserCreateManyInput,
        ),
      });
    });
    return { stakeholderId: stakeholderId, isCustom: true };
  }

  async createStakeholderUsersFromExcel(
    user: UserDto,
    dto: CreateStakeholderUserExcelDto,
    file: Express.Multer.File,
  ) {
    const workbook = XLSX.read(file.buffer, { type: 'buffer' });
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const stakeholderUsers = XLSX.utils.sheet_to_json(worksheet);
    if (stakeholderUsers.length > 1000) {
      throw new BadRequestException('Maksimum 1000 satır desteklenmektedir.');
    }
    const stakeholderUserDtos: CreateStakeholderUserDto[] = [];
    for (const su of stakeholderUsers) {
      const suValues: string[] = Object.values(su);
      stakeholderUserDtos.push({
        stakeholderId: dto.stakeholderId,
        isCustom: dto.isCustom,
        name: suValues[0],
        surname: suValues[1],
        email: suValues[2],
        title: suValues[3],
        companyName: suValues[4],
        phone: suValues[5],
      });
    }
    return await this.createStakeholderUsers(user, stakeholderUserDtos);
  }

  async updateStakeholder(user: UserDto, dto: UpdateStakeholderDto) {
    await this.prismaService.$transaction(async (tx) => {
      let stakeholderId: number;
      if (dto.isCustom) {
        await tx.stakeholder.update({
          where: { id: dto.stakeholderId, organizationId: user.organizationId },
          data: {
            name: dto.name,
            type: dto.type,
            expectation: dto.expectation,
          },
        });
        stakeholderId = dto.stakeholderId;
      } else {
        const stakeholderDefault = await tx.stakeholderDefault.findFirstOrThrow(
          {
            where: { id: dto.stakeholderId },
          },
        );
        const stakeholder = await this.createStakeholder(
          user,
          {
            name: stakeholderDefault.name,
            type: stakeholderDefault.type,
            expectation: dto.expectation,
          },
          {
            tx: tx,
            defaultId: stakeholderDefault.id,
          },
        );
        stakeholderId = stakeholder.id;
      }
      if (dto.communications) {
        if (dto.communications.length == 0) {
          await tx.stakeholderCommunication.deleteMany({
            where: { stakeholderId: stakeholderId },
          });
        } else {
          const createCommunications = dto.communications.filter((c) => !c.id);
          const updateCommunications = dto.communications.filter((c) => c.id);
          await tx.stakeholderCommunication.deleteMany({
            where: {
              id: { notIn: updateCommunications.map((c) => c.id) },
              stakeholderId: stakeholderId,
            },
          });
          await tx.stakeholderCommunication.createMany({
            data: createCommunications.map(
              (c) =>
                ({
                  createdUserId: user.id,
                  stakeholderId: stakeholderId,
                  methodType: c.methodType,
                  methodText: c.methodText,
                  frequencyType: c.frequencyType,
                  frequencyText: c.frequencyText,
                }) as Prisma.StakeholderCommunicationCreateManyInput,
            ),
          });
          for (const c of updateCommunications) {
            await tx.stakeholderCommunication.update({
              where: { id: c.id },
              data: {
                methodType: c.methodType,
                methodText: c.methodText,
                frequencyType: c.frequencyType,
                frequencyText: c.frequencyText,
              },
            });
          }
        }
      }
    });
  }

  async updateStakeholderUser(user: UserDto, dto: UpdateStakeholderUserDto) {
    await this.prismaService.stakeholderUser.update({
      where: {
        id: dto.id,
        stakeholder: { organizationId: user.organizationId },
      },
      data: {
        name: dto.name,
        surname: dto.surname,
        email: dto.email,
        title: dto.title,
        companyName: dto.companyName,
        phone: dto.phone,
      },
    });
  }

  async deleteStakeholder(user: UserDto, dto: DeleteStakeholderDto) {
    await this.prismaService.$transaction(async (tx) => {
      if (dto.isCustom) {
        await tx.stakeholderUser.deleteMany({
          where: { stakeholderId: dto.stakeholderId },
        });
        await tx.stakeholder.deleteMany({
          where: {
            id: dto.stakeholderId,
            organizationId: user.organizationId,
          },
        });
      } else {
        const stakeholderDefault = await tx.stakeholderDefault.findFirstOrThrow(
          {
            where: { id: dto.stakeholderId },
          },
        );
        await this.createStakeholder(
          user,
          {
            name: stakeholderDefault.name,
            type: stakeholderDefault.type,
          },
          {
            tx: tx,
            defaultId: stakeholderDefault.id,
            isDelete: true,
          },
        );
      }
    });
  }

  async deleteStakeholderUser(user: UserDto, dto: DeleteStakeholderUserDto) {
    await this.prismaService.stakeholderUser.delete({
      where: {
        id: dto.stakeholderUserId,
        stakeholder: { organizationId: user.organizationId },
      },
    });
  }
}
