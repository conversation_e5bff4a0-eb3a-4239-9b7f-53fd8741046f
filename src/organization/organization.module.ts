import { Modu<PERSON> } from '@nestjs/common';
import { OrganizationController } from './controllers/organization.controller';
import { OrganizationService } from './services/organization.service';
import { CommonModule } from '../common/common.module';
import { OrganizationValidation } from './validations/organization.validation';
import { FileManagerModule } from '../file-manager/file-manager.module';
import { MailModule } from '../mail/mail.module';
import { SubsidiaryService } from './services/subsidiary.service';
import { SubsidiaryValidation } from './validations/subsidiary.validation';
import { SubsidiaryController } from './controllers/subsidiary.controller';
import { StakeholderController } from './controllers/stakeholder.controller';
import { StakeholderService } from './services/stakeholder.service';
import { StakeholderValidation } from './validations/stakeholder.validation';
import { QuickStartModule } from '../quick-start/quick-start.module';
import { ProcedureModule } from '../procedure/procedure.module';

@Module({
  imports: [
    FileManagerModule,
    MailModule,
    ProcedureModule,
    QuickStartModule,
    CommonModule,
  ],
  controllers: [
    OrganizationController,
    StakeholderController,
    SubsidiaryController,
  ],
  providers: [
    OrganizationService,
    OrganizationValidation,
    StakeholderService,
    StakeholderValidation,
    SubsidiaryService,
    SubsidiaryValidation,
  ],
  exports: [OrganizationService, StakeholderService],
})
export class OrganizationModule {}
