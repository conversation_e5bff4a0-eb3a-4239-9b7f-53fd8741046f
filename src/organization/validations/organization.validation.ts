import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { InviteUserDto } from '../dtos/organization/req/invite-user.dto';
import { DeleteOrganizationDto } from '../dtos/organization/req/delete-organization.dto';
import { Organization } from '@prisma/client';
import { DeleteOrganizationUserDto } from '../dtos/organization/req/delete-organization-user.dto';
import { UpdateOrganizationDto } from '../dtos/organization/req/update-organization.dto';
import { UpdateSettingDto } from '../dtos/organization/req/update-setting.dto';
import { OrganizationSettingEnum } from '../enums/organization-setting.enum';
import { GetOrganizationUserDto } from '../dtos/organization/req/get-organization-user.dto';
import { OrganizationService } from '../services/organization.service';
import { UserService } from '../../common/services/user.service';
import { RoleEnum } from '../../common/enums/role.enum';

@Injectable()
export class OrganizationValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private userService: UserService,
  ) {}

  async getOrganizationUsers(
    user: UserDto,
    dto: GetOrganizationUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async inviteUser(user: UserDto, dto: InviteUserDto): Promise<void> {
    const userInOrg = await this.prismaService.user.findFirst({
      where: {
        email: dto.email,
        userOrganizations: { some: { organizationId: user.organizationId } },
      },
    });
    if (userInOrg) {
      throw new BadRequestException(
        'Şirketinizde bu e-mail adresine kayıtlı kullanıcı bulunmaktadır.',
      );
    }
    const userInAnotherOrg = await this.prismaService.user.findFirst({
      where: {
        email: dto.email,
        userOrganizations: { none: { organizationId: user.organizationId } },
      },
    });
    if (userInAnotherOrg) {
      throw new BadRequestException(
        'Bu e-mail adresi farklı bir şirkete kayıtlıdır.',
      );
    }
    const pendingExist = await this.prismaService.userPending.findFirst({
      where: { email: dto.email, organizationId: user.organizationId },
    });
    if (pendingExist) {
      throw new BadRequestException(
        'Şirketiniz üzerinden bu e-mail adresine zaten davet gönderilmiştir.',
      );
    }
    const orgUserCount = await this.prismaService.userOrganization.count({
      where: {
        organizationId: user.organizationId,
        role: { key: { not: RoleEnum.EMPLOYEE } },
      },
    });
    const orgPendingCount = await this.prismaService.userPending.count({
      where: {
        organizationId: user.organizationId,
        OR: [{ roleId: null }, { role: { key: { not: RoleEnum.EMPLOYEE } } }],
      },
    });
    if (orgUserCount + orgPendingCount >= 7) {
      throw new BadRequestException(
        'Şirketinizde en fazla 7 kullanıcı olabilir.',
      );
    }
  }

  async updateOrganization(
    user: UserDto,
    dto: UpdateOrganizationDto,
  ): Promise<void> {
    if (dto.countries?.length > 0) {
      const countryIds = [...new Set(dto.countries.map((c) => c.countryId))];
      const countryCount = await this.prismaService.country.count({
        where: { id: { in: countryIds } },
      });
      if (countryCount != countryIds.length) {
        throw new BadRequestException('Geçersiz ülkeler.');
      }
      const currencyIds = [...new Set(dto.countries.map((c) => c.currencyId))];
      const currencyCount = await this.prismaService.currency.count({
        where: { id: { in: currencyIds } },
      });
      if (currencyCount != currencyIds.length) {
        throw new BadRequestException('Geçersiz para birimleri.');
      }
      for (let i = 0; i < dto.countries.length; i++) {
        for (let j = i + 1; j < dto.countries.length; j++) {
          if (
            dto.countries[i].countryId == dto.countries[j].countryId &&
            dto.countries[i].currencyId == dto.countries[j].currencyId
          ) {
            throw new BadRequestException(
              'Aynı ülke ve para birimi kombinasyonu olamaz.',
            );
          }
        }
      }
    }
    if (dto.sectorIds?.length > 0) {
      const sectorCount = await this.prismaService.sector.count({
        where: { id: { in: dto.sectorIds } },
      });
      if (sectorCount != dto.sectorIds.length) {
        throw new BadRequestException('Geçersiz sektörler.');
      }
    }
  }

  async updateSettings(user: UserDto, dto: UpdateSettingDto): Promise<void> {
    if (
      dto.key == OrganizationSettingEnum.INTERNAL_PERCENT ||
      dto.key == OrganizationSettingEnum.EXTERNAL_PERCENT
    ) {
      const value = Number(dto.value);
      if (isNaN(value)) {
        throw new BadRequestException('Geçersiz değer.');
      }
      if (value < 0 || value > 1) {
        throw new BadRequestException('Değer 0-1 arasında olmalıdır.');
      }
    }
  }

  async deleteOrganization(
    user: UserDto,
    dto: DeleteOrganizationDto,
  ): Promise<Organization[]> {
    await this.userService.checkPassword(user, dto.password);
    const otherOrganizations = await this.prismaService.organization.findMany({
      where: {
        id: { not: user.organizationId },
        userOrganizations: { some: { userId: user.id } },
      },
    });
    if (otherOrganizations.length == 0) {
      throw new BadRequestException(
        'Yalnızca bir organizasyonda bulunduğun için silme işlemi yapamazsın.',
      );
    }
    return otherOrganizations;
  }

  async deleteOrganizationUser(
    user: UserDto,
    dto: DeleteOrganizationUserDto,
  ): Promise<void> {
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: {
        organizationId: user.organizationId,
        userId: dto.userId,
      },
    });
    if (user.id == dto.userId) {
      throw new BadRequestException('Kendinizi şirketten çıkaramazsınız.');
    }
  }
}
