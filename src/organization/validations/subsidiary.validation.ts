import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { OrganizationTypeEnum } from '../enums/organization-type.enum';
import { UserDto } from '../../common/dtos/user.dto';
import { CreateSubsidiaryDto } from '../dtos/subsidiary/req/create-subsidiary.dto';
import { UpdateSubsidiaryDto } from '../dtos/subsidiary/req/update-subsidiary.dto';
import { DeleteSubsidiaryDto } from '../dtos/subsidiary/req/delete-subsidiary.dto';
import { RecoverSubsidiaryDto } from '../dtos/subsidiary/req/recover-subsidiary.dto';
import { GetStakeholderDto } from '../dtos/subsidiary/req/get-stakeholder.dto';
import { GetStakeholderUserDto } from '../dtos/subsidiary/req/get-stakeholder-user.dto';
import { GetProcedureCategoryDto } from '../dtos/subsidiary/req/get-procedure-category.dto';
import { GetProcedurePolicyDto } from '../dtos/subsidiary/req/get-procedure-policy.dto';
import { GetSubsidiaryReportDto } from '../dtos/subsidiary/req/get-subsidiary-report.dto';
import { OrganizationService } from '../services/organization.service';
import { GetSubsidiaryReportFileDto } from '../dtos/subsidiary/req/get-subsidiary-report-file.dto';
import { GetSupplierDto } from '../dtos/subsidiary/req/get-supplier.dto';
import { RoleEnum } from '../../common/enums/role.enum';

@Injectable()
export class SubsidiaryValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
  ) {}

  async getSuppliers(user: UserDto, dto: GetSupplierDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getSubsidiaryReports(
    user: UserDto,
    dto: GetSubsidiaryReportDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getSubsidiaryReportFiles(
    user: UserDto,
    dto: GetSubsidiaryReportFileDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getStakeholders(user: UserDto, dto: GetStakeholderDto): Promise<void> {
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    if (!organizationIds.includes(dto.subsidiaryId)) {
      throw new BadRequestException('Geçersiz iştirak veya tedarikçi.');
    }
  }

  async getStakeholderUsers(
    user: UserDto,
    dto: GetStakeholderUserDto,
  ): Promise<void> {
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    if (!organizationIds.includes(dto.subsidiaryId)) {
      throw new BadRequestException('Geçersiz iştirak veya tedarikçi.');
    }
  }

  async getProcedureCategories(user: UserDto, dto: GetProcedureCategoryDto) {
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    if (!organizationIds.includes(dto.subsidiaryId)) {
      throw new BadRequestException('Geçersiz iştirak veya tedarikçi.');
    }
  }

  async getProcedurePolicies(user: UserDto, dto: GetProcedurePolicyDto) {
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    if (!organizationIds.includes(dto.subsidiaryId)) {
      throw new BadRequestException('Geçersiz iştirak veya tedarikçi.');
    }
  }

  async createSubsidiary(
    user: UserDto,
    dto: CreateSubsidiaryDto,
  ): Promise<void> {
    const organization = await this.prismaService.organization.findFirstOrThrow(
      {
        where: { id: user.organizationId },
      },
    );
    if (
      ![OrganizationTypeEnum.AFFILIATE, OrganizationTypeEnum.SUPPLIER].includes(
        dto.organizationType,
      )
    ) {
      throw new BadRequestException('Geçersiz organizasyon tipi.');
    }

    if (dto.organizationType == OrganizationTypeEnum.AFFILIATE) {
      if (!dto.affiliateType) {
        throw new BadRequestException('Alt şirket türü gereklidir.');
      }
    } else {
      delete dto.affiliateType;
    }

    if (organization.organizationType == OrganizationTypeEnum.AFFILIATE) {
      throw new BadRequestException(
        'Alt şirket olduğunuz için tedarikçi oluşturamazsınız.',
      );
    } else if (organization.organizationType == OrganizationTypeEnum.SUPPLIER) {
      throw new BadRequestException('İştirak veya tedarikçi oluşturamazsınız.');
    }

    const permissionsCount = await this.prismaService.permission.count({
      where: { id: { in: dto.permissionIds } },
    });
    if (permissionsCount != dto.permissionIds.length) {
      throw new BadRequestException('Geçersiz izinler.');
    }
    const userInAnotherOrg = await this.prismaService.user.findFirst({
      where: { email: dto.email },
    });
    if (userInAnotherOrg) {
      throw new BadRequestException(
        'Bu e-mail adresi zaten bir şirkete kayıtlıdır.',
      );
    }
  }

  async inviteManager(user: UserDto, dto: UpdateSubsidiaryDto): Promise<void> {
    const pendingExist = await this.prismaService.userPending.findFirst({
      where: { email: dto.managerEmail, organizationId: dto.id },
    });
    if (pendingExist) {
      throw new BadRequestException(
        'Şirket üzerinden bu e-mail adresine zaten davet gönderilmiştir.',
      );
    }
    const userInAnotherOrg = await this.prismaService.user.findFirst({
      where: {
        email: dto.managerEmail,
        userOrganizations: { none: { organizationId: dto.id } },
      },
    });
    if (userInAnotherOrg) {
      throw new BadRequestException(
        'Bu e-mail adresi farklı bir şirkete kayıtlıdır.',
      );
    }
    const orgUserCount = await this.prismaService.userOrganization.count({
      where: {
        organizationId: dto.id,
        role: { key: { not: RoleEnum.EMPLOYEE } },
      },
    });
    const orgPendingCount = await this.prismaService.userPending.count({
      where: {
        organizationId: dto.id,
        OR: [{ roleId: null }, { role: { key: { not: RoleEnum.EMPLOYEE } } }],
      },
    });
    if (orgUserCount + orgPendingCount >= 7) {
      throw new BadRequestException('Şirkette en fazla 7 kullanıcı olabilir.');
    }
  }

  async updateSubsidiary(
    user: UserDto,
    dto: UpdateSubsidiaryDto,
  ): Promise<boolean> {
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    if (!organizationIds.includes(dto.id)) {
      throw new BadRequestException('Geçersiz iştirak veya tedarikçi.');
    }
    if (dto.managerEmail) {
      const userOrg = await this.prismaService.userOrganization.findFirst({
        where: { organizationId: dto.id, user: { email: dto.managerEmail } },
      });
      if (!userOrg) {
        await this.inviteManager(user, dto);
      }
      return !!userOrg;
    }
  }

  async recoverSubsidiary(
    user: UserDto,
    dto: RecoverSubsidiaryDto,
  ): Promise<void> {
    const organizationIds = await this.organizationService.getOrganizationIds(
      user,
      { isDeleted: true },
    );
    if (!organizationIds.includes(dto.id)) {
      throw new BadRequestException('Geçersiz iştirak veya tedarikçi.');
    }
  }

  async deleteSubsidiary(
    user: UserDto,
    dto: DeleteSubsidiaryDto,
  ): Promise<void> {
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    if (!organizationIds.includes(dto.id)) {
      throw new BadRequestException('Geçersiz iştirak veya tedarikçi.');
    }
  }
}
