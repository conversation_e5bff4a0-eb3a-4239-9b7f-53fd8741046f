import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { UpdateStakeholderDto } from '../dtos/stakeholder/req/update-stakeholder.dto';
import { CreateStakeholderUserDto } from '../dtos/stakeholder/req/create-stakeholder-user.dto';
import { UpdateStakeholderUserDto } from '../dtos/stakeholder/req/update-stakeholder-user.dto';
import { CreateStakeholderDto } from '../dtos/stakeholder/req/create-stakeholder.dto';
import { StakeholderFrequencyTypeEnum } from '../enums/stakeholder-frequency-type.enum';
import { StakeholderMethodTypeEnum } from '../enums/stakeholder-method-type.enum';
import { PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { GetStakeholderUserDto } from '../dtos/stakeholder/req/get-stakeholder-user.dto';
import { ConfigService } from '@nestjs/config';
import { CreateStakeholderUserExcelDto } from '../dtos/stakeholder/req/create-stakeholder-user-excel.dto';
import { DeleteStakeholderDto } from '../dtos/stakeholder/req/delete-stakeholder.dto';

@Injectable()
export class StakeholderValidation {
  private readonly UPLOAD_MAX_FILE_SIZE: number;

  constructor(
    private prismaService: PrismaService,
    private configService: ConfigService,
  ) {
    this.UPLOAD_MAX_FILE_SIZE = this.configService.getOrThrow<number>(
      'UPLOAD_MAX_FILE_SIZE',
    );
  }

  async getStakeholderUsers(
    organizationId: number,
    dto: GetStakeholderUserDto,
  ): Promise<void> {
    if (!dto.isCustom) {
      const editedStakeholder = await this.prismaService.stakeholder.findFirst({
        where: {
          organizationId: organizationId,
          defaultId: dto.stakeholderId,
          deletedAt: {},
        },
      });
      if (editedStakeholder) {
        throw new BadRequestException('Paydaş bulunamadı.');
      }
    }
  }

  async createStakeholder(
    user: UserDto,
    dto: CreateStakeholderDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      defaultId?: number;
      isDelete?: boolean;
    },
  ) {
    const dbService = options?.tx ?? this.prismaService;
    if (options?.isDelete && !options?.defaultId) {
      throw new BadRequestException('Geçersiz silme işlemi.');
    }
    if (options?.defaultId) {
      const defaultCount = await dbService.stakeholder.count({
        where: {
          organizationId: user.organizationId,
          defaultId: options.defaultId,
          deletedAt: {},
        },
      });
      if (defaultCount > 0) {
        throw new BadRequestException(
          'Oluşturulmak istenen varsayılan paydaşlar zaten mevcut veya silinmiş.',
        );
      }
    }
  }

  async createStakeholderUsers(
    user: UserDto,
    dtos: CreateStakeholderUserDto[],
  ) {
    const stakeholderIds = dtos.map((dto) => dto.stakeholderId);
    const isCustoms = dtos.map((dto) => dto.isCustom);
    if (new Set(stakeholderIds).size != 1) {
      throw new BadRequestException('Geçersiz paydaşlar.');
    }
    if (new Set(isCustoms).size != 1) {
      throw new BadRequestException('Geçersiz paydaş durumu.');
    }
    if (dtos[0].isCustom) {
      await this.prismaService.stakeholder.findFirstOrThrow({
        where: {
          id: dtos[0].stakeholderId,
          organizationId: user.organizationId,
        },
      });
    } else {
      await this.prismaService.stakeholderDefault.findFirstOrThrow({
        where: { id: dtos[0].stakeholderId },
      });
    }
    const emails = dtos.map((dto) => dto.email);
    const existStakeholderUsers =
      await this.prismaService.stakeholderUser.findMany({
        where: {
          email: { in: emails },
          stakeholder: {
            id: dtos[0].stakeholderId,
            organizationId: user.organizationId,
          },
        },
      });
    if (existStakeholderUsers.length > 0) {
      throw new BadRequestException(
        'Girdiğiniz e-mail adreslerine ait paydaş kullanıcısı zaten bulunmaktadır: ' +
          existStakeholderUsers.map((su) => su.email).join(', '),
      );
    }
  }

  async createStakeholderUsersFromExcel(
    user: UserDto,
    dto: CreateStakeholderUserExcelDto,
    file: Express.Multer.File,
  ): Promise<void> {
    if (
      file.mimetype !=
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      throw new BadRequestException('Geçerli dosya türü: xlsx');
    }
    if (file.size > this.UPLOAD_MAX_FILE_SIZE) {
      throw new BadRequestException(
        `Dosya boyutu en fazla ${this.UPLOAD_MAX_FILE_SIZE / 10 ** 6} MB olabilir.`,
      );
    }
  }

  async updateStakeholder(user: UserDto, dto: UpdateStakeholderDto) {
    if (dto.isCustom) {
      await this.prismaService.stakeholder.findFirstOrThrow({
        where: {
          id: dto.stakeholderId,
          organizationId: user.organizationId,
        },
      });
    } else {
      await this.prismaService.stakeholderDefault.findFirstOrThrow({
        where: {
          id: dto.stakeholderId,
          stakeholders: {
            none: { organizationId: user.organizationId, deletedAt: {} },
          },
        },
      });
    }
    if (dto.communications?.length > 0) {
      const communicationIds = dto.communications
        .filter((c) => c.id)
        .map((c) => c.id);
      const communications =
        await this.prismaService.stakeholderCommunication.findMany({
          where: {
            id: { in: communicationIds },
            stakeholderId: dto.stakeholderId,
          },
        });
      if (communications.length != communicationIds.length) {
        throw new BadRequestException('Geçersiz iletişim verileri.');
      }
      for (const comm of dto.communications) {
        if (
          comm.methodType == StakeholderMethodTypeEnum.OTHER &&
          !comm.methodText
        ) {
          throw new BadRequestException('Geçersiz iletişim yöntemi.');
        }
        if (
          comm.methodType != StakeholderMethodTypeEnum.OTHER &&
          comm.methodType
        ) {
          comm.methodText = null;
        }
        if (
          comm.frequencyType == StakeholderFrequencyTypeEnum.OTHER &&
          !comm.frequencyText
        ) {
          throw new BadRequestException('Geçersiz iletişim sıklığı.');
        }
        if (
          comm.frequencyType != StakeholderFrequencyTypeEnum.OTHER &&
          comm.frequencyType
        ) {
          comm.frequencyText = null;
        }
      }
    }
  }

  async updateStakeholderUser(user: UserDto, dto: UpdateStakeholderUserDto) {
    const stakeholderUser =
      await this.prismaService.stakeholderUser.findFirstOrThrow({
        where: {
          id: dto.id,
          stakeholder: { organizationId: user.organizationId },
        },
      });
    if (dto.email) {
      const existStakeholderUser =
        await this.prismaService.stakeholderUser.findFirst({
          where: {
            id: { not: dto.id },
            email: dto.email,
            stakeholder: {
              id: stakeholderUser.stakeholderId,
              organizationId: user.organizationId,
            },
          },
        });
      if (existStakeholderUser) {
        throw new BadRequestException(
          'Girdiğiniz e-mail adresine ait paydaş kullanıcısı zaten bulunmaktadır.',
        );
      }
    }
  }

  async deleteStakeholder(user: UserDto, dto: DeleteStakeholderDto) {
    if (dto.isCustom) {
      await this.prismaService.stakeholder.findFirstOrThrow({
        where: {
          id: dto.stakeholderId,
          organizationId: user.organizationId,
        },
      });
    } else {
      await this.prismaService.stakeholderDefault.findFirstOrThrow({
        where: {
          id: dto.stakeholderId,
          stakeholders: {
            none: { organizationId: user.organizationId, deletedAt: {} },
          },
        },
      });
    }
  }
}
