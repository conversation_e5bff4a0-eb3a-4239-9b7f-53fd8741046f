import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { SubsidiaryService } from '../services/subsidiary.service';
import { SubsidiaryValidation } from '../validations/subsidiary.validation';
import { CreateSubsidiaryDto } from '../dtos/subsidiary/req/create-subsidiary.dto';
import { GetSubsidiaryDetailDto } from '../dtos/subsidiary/req/get-subsidiary-detail.dto';
import { UpdateSubsidiaryDto } from '../dtos/subsidiary/req/update-subsidiary.dto';
import { DeleteSubsidiaryDto } from '../dtos/subsidiary/req/delete-subsidiary.dto';
import { RecoverSubsidiaryDto } from '../dtos/subsidiary/req/recover-subsidiary.dto';
import { GetSupplierDto } from '../dtos/subsidiary/req/get-supplier.dto';
import { GetAffiliateDto } from '../dtos/subsidiary/req/get-affiliate.dto';
import { GetSubsidiaryReportDto } from '../dtos/subsidiary/req/get-subsidiary-report.dto';
import { GetStakeholderDto } from '../dtos/subsidiary/req/get-stakeholder.dto';
import { GetStakeholderUserDto } from '../dtos/subsidiary/req/get-stakeholder-user.dto';
import { GetProcedureCategoryDto } from '../dtos/subsidiary/req/get-procedure-category.dto';
import { GetProcedurePolicyDto } from '../dtos/subsidiary/req/get-procedure-policy.dto';
import { GetSubsidiaryDto } from '../dtos/subsidiary/req/get-subsidiary.dto';
import { GetSubsidiaryReportFileDto } from '../dtos/subsidiary/req/get-subsidiary-report-file.dto';
import { Request } from 'express';

@ApiTags('Organizations/Subsidiaries')
@Controller('organizations/subsidiaries')
export class SubsidiaryController {
  constructor(
    private subsidiaryService: SubsidiaryService,
    private subsidiaryValidation: SubsidiaryValidation,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSubsidiaries(
    @User() user: UserDto,
    @Query() dto: GetSubsidiaryDto,
  ): Promise<GeneralResponseDto> {
    const subsidiaries = await this.subsidiaryService.getSubsidiaries(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(subsidiaries);
  }

  @Get('affiliates')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getAffiliates(
    @User() user: UserDto,
    @Query() dto: GetAffiliateDto,
  ): Promise<GeneralResponseDto> {
    const affiliates = await this.subsidiaryService.getAffiliates(user, dto);
    return new GeneralResponseDto().setData(affiliates);
  }

  @Get('suppliers')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSuppliers(
    @User() user: UserDto,
    @Query() dto: GetSupplierDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.getSuppliers(user, dto);
    const suppliers = await this.subsidiaryService.getSuppliers(user, dto);
    return new GeneralResponseDto().setData(suppliers);
  }

  @Get('detail')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSubsidiaryDetail(
    @User() user: UserDto,
    @Query() dto: GetSubsidiaryDetailDto,
  ): Promise<GeneralResponseDto> {
    const subsidiary = await this.subsidiaryService.getSubsidiaryDetail(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(subsidiary);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSubsidiaryReports(
    @User() user: UserDto,
    @Query() dto: GetSubsidiaryReportDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.getSubsidiaryReports(user, dto);
    const reports = await this.subsidiaryService.getSubsidiaryReports(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/files')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSubsidiaryReportFiles(
    @User() user: UserDto,
    @Query() dto: GetSubsidiaryReportFileDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.getSubsidiaryReportFiles(user, dto);
    const reports = await this.subsidiaryService.getSubsidiaryReportFiles(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(reports);
  }

  @Get('stakeholders')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getStakeholders(
    @User() user: UserDto,
    @Query() dto: GetStakeholderDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.getStakeholders(user, dto);
    const stakeholders = await this.subsidiaryService.getStakeholders(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(stakeholders);
  }

  @Get('stakeholders/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getStakeholderUsers(
    @User() user: UserDto,
    @Query() dto: GetStakeholderUserDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.getStakeholderUsers(user, dto);
    const stakeholders = await this.subsidiaryService.getStakeholderUsers(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(stakeholders);
  }

  @Get('procedures/categories')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getProcedureCategories(
    @User() user: UserDto,
    @Query() dto: GetProcedureCategoryDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.getProcedureCategories(user, dto);
    const stakeholders = await this.subsidiaryService.getProcedureCategories(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(stakeholders);
  }

  @Get('procedures/policies')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getProcedurePolicies(
    @User() user: UserDto,
    @Query() dto: GetProcedurePolicyDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.getProcedurePolicies(user, dto);
    const stakeholders = await this.subsidiaryService.getProcedurePolicies(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(stakeholders);
  }

  @Post()
  @ApiBearerAuth()
  @ApiBody({ type: CreateSubsidiaryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createSubsidiary(
    @User() user: UserDto,
    @Body() dto: CreateSubsidiaryDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.createSubsidiary(user, dto);
    const result = await this.subsidiaryService.createSubsidiary(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateSubsidiaryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateSubsidiary(
    @User() user: UserDto,
    @Body() dto: UpdateSubsidiaryDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    const managerEmailInOrg = await this.subsidiaryValidation.updateSubsidiary(
      user,
      dto,
    );
    const result = await this.subsidiaryService.updateSubsidiary(
      user,
      dto,
      request,
      managerEmailInOrg,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Put('recover')
  @ApiBearerAuth()
  @ApiBody({ type: RecoverSubsidiaryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async recoverSubsidiary(
    @User() user: UserDto,
    @Body() dto: RecoverSubsidiaryDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.recoverSubsidiary(user, dto);
    await this.subsidiaryService.recoverSubsidiary(user, dto);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteSubsidiaryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteSubsidiary(
    @User() user: UserDto,
    @Body() dto: DeleteSubsidiaryDto,
  ): Promise<GeneralResponseDto> {
    await this.subsidiaryValidation.deleteSubsidiary(user, dto);
    await this.subsidiaryService.deleteSubsidiary(user, dto);
    return new GeneralResponseDto();
  }
}
