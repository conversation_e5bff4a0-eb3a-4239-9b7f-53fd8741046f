import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { StakeholderService } from '../services/stakeholder.service';
import { StakeholderValidation } from '../validations/stakeholder.validation';
import { UpdateStakeholderDto } from '../dtos/stakeholder/req/update-stakeholder.dto';
import { CreateStakeholderUserDto } from '../dtos/stakeholder/req/create-stakeholder-user.dto';
import { DeleteStakeholderUserDto } from '../dtos/stakeholder/req/delete-stakeholder-user.dto';
import { GetStakeholderUserDto } from '../dtos/stakeholder/req/get-stakeholder-user.dto';
import { UpdateStakeholderUserDto } from '../dtos/stakeholder/req/update-stakeholder-user.dto';
import { GetStakeholderDto } from '../dtos/stakeholder/req/get-stakeholder.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { CreateStakeholderUserExcelDto } from '../dtos/stakeholder/req/create-stakeholder-user-excel.dto';
import { DeleteStakeholderDto } from '../dtos/stakeholder/req/delete-stakeholder.dto';
import { CreateStakeholderDto } from '../dtos/stakeholder/req/create-stakeholder.dto';

@ApiTags('Organizations/Stakeholders')
@Controller('organizations/stakeholders')
export class StakeholderController {
  constructor(
    private stakeholderValidation: StakeholderValidation,
    private stakeholderService: StakeholderService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getStakeholders(
    @User() user: UserDto,
    @Query() dto: GetStakeholderDto,
  ): Promise<GeneralResponseDto> {
    const stakeholders = await this.stakeholderService.getStakeholders(
      user.organizationId,
      dto,
    );
    return new GeneralResponseDto().setData(stakeholders);
  }

  @Get('users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getStakeholderUsers(
    @User() user: UserDto,
    @Query() dto: GetStakeholderUserDto,
  ): Promise<GeneralResponseDto> {
    const stakeholder = await this.stakeholderService.getStakeholderUsers(
      user.organizationId,
      dto,
    );
    return new GeneralResponseDto().setData(stakeholder);
  }

  @Post()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createStakeholder(
    @User() user: UserDto,
    @Body() dto: CreateStakeholderDto,
  ): Promise<GeneralResponseDto> {
    await this.stakeholderService.createStakeholder(user, dto);
    return new GeneralResponseDto();
  }

  @Post('users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createStakeholderUsers(
    @User() user: UserDto,
    @Body() dto: CreateStakeholderUserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.stakeholderService.createStakeholderUsers(user, [
      dto,
    ]);
    return new GeneralResponseDto().setData(result);
  }

  @Post('users/excel')
  @ApiBearerAuth()
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({ type: CreateStakeholderUserExcelDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createFile(
    @User() user: UserDto,
    @Body() dto: CreateStakeholderUserExcelDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    await this.stakeholderValidation.createStakeholderUsersFromExcel(
      user,
      dto,
      file,
    );
    const result =
      await this.stakeholderService.createStakeholderUsersFromExcel(
        user,
        dto,
        file,
      );
    return new GeneralResponseDto().setData(result);
  }

  @Put()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateStakeholders(
    @User() user: UserDto,
    @Body() dto: UpdateStakeholderDto,
  ): Promise<GeneralResponseDto> {
    await this.stakeholderValidation.updateStakeholder(user, dto);
    await this.stakeholderService.updateStakeholder(user, dto);
    return new GeneralResponseDto();
  }

  @Put('users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateStakeholderUser(
    @User() user: UserDto,
    @Body() dto: UpdateStakeholderUserDto,
  ): Promise<GeneralResponseDto> {
    await this.stakeholderValidation.updateStakeholderUser(user, dto);
    await this.stakeholderService.updateStakeholderUser(user, dto);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteStakeholder(
    @User() user: UserDto,
    @Body() dto: DeleteStakeholderDto,
  ): Promise<GeneralResponseDto> {
    await this.stakeholderValidation.deleteStakeholder(user, dto);
    await this.stakeholderService.deleteStakeholder(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteStakeholderUser(
    @User() user: UserDto,
    @Body() dto: DeleteStakeholderUserDto,
  ): Promise<GeneralResponseDto> {
    await this.stakeholderService.deleteStakeholderUser(user, dto);
    return new GeneralResponseDto();
  }
}
