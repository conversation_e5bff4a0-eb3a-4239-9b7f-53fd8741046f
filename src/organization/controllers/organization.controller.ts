import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { OrganizationService } from '../services/organization.service';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { UpdateOrganizationDto } from '../dtos/organization/req/update-organization.dto';
import { OrganizationValidation } from '../validations/organization.validation';
import { UpdateUserDto } from '../dtos/organization/req/update-user.dto';
import { InviteUserDto } from '../dtos/organization/req/invite-user.dto';
import { OrganizationUserResDto } from '../dtos/organization/res/organization-user.res.dto';
import { GetOrganizationUserDto } from '../dtos/organization/req/get-organization-user.dto';
import { DeleteOrganizationDto } from '../dtos/organization/req/delete-organization.dto';
import { DeleteInvitedUserDto } from '../dtos/organization/req/delete-invited-user.dto';
import { DeleteOrganizationUserDto } from '../dtos/organization/req/delete-organization-user.dto';
import { UpdateSettingDto } from '../dtos/organization/req/update-setting.dto';
import { Request } from 'express';
import { ApiOkResponseWithDto } from '../../common/decorators/swagger.decorator';

@ApiTags('Organizations')
@Controller('organizations')
export class OrganizationController {
  constructor(
    private organizationService: OrganizationService,
    private organizationValidation: OrganizationValidation,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getOrganizations(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.organizationService.getOrganizations(user);
    return new GeneralResponseDto().setData(result);
  }

  @Get('profile')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getOrganizationProfile(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.organizationService.getOrganizationProfile(user);
    return new GeneralResponseDto().setData(result);
  }

  @Get('users')
  @ApiBearerAuth()
  @ApiOkResponseWithDto(OrganizationUserResDto)
  async getOrganizationUsers(
    @User() user: UserDto,
    @Query() dto: GetOrganizationUserDto,
  ): Promise<GeneralResponseDto> {
    await this.organizationValidation.getOrganizationUsers(user, dto);
    const users = await this.organizationService.getOrganizationUsers(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(users);
  }

  @Get('countries')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getOrganizationCountries(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const countries =
      await this.organizationService.getOrganizationCountries(user);
    return new GeneralResponseDto().setData(countries);
  }

  @Get('sources')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSources(): Promise<GeneralResponseDto> {
    const sources = await this.organizationService.getSources();
    return new GeneralResponseDto().setData(sources);
  }

  @Get('sectors')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSectors(): Promise<GeneralResponseDto> {
    const sectors = await this.organizationService.getSectors();
    return new GeneralResponseDto().setData(sectors);
  }

  @Post('users/invite')
  @ApiBearerAuth()
  @ApiBody({ type: InviteUserDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async inviteUser(
    @User() user: UserDto,
    @Body() dto: InviteUserDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    await this.organizationValidation.inviteUser(user, dto);
    const result = await this.organizationService.inviteUser(
      user,
      dto,
      request,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateOrganizationDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateOrganization(
    @User() user: UserDto,
    @Body() dto: UpdateOrganizationDto,
  ): Promise<GeneralResponseDto> {
    await this.organizationValidation.updateOrganization(user, dto);
    await this.organizationService.updateOrganization(user, dto);
    return new GeneralResponseDto();
  }

  @Put('/users')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateUserDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateUser(
    @User() user: UserDto,
    @Body() dto: UpdateUserDto,
  ): Promise<GeneralResponseDto> {
    await this.organizationService.updateUser(user, dto);
    return new GeneralResponseDto();
  }

  @Put('/settings')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateSettingDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateSettings(
    @User() user: UserDto,
    @Body() dto: UpdateSettingDto,
  ): Promise<GeneralResponseDto> {
    await this.organizationValidation.updateSettings(user, dto);
    await this.organizationService.updateSettings(user, dto);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteOrganizationDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteOrganization(
    @User() user: UserDto,
    @Body() dto: DeleteOrganizationDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    const otherOrganizations =
      await this.organizationValidation.deleteOrganization(user, dto);
    const result = await this.organizationService.deleteOrganization(
      user,
      dto,
      request,
      otherOrganizations,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Delete('users/invite')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteInvitedUserDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteInvitedUser(
    @User() user: UserDto,
    @Body() dto: DeleteInvitedUserDto,
  ): Promise<GeneralResponseDto> {
    await this.organizationService.deleteInvitedUser(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('users')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteOrganizationUserDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteOrganizationUser(
    @User() user: UserDto,
    @Body() dto: DeleteOrganizationUserDto,
  ): Promise<GeneralResponseDto> {
    await this.organizationValidation.deleteOrganizationUser(user, dto);
    const firedUser = await this.organizationService.deleteOrganizationUser(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(firedUser);
  }
}
