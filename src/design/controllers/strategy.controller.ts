import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { Api<PERSON><PERSON>erA<PERSON>, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { StrategyValidation } from '../validations/strategy.validation';
import { StrategyService } from '../services/strategy.service';
import { GetStrategyDto } from '../dtos/strategy/get-strategy.dto';
import { DeleteStrategyReportDto } from '../dtos/strategy/delete-strategy-report.dto';
import { UpdateStrategyReportDto } from '../dtos/strategy/update-strategy-report.dto';
import { CreateStrategyHelpDto } from '../dtos/strategy/create-strategy-help.dto';
import { CreateStrategyHelpAnswerDto } from '../dtos/strategy/create-strategy-help-answer.dto';
import { GetStrategyReportDto } from '../dtos/strategy/get-strategy-report.dto';
import { GetStrategyReportUserDto } from '../dtos/strategy/get-strategy-report-user.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { GetStrategyHelpDetailDto } from '../dtos/strategy/get-strategy-help-detail.dto';
import { GetCurrentStrategyDto } from '../dtos/strategy/get-current-strategy.dto';
import { CreateStrategyReportDto } from '../dtos/strategy/create-strategy-report.dto';
import { DeleteStrategyHelpDto } from '../dtos/strategy/delete-strategy-help.dto';
import { UpdateStrategyTermDto } from '../dtos/strategy/update-strategy-term.dto';
import { UpdateStrategyDto } from '../dtos/strategy/update-strategy.dto';
import { UpdateStrategyDetailCombineDto } from '../dtos/strategy/update-strategy-detail-combine.dto';
import { UpdateStrategyDetailSeparateDto } from '../dtos/strategy/update-strategy-detail-separate.dto';

@ApiTags('Designs/Strategies')
@Controller('designs/strategies')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class StrategyController {
  constructor(
    private strategyValidation: StrategyValidation,
    private strategyService: StrategyService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getStrategies(
    @User() user: UserDto,
    @Query() dto: GetStrategyDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.strategyService.getStrategies(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentStrategies(
    @User() user: UserDto,
    @Query() dto: GetCurrentStrategyDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.getCurrentStrategies(user, dto);
    const result = await this.strategyService.getCurrentStrategies(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetStrategyReportDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.getReports(user, dto);
    const reports = await this.strategyService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetStrategyReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.getReportUsers(user, dto);
    const users = await this.strategyService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps/detail')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getHelpDetail(
    @User() user: UserDto,
    @Query() dto: GetStrategyHelpDetailDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.getHelpDetail(user, dto);
    const result = await this.strategyService.getHelpDetail(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(
    @User() user: UserDto,
    @Body() dto: CreateStrategyReportDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.strategyService.createReport(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.strategyService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelp(
    @User() user: UserDto,
    @Body() dto: CreateStrategyHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.createHelp(user, dto);
    await this.strategyService.createHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Post('helps/answers')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createHelpAnswer(
    @User() user: UserDto,
    @Body() dto: CreateStrategyHelpAnswerDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.createHelpAnswer(user, dto);
    await this.strategyService.createHelpAnswer(user, dto);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateStrategy(
    @User() user: UserDto,
    @Body() dto: UpdateStrategyDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.updateStrategy(user, dto);
    await this.strategyService.updateStrategy(user, dto, {
      removeHelps: true,
    });
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateStrategyReportDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.updateReport(user, dto);
    await this.strategyService.updateReport(user, dto);
    return new GeneralResponseDto();
  }

  @Put('details/combines')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateDetailsCombine(
    @User() user: UserDto,
    @Body() dto: UpdateStrategyDetailCombineDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.updateDetailsCombine(user, dto);
    await this.strategyService.updateDetailsCombine(user, dto);
    return new GeneralResponseDto();
  }

  @Put('details/separates')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateDetailsSeparates(
    @User() user: UserDto,
    @Body() dto: UpdateStrategyDetailSeparateDto,
  ): Promise<GeneralResponseDto> {
    const strategy = await this.strategyValidation.updateDetailsSeparate(
      user,
      dto,
    );
    await this.strategyService.updateDetailsSeparate(user, dto, strategy);
    return new GeneralResponseDto();
  }

  @Put('terms')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateTerms(
    @User() user: UserDto,
    @Body() dto: UpdateStrategyTermDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.updateTerms(user, dto);
    await this.strategyService.updateTerms(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteStrategyReportDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.deleteReport(user, dto);
    await this.strategyService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteHelp(
    @User() user: UserDto,
    @Body() dto: DeleteStrategyHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.strategyValidation.deleteHelp(user, dto);
    await this.strategyService.deleteHelp(user, dto);
    return new GeneralResponseDto();
  }
}
