import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Public, User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { GovernanceValidation } from '../validations/governance.validation';
import { GovernanceService } from '../services/governance.service';
import { GetGovernanceDto } from '../dtos/governance/get-governance.dto';
import { DeleteGovernanceReportDto } from '../dtos/governance/delete-governance-report.dto';
import { GetGovernanceReportDto } from '../dtos/governance/get-governance-report.dto';
import { GetGovernanceReportUserDto } from '../dtos/governance/get-governance-report-user.dto';
import { OrganizationTypes } from '../../common/decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { GetCurrentGovernanceDto } from '../dtos/governance/get-current-governance.dto';
import { CreateGovernanceReportDto } from '../dtos/governance/create-governance-report.dto';
import { UpdateGovernanceDto } from '../dtos/governance/update-governance.dto';
import { CreateGovernanceDto } from '../dtos/governance/create-governance.dto';
import { CreateGovernanceMemberDto } from '../dtos/governance/create-governance-member.dto';
import { CreateGovernanceMeetingDto } from '../dtos/governance/create-governance-meeting.dto';
import { GetGovernanceMemberDto } from '../dtos/governance/get-governance-member.dto';
import { GetGovernanceMeetingDto } from '../dtos/governance/get-governance-meeting.dto';
import { GetGovernancePriorityIssueDto } from '../dtos/governance/get-governance-priority-issue.dto';
import { UpdateGovernanceMemberDto } from '../dtos/governance/update-governance-member.dto';
import { DeleteGovernanceMemberDto } from '../dtos/governance/delete-governance-member.dto';
import { DeleteGovernanceMeetingDto } from '../dtos/governance/delete-governance-meeting.dto';
import { UpdateGovernanceMeetingDto } from '../dtos/governance/update-governance-meeting.dto';
import { GetGovernanceMemberProfileDto } from '../dtos/governance/get-governance-member-profile.dto';
import { CreateGovernanceMemberProfileDto } from '../dtos/governance/create-governance-member-profile.dto';
import { DeleteGovernanceDto } from '../dtos/governance/delete-governance.dto';
import { CreateGovernanceMemberProfileCodeDto } from '../dtos/governance/create-governance-member-profile-code.dto';

@ApiTags('Designs/Governances')
@Controller('designs/governances')
@OrganizationTypes(OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE)
export class GovernanceController {
  constructor(
    private governanceValidation: GovernanceValidation,
    private governanceService: GovernanceService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getGovernances(
    @User() user: UserDto,
    @Query() dto: GetGovernanceDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.governanceService.getGovernances(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentGovernances(
    @User() user: UserDto,
    @Query() dto: GetCurrentGovernanceDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.getCurrentGovernances(user, dto);
    const result = await this.governanceService.getCurrentGovernances(
      user,
      dto,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetGovernanceReportDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.getReports(user, dto);
    const reports = await this.governanceService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetGovernanceReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.getReportUsers(user, dto);
    const users = await this.governanceService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('members')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getMembers(
    @User() user: UserDto,
    @Query() dto: GetGovernanceMemberDto,
  ): Promise<GeneralResponseDto> {
    const members = await this.governanceService.getMembers(user, dto);
    return new GeneralResponseDto().setData(members);
  }

  @Get('members/profile')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getMemberProfile(
    @Query() dto: GetGovernanceMemberProfileDto,
  ): Promise<GeneralResponseDto> {
    const profile = await this.governanceService.getMemberProfile(dto);
    return new GeneralResponseDto().setData(profile);
  }

  @Get('meetings')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getMeetings(
    @User() user: UserDto,
    @Query() dto: GetGovernanceMeetingDto,
  ): Promise<GeneralResponseDto> {
    const meetings = await this.governanceService.getMeetings(user, dto);
    return new GeneralResponseDto().setData(meetings);
  }

  @Get('priority-issues')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getPriorityIssues(
    @User() user: UserDto,
    @Query() dto: GetGovernancePriorityIssueDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.governanceService.getPriorityIssues(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Post()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createGovernance(
    @User() user: UserDto,
    @Body() dto: CreateGovernanceDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.createGovernance(user, dto);
    const governance = await this.governanceService.createGovernance(user, dto);
    return new GeneralResponseDto().setData(governance);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(
    @User() user: UserDto,
    @Body() dto: CreateGovernanceReportDto,
  ): Promise<GeneralResponseDto> {
    const strategyReport = await this.governanceValidation.createReport(
      user,
      dto,
    );
    const result = await this.governanceService.createReport(
      user,
      dto,
      strategyReport,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.governanceService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('members')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createMember(
    @User() user: UserDto,
    @Body() dto: CreateGovernanceMemberDto,
  ): Promise<GeneralResponseDto> {
    const memberProfile = await this.governanceValidation.createMember(
      user,
      dto,
    );
    const result = await this.governanceService.createMember(
      user,
      dto,
      memberProfile,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('members/profile')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createMemberProfile(
    @Body() dto: CreateGovernanceMemberProfileDto,
  ): Promise<GeneralResponseDto> {
    const memberProfile =
      await this.governanceValidation.createMemberProfile(dto);
    await this.governanceService.createMemberProfile(dto, memberProfile);
    return new GeneralResponseDto();
  }

  @Post('members/profile/code')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createMemberProfileCode(
    @Body() dto: CreateGovernanceMemberProfileCodeDto,
  ): Promise<GeneralResponseDto> {
    const memberProfile =
      await this.governanceValidation.createMemberProfileCode(dto);
    await this.governanceService.createMemberProfileCode(dto, memberProfile);
    return new GeneralResponseDto();
  }

  @Post('meetings')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createMeeting(
    @User() user: UserDto,
    @Body() dto: CreateGovernanceMeetingDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.createMeeting(user, dto);
    await this.governanceService.createMeeting(user, dto);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateGovernance(
    @User() user: UserDto,
    @Body() dto: UpdateGovernanceDto,
  ): Promise<GeneralResponseDto> {
    const governance = await this.governanceValidation.updateGovernance(
      user,
      dto,
    );
    const updatedGovernance = await this.governanceService.updateGovernance(
      user,
      dto,
      governance,
    );
    return new GeneralResponseDto().setData(updatedGovernance);
  }

  @Put('members')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateMember(
    @User() user: UserDto,
    @Body() dto: UpdateGovernanceMemberDto,
  ): Promise<GeneralResponseDto> {
    const governance = await this.governanceValidation.updateMember(user, dto);
    await this.governanceService.updateMember(user, dto, governance);
    return new GeneralResponseDto();
  }

  @Put('meetings')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateMeeting(
    @User() user: UserDto,
    @Body() dto: UpdateGovernanceMeetingDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.updateMeeting(user, dto);
    await this.governanceService.updateMeeting(user, dto);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteGovernance(
    @User() user: UserDto,
    @Body() dto: DeleteGovernanceDto,
  ): Promise<GeneralResponseDto> {
    const governance = await this.governanceValidation.deleteGovernance(
      user,
      dto,
    );
    await this.governanceService.deleteGovernance(user, dto, governance);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteGovernanceReportDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.deleteReport(user, dto);
    await this.governanceService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('members')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteMember(
    @User() user: UserDto,
    @Body() dto: DeleteGovernanceMemberDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.deleteMember(user, dto);
    await this.governanceService.deleteMember(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('meetings')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteMeeting(
    @User() user: UserDto,
    @Body() dto: DeleteGovernanceMeetingDto,
  ): Promise<GeneralResponseDto> {
    await this.governanceValidation.deleteMeeting(user, dto);
    await this.governanceService.deleteMeeting(user, dto);
    return new GeneralResponseDto();
  }
}
