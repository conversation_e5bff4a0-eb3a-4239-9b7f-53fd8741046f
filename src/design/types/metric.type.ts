export interface ChartDataItem {
  name: string;
  year: number;
  value: number;
  color?: string;
}

export interface GroupedChart {
  chartUniqueId: string;
  title: string;
  xAxis: string | number;
  yAxis: string | number;
  xAxisLabel: string;
  yAxisLabel: string;
  graphType: number;
  metricIds: number[];
  filterType: number;
  filterKey: number;
  filterOptions: { id: number; name: string }[];
}
