import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetMetricDto } from '../dtos/metric/get-metric.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { DeleteMetricReportDto } from '../dtos/metric/delete-metric-report.dto';
import {
  ChartTemplate,
  GovernanceReport,
  MetricReport,
  MetricSupervisorProfile,
  Prisma,
  PrismaClient,
  User,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateMetricReportDto } from '../dtos/metric/update-metric-report.dto';
import { CreateMetricHelpDto } from '../dtos/metric/create-metric-help.dto';
import { GetMetricReportDto } from '../dtos/metric/get-metric-report.dto';
import { GetMetricReportUserDto } from '../dtos/metric/get-metric-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetMetricHelpDetailDto } from '../dtos/metric/get-metric-help-detail.dto';
import { GetCurrentMetricDto } from '../dtos/metric/get-current-metric.dto';
import { ActivityService } from '../../activity/activity.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateMetricReportDto } from '../dtos/metric/create-metric-report.dto';
import { UpdateMetricDto } from '../dtos/metric/update-metric.dto';
import { DeleteMetricHelpDto } from '../dtos/metric/delete-metric-help.dto';
import { format } from 'date-fns';
import { UtilService } from '../../common/services/util.service';
import { CreateMetricDto } from '../dtos/metric/create-metric.dto';
import { DeleteMetricDto } from '../dtos/metric/delete-metric.dto';
import { GetDefaultMetricDto } from '../dtos/metric/get-default-metric.dto';
import { CreateMetricSupervisorDto } from '../dtos/metric/create-metric-supervisor.dto';
import * as bcrypt from 'bcrypt';
import { RoleEnum } from '../../common/enums/role.enum';
import { PermissionEnum } from '../../common/enums/permission.enum';
import { MailService } from '../../mail/mail.service';
import { Request } from 'express';
import { GetMetricSupervisorDetailDto } from '../dtos/metric/get-metric-supervisor-detail.dto';
import { MetricActivityTypeEnum } from '../enums/metric-activity-type.enum';
import { GetMetricActivityDto } from '../dtos/metric/get-metric-activity.dto';
import { UpdateMetricSupervisorDto } from '../dtos/metric/update-metric-supervisor.dto';
import { MetricPerformanceTypeEnum } from '../enums/metric-performance-type.enum';
import { ApiClientService } from '../../common/services/api-client.service';
import {
  ChartFilterKeyEnum,
  ChartModelTypeEnum,
  ChartXAxisEnum,
  ChartYAxisEnum,
  MetricChartColors,
} from '../../admin/chart/enums/chart.enum';
import { GetMetricChartDto } from '../dtos/metric/get-metric-chart.dto';
import { GetMetricChartDetailDto } from '../dtos/metric/get-metric-chart-detail.dto';
import { ChartDataItem } from '../types/metric.type';
import { MetricValidation } from '../validations/metric.validation';

@Injectable()
export class MetricService {
  private readonly logger = new Logger(MetricService.name);

  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private organizationService: OrganizationService,
    private mailService: MailService,
    private organizationUtilService: OrganizationUtilService,
    private utilService: UtilService,
    private apiClientService: ApiClientService,
    private metricValidation: MetricValidation,
  ) {}

  async getMetrics(
    user: UserDto,
    dto: GetMetricDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<{ report: any; metrics: any[] }> {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const hasMetricPermission = user.permissions.some(
      (p) => p.key == PermissionEnum.METRIC_SUPERVISOR,
    );
    const report = await dbService.metricReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        code: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
        governanceReport: {
          select: { id: true, name: true, strategyReportId: true },
        },
      },
      where: {
        id: dto.reportId,
        organizationId: { in: organizationIds },
        metrics: hasMetricPermission
          ? {
              some: { supervisors: { some: { profile: { userId: user.id } } } },
            }
          : undefined,
      },
    });
    const metrics = await dbService.metric.findMany({
      select: {
        id: true,
        defaultId: true,
        facilityIds: true,
        metricName: true,
        goalName: true,
        categoryType: true,
        unitType: true,
        periodType: true,
        note: true,
        isActivity: true,
        createdAt: true,
        updatedAt: true,
        strategyDetail: {
          select: {
            id: true,
            priorityIssue: true,
            subPriorityIssues: true,
            minorPriorityIssues: true,
            strategy: { select: { id: true, strategyFocusArea: true } },
          },
        },
        performances: {
          select: {
            id: true,
            year: true,
            months: true,
            value: true,
            performanceType: true,
            isBased: true,
            description: true,
            fileIds: true,
          },
        },
        helps: {
          take: 1,
          select: {
            id: true,
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        supervisors: {
          take: 1,
          select: {
            profile: {
              select: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    surname: true,
                    email: true,
                    image: true,
                    userOrganizations: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        activities: {
          take: 1,
          select: {
            activityType: true,
            relatedId: true,
            payload: true,
            createdAt: true,
            createdUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
        supervisors: hasMetricPermission
          ? { some: { profile: { userId: user.id } } }
          : undefined,
      },
    });

    const defaultIds = metrics
      .filter((m) => m.defaultId)
      .map((m) => m.defaultId);
    const metricDefaults = await dbService.metricDefault.findMany({
      where: { id: { in: defaultIds } },
      select: {
        id: true,
        metricName: true,
        categoryType: true,
        unitType: true,
        note: true,
      },
    });

    const defaultsMap = new Map(metricDefaults.map((md) => [md.id, md]));
    const strategyDetails = await dbService.strategyDetail.findMany({
      where: {
        strategy: { reportId: report.governanceReport.strategyReportId },
      },
      include: { strategy: true },
    });
    const facilities = await this.apiClientService.getFacilities(user);
    const groupMetrics: Record<string | number, any> = {};
    const ACTIVITY_GROUP_KEY = 'activity';
    const ACTIVITY_ISSUE_KEY = 'activityIssue';
    for (const strategyDetail of strategyDetails) {
      if (!groupMetrics[strategyDetail.strategy.id]) {
        groupMetrics[strategyDetail.strategy.id] = {
          strategyFocusArea: strategyDetail.strategy.strategyFocusArea,
          issues: {
            [strategyDetail.id]: {
              strategyDetailId: strategyDetail.id,
              priorityIssue: strategyDetail.priorityIssue,
              subPriorityIssues: strategyDetail.subPriorityIssues,
              minorPriorityIssues: strategyDetail.minorPriorityIssues,
              metrics: [],
            },
          },
        };
      } else if (
        !groupMetrics[strategyDetail.strategy.id].issues[strategyDetail.id]
      ) {
        groupMetrics[strategyDetail.strategy.id].issues[strategyDetail.id] = {
          strategyDetailId: strategyDetail.id,
          priorityIssue: strategyDetail.priorityIssue,
          subPriorityIssues: strategyDetail.subPriorityIssues,
          minorPriorityIssues: strategyDetail.minorPriorityIssues,
          metrics: [],
        };
      }
    }

    const fileIds = metrics
      .flatMap((m) => m.performances)
      .filter((p) => p.fileIds)
      .flatMap((p) => this.utilService.jsonToNumberList(p.fileIds));
    const files = await this.prismaService.fileManager.findMany({
      select: {
        id: true,
        name: true,
        url: true,
        type: true,
        size: true,
        createdAt: true,
      },
      where: {
        id: { in: fileIds },
        organizationId: user.organizationId,
      },
    });

    for (const m of metrics) {
      if (m.defaultId && defaultsMap.has(m.defaultId)) {
        const defaultMetric = defaultsMap.get(m.defaultId);
        m.metricName = defaultMetric.metricName;
        m.categoryType = defaultMetric.categoryType;
        m.unitType = defaultMetric.unitType;
        m.note = defaultMetric.note;
      }

      for (const p of m.performances) {
        const performanceFileIds = this.utilService.jsonToNumberList(p.fileIds);
        p['files'] = files.filter((f) => performanceFileIds.includes(f.id));
        delete p.fileIds;
      }

      m['facilities'] = facilities
        .filter((f) =>
          this.utilService.jsonToNumberList(m.facilityIds).includes(f.id),
        )
        .map((f) => ({ id: f.id, name: f.name }));

      m['responsibleUserInfo'] = null;
      if (m.helps.length > 0) {
        m['responsibleUserInfo'] = plainToInstance(
          UserDataDto,
          m.helps[0].helpedUser,
          {
            excludeExtraneousValues: true,
          },
        );
        m['responsibleUserInfo']['isMe'] =
          m['responsibleUserInfo'].id == user.id;
      }

      m['supervisorUserInfo'] = null;
      if (m.supervisors.length > 0) {
        m['supervisorUserInfo'] = plainToInstance(
          UserDataDto,
          m.supervisors[0].profile.user,
          { excludeExtraneousValues: true },
        );
        m['supervisorUserInfo']['isMe'] = m['supervisorUserInfo'].id == user.id;
      }

      m['activity'] = null;
      if (m.activities.length > 0) {
        m['activity'] = m.activities[0];
        m['activity']['createdUserInfo'] = plainToInstance(
          UserDataDto,
          m['activity'].createdUser,
          { excludeExtraneousValues: true },
        );
        delete m['activity'].createdUser;
      }

      if (m.strategyDetail) {
        groupMetrics[m.strategyDetail.strategy.id].issues[
          m.strategyDetail.id
        ].metrics.push(m);
      } else if (m.isActivity) {
        if (!groupMetrics[ACTIVITY_GROUP_KEY]) {
          groupMetrics[ACTIVITY_GROUP_KEY] = {
            strategyFocusArea: 'Faaliyet Metrikleri',
            issues: {
              [ACTIVITY_ISSUE_KEY]: {
                strategyDetailId: null,
                priorityIssue: 'Faaliyet Metrikleri',
                subPriorityIssues: [],
                minorPriorityIssues: [],
                metrics: [],
              },
            },
          };
        }
        groupMetrics[ACTIVITY_GROUP_KEY].issues[
          ACTIVITY_ISSUE_KEY
        ].metrics.push(m);
      }

      delete m.facilityIds;
      delete m.strategyDetail;
      delete m.helps;
      delete m.supervisors;
      delete m.activities;
    }
    const metricsList = Object.values(groupMetrics);
    for (const m of metricsList) {
      m['issues'] = Object.values(m.issues);
    }

    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    delete report.createdUser;
    return { report: report, metrics: metricsList };
  }

  async getChartsTemplates() {
    const chartTemplates = await this.prismaService.chartTemplate.findMany({
      where: { modelType: ChartModelTypeEnum.METRIC },
    });
    return chartTemplates;
  }

  async getCharts(
    user: UserDto,
    dto: GetMetricChartDto,
  ): Promise<{ report: any; charts: any[] }> {
    const charts = await this.prismaService.chart.findMany({
      where: { template: { modelType: ChartModelTypeEnum.METRIC } },
      include: { template: true },
    });
    const { report, metrics } = await this.getMetrics(user, {
      reportId: dto.reportId,
    });

    const metricDefaultList = metrics
      .flatMap((m) => m.issues)
      .flatMap((i) => i.metrics)
      .filter((m) => m.defaultId)
      .map((m) => ({
        id: m.id,
        defaultId: m.defaultId,
        metricName: m.metricName,
        performances: m.performances,
        unitType: m.unitType,
        facilities: m.facilities,
      }));

    const templateMap = new Map<number, any>();
    for (const chart of charts) {
      const template = chart.template;
      if (!templateMap.has(template.id)) {
        templateMap.set(template.id, {
          templateId: template.id,
          title: template.title,
          xAxis: template.xAxis,
          yAxis: template.yAxis,
          xAxisLabel: template.xAxisLabel,
          yAxisLabel: template.yAxisLabel,
          graphType: template.graphType,
          filterType: template.filterType,
          filterKey: template.filterKey,
          metrics: [],
        });
      }

      const metrics = metricDefaultList
        .filter((m) => m.defaultId === chart.defaultId)
        .map((m) => {
          let filterOptions: { id: number; name: string }[] = [];
          if (template.filterKey === ChartFilterKeyEnum.FACILITY) {
            filterOptions = [
              {
                id: m.id,
                name: m.facilities.map((f) => f.name).join(', '),
              },
            ];
          } else if (template.filterKey === ChartFilterKeyEnum.YEAR) {
            filterOptions = m.performances.map((p) => ({
              id: p.year,
              name: p.year.toString(),
            }));
          }
          return {
            ids: [m.id],
            chartId: chart.id,
            defaultId: m.defaultId,
            metricName: m.metricName,
            unitType: m.unitType,
            facilities: m.facilities,
            filterOptions: filterOptions,
          };
        });

      const metricsMap = new Map<number, any>();
      for (const m of metrics) {
        if (!metricsMap.has(m.defaultId)) {
          m.filterOptions = m.filterOptions
            .filter((fo) => fo.name)
            .sort((a, b) => a.id - b.id);
          metricsMap.set(m.defaultId, m);
          continue;
        }
        const selectedMetric = metricsMap.get(m.defaultId);
        selectedMetric.ids.push(...m.ids);

        selectedMetric.facilities.push(...m.facilities);
        selectedMetric.facilities = [
          ...new Map(
            selectedMetric.facilities.map((item) => [item.id, item]),
          ).values(),
        ];

        if (template.filterKey === ChartFilterKeyEnum.FACILITY) {
          const selectedFo = selectedMetric.filterOptions[0];
          const mFo = m.filterOptions[0];
          selectedMetric.filterOptions = [
            { id: selectedFo.id, name: selectedFo.name },
            { id: mFo.id, name: mFo.name },
          ].filter((fo) => fo.name);
        } else if (template.filterKey === ChartFilterKeyEnum.YEAR) {
          selectedMetric.filterOptions.push(...m.filterOptions);
          selectedMetric.filterOptions = [
            // unique years
            ...new Map(
              selectedMetric.filterOptions.map((fo) => [fo.id, fo]),
            ).values(),
          ].sort((a, b) => a['id'] - b['id']);
        }
      }
      const metricsArr = Array.from(metricsMap.values()).map((m) => ({
        ...m,
        ids: m.ids.join(','),
      }));

      const chartMetrics = templateMap.get(template.id).metrics;
      const uniqueMetrics = metricsArr.filter(
        (m) =>
          !chartMetrics.some(
            (cm) =>
              m.ids.includes(cm.id) ||
              (cm.defaultId && m.defaultId && cm.defaultId == m.defaultId),
          ),
      );
      chartMetrics.push(...uniqueMetrics);
    }

    return {
      report: {
        ...report,
        createdUserInfo: {
          ...report.createdUserInfo,
          isMe: report.createdUserInfo?.id == user?.id,
        },
      },
      charts: Array.from(templateMap.values()),
    };
  }

  private transformPerformanceData(
    metric: Prisma.MetricGetPayload<{ include: { performances: true } }>,
    performanceType: MetricPerformanceTypeEnum,
    options?: { color?: string; facilityNames?: string },
  ): ChartDataItem[] {
    const performances = metric.performances.filter(
      (p) => p.performanceType === performanceType,
    );
    const sumPerformances = new Map<number, { year: number; value: number }>();
    for (const p of performances) {
      const value = Number(p.value) || 0;
      if (!sumPerformances.has(p.year)) {
        sumPerformances.set(p.year, { year: p.year, value: value });
        continue;
      }
      sumPerformances.get(p.year).value += value;
    }

    return Array.from(sumPerformances.values()).map(
      (p) =>
        ({
          name: options?.facilityNames
            ? `${options.facilityNames} (${p.year})`
            : p.year.toString(),
          year: p.year,
          value: p.value,
          color: options?.color,
        }) as ChartDataItem,
    );
  }

  async getChartsDetail(
    user: UserDto,
    dto: GetMetricChartDetailDto,
    template: ChartTemplate,
    metrics: Prisma.MetricGetPayload<{ include: { performances: true } }>[],
  ) {
    const result: {
      metricId: number;
      facilityIds: string;
      facilityNames: string;
      values: ChartDataItem[];
    }[] = [];
    const facilities = await this.apiClientService.getFacilities(user);

    for (const metric of metrics) {
      const metricFacilityIds = this.utilService.jsonToNumberList(
        metric.facilityIds,
      );
      const metricFacilityList = facilities.filter((f) =>
        metricFacilityIds.includes(f.id),
      );
      const facilityIds = metricFacilityList.map((f) => f.id).join(',');
      const facilityNames = metricFacilityList.map((f) => f.name).join(', ');

      let chartData: ChartDataItem[] = [];
      if (template.yAxis === ChartYAxisEnum.METRIC_YEAR_VALUE) {
        if (template.xAxis == ChartXAxisEnum.METRIC_PERFORMANCE) {
          chartData = this.transformPerformanceData(
            metric,
            MetricPerformanceTypeEnum.METRIC,
          );
        } else if (
          template.xAxis == ChartXAxisEnum.METRIC_PERFORMANCE_AND_TARGETS
        ) {
          chartData = [
            ...this.transformPerformanceData(
              metric,
              MetricPerformanceTypeEnum.METRIC,
            ),
            ...this.transformPerformanceData(
              metric,
              MetricPerformanceTypeEnum.SHORT_GOAL,
              { color: MetricChartColors.SHORT_GOAL },
            ),
            ...this.transformPerformanceData(
              metric,
              MetricPerformanceTypeEnum.MEDIUM_GOAL,
              { color: MetricChartColors.MEDIUM_GOAL },
            ),
            ...this.transformPerformanceData(
              metric,
              MetricPerformanceTypeEnum.LONG_GOAL,
              { color: MetricChartColors.LONG_GOAL },
            ),
          ];
        } else if (template.xAxis == ChartXAxisEnum.METRIC_FACILITIES) {
          chartData = this.transformPerformanceData(
            metric,
            MetricPerformanceTypeEnum.METRIC,
            { facilityNames: facilityNames },
          );
        }
      }

      result.push({
        metricId: metric.id,
        facilityIds: facilityIds,
        facilityNames: facilityNames,
        values: chartData,
      });
    }

    return result;
  }

  async getCurrentMetrics(
    user: UserDto,
    dto: GetCurrentMetricDto,
  ): Promise<{ report: any; metrics: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;

    const hasMetricPermission = user.permissions.some(
      (p) => p.key == PermissionEnum.METRIC_SUPERVISOR,
    );
    let metricReport: MetricReport;
    if (dto.reportId) {
      metricReport = await this.prismaService.metricReport.findFirstOrThrow({
        where: {
          id: dto.reportId,
          organizationId: dto.subsidiaryId,
          metrics: {
            some: {
              performances: { some: {} },
              supervisors: hasMetricPermission
                ? { some: { profile: { userId: user.id } } }
                : undefined,
            },
          },
        },
      });
    } else {
      metricReport = await this.getLastReport(dto.subsidiaryId, {
        hasPerformance: true,
      });
      if (!metricReport) {
        return { report: null, metrics: [] };
      }
    }
    const { report, metrics } = await this.getMetrics(user, {
      reportId: metricReport.id,
    });
    return {
      report: report,
      metrics: metrics,
    };
  }

  async getDefaultMetrics(
    user: UserDto,
    dto: GetDefaultMetricDto,
    governanceReport: GovernanceReport,
  ) {
    if (dto.strategyDetailId) {
      const strategyDetail =
        await this.prismaService.strategyDetail.findFirstOrThrow({
          where: {
            id: dto.strategyDetailId,
            strategy: { reportId: governanceReport.strategyReportId },
          },
        });

      const impactIds = this.utilService.jsonToNumberList(
        strategyDetail.impactIds,
      );
      const impacts = await this.prismaService.impact.findMany({
        where: {
          id: { in: impactIds },
        },
        include: {
          industry: {
            include: {
              details: {
                where: {
                  isSelected: true,
                  metricDefaultIds: { not: null },
                },
              },
            },
          },
        },
      });

      const financialIds = this.utilService.jsonToNumberList(
        strategyDetail.financialIds,
      );
      const financials = await this.prismaService.financial.findMany({
        where: {
          id: { in: financialIds },
        },
        include: {
          industry: {
            include: {
              details: {
                where: {
                  isSelected: true,
                  metricDefaultIds: { not: null },
                },
              },
            },
          },
        },
      });

      const allMetricDefaultIds = [
        ...new Set(
          impacts.flatMap(
            (impact) =>
              impact.industry?.details?.flatMap((detail) => {
                if (
                  impact.industryDetailId &&
                  impact.industryDetailId !== detail.id
                )
                  return [];
                return this.utilService.jsonToNumberList(
                  detail.metricDefaultIds,
                );
              }) || [],
          ),
        ),
        ...new Set(
          financials.flatMap(
            (financial) =>
              financial.industry?.details?.flatMap((detail) => {
                if (
                  financial.industryDetailId &&
                  financial.industryDetailId !== detail.id
                )
                  return [];
                return this.utilService.jsonToNumberList(
                  detail.metricDefaultIds,
                );
              }) || [],
          ),
        ),
      ];

      const metricDefaults = await this.prismaService.metricDefault.findMany({
        where: {
          id: { in: allMetricDefaultIds },
        },
        select: {
          id: true,
          metricName: true,
          categoryType: true,
          unitType: true,
          note: true,
        },
      });
      return metricDefaults;
    } else if (dto.isActivity) {
      const userSector =
        await this.prismaService.userOrganization.findFirstOrThrow({
          where: {
            userId: user.id,
            organizationId: governanceReport.organizationId,
          },
          select: {
            organization: {
              select: {
                sectors: {
                  select: {
                    sector: {
                      select: { metricDefaults: { select: { id: true } } },
                    },
                  },
                },
              },
            },
          },
        });
      if (!userSector) {
        return [];
      }
      const metricDefaults = await this.prismaService.metricDefault.findMany({
        where: {
          id: {
            in: userSector.organization.sectors.flatMap((sector) =>
              sector.sector.metricDefaults.map((md) => md.id),
            ),
          },
        },
        select: {
          id: true,
          metricName: true,
          categoryType: true,
          unitType: true,
          note: true,
        },
      });
      return metricDefaults;
    }
    return [];
  }

  async getActivities(user: UserDto, dto: GetMetricActivityDto) {
    const activities = await this.prismaService.metricActivity.findMany({
      select: {
        activityType: true,
        relatedId: true,
        payload: true,
        createdAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
      },
      where: { metricId: dto.metricId },
      orderBy: { createdAt: 'desc' },
    });
    for (const a of activities) {
      a['createdUserInfo'] = plainToInstance(UserDataDto, a.createdUser, {
        excludeExtraneousValues: true,
      });
      delete a.createdUser;
    }
    return activities;
  }

  async getReports(user: UserDto, dto: GetMetricReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;

    const hasMetricPermission = user.permissions.some(
      (p) => p.key == PermissionEnum.METRIC_SUPERVISOR,
    );
    const reports = await this.prismaService.metricReport.findMany({
      select: {
        id: true,
        name: true,
        code: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        metrics: { select: { performances: true } },
      },
      where: {
        organizationId: dto.subsidiaryId,
        metrics: hasMetricPermission
          ? {
              some: { supervisors: { some: { profile: { userId: user.id } } } },
            }
          : undefined,
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      r['hasPerformance'] = r.metrics.some((g) => g.performances.length > 0);
      delete r.createdUser;
      delete r.metrics;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetMetricReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        OR: [
          {
            createdMetricReports: {
              some: { id: dto.reportId, organizationId: dto.subsidiaryId },
            },
          },
          {
            createdMetrics: {
              some: {
                report: { id: dto.reportId, organizationId: dto.subsidiaryId },
              },
            },
          },
          {
            helpedMetricHelps: {
              some: {
                metric: {
                  report: {
                    id: dto.reportId,
                    organizationId: dto.subsidiaryId,
                  },
                },
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetMetricHelpDetailDto,
  ): Promise<{ report: any; metrics: any[] }> {
    const result = await this.getMetrics(user, {
      reportId: dto.reportId,
    });
    const helps = await this.prismaService.metricHelp.findMany({
      where: {
        helpedUserId: user.id,
        metric: { reportId: dto.reportId },
      },
    });
    const helpMetricIds = helps.map((h) => h.metricId);
    result.metrics = result.metrics
      .map((m) => {
        for (const issue of m.issues) {
          issue.metrics = issue.metrics.filter((m) =>
            helpMetricIds.includes(m.id),
          );
        }
        return m;
      })
      .filter((m) => m.issues.some((i) => i.metrics.length > 0));
    return {
      report: result.report,
      metrics: result.metrics,
    };
  }

  async getSupervisorDetail(
    user: UserDto,
    dto: GetMetricSupervisorDetailDto,
  ): Promise<{ report: any; metrics: any[] }> {
    const result = await this.getMetrics(user, {
      reportId: dto.reportId,
    });
    const supervisors = await this.prismaService.metricSupervisor.findMany({
      where: {
        profile: { userId: user.id },
        metric: { reportId: dto.reportId },
      },
    });
    const supervisorMetricIds = supervisors.map((s) => s.metricId);
    result.metrics = result.metrics
      .map((m) => {
        for (const issue of m.issues) {
          issue.metrics = issue.metrics.filter((m) =>
            supervisorMetricIds.includes(m.id),
          );
        }
        return m;
      })
      .filter((m) => m.issues.some((i) => i.metrics.length > 0));
    return {
      report: result.report,
      metrics: result.metrics,
    };
  }

  async getSupervisors(user: UserDto) {
    const profiles = await this.prismaService.metricSupervisorProfile.findMany({
      select: {
        id: true,
        department: true,
        user: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            isOtp: true,
            userOrganizations: true,
          },
        },
      },
      where: {
        organizationId: user.organizationId,
        user: {
          userOrganizations: { some: { organizationId: user.organizationId } },
        },
      },
    });
    for (const p of profiles) {
      p['canSendMail'] = p.user.isOtp;
      p['userInfo'] = plainToInstance(UserDataDto, p.user, {
        excludeExtraneousValues: true,
      });
      delete p.user;
    }
    return profiles;
  }

  private async getLastReport(
    organizationId: number,
    options?: { hasPerformance?: boolean },
  ): Promise<MetricReport> {
    const report = await this.prismaService.metricReport.findFirst({
      where: {
        organizationId: organizationId,
        metrics:
          options?.hasPerformance === undefined
            ? undefined
            : options.hasPerformance
              ? { some: { performances: { some: {} } } }
              : { none: { performances: { some: {} } } },
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createMetric(
    user: UserDto,
    dto: CreateMetricDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ) {
    const metricDefault = await this.metricValidation.createMetric(user, dto, {
      tx: options?.tx,
    });

    await this.prismaService.$transaction(async (tx) => {
      tx = options?.tx ?? tx;

      const metric = await tx.metric.create({
        data: {
          createdUserId: user.id,
          reportId: dto.reportId,
          strategyDetailId: dto.strategyDetailId,
          defaultId: metricDefault?.id,
          facilityIds: dto.facilityIds,
          metricName: metricDefault?.metricName ?? dto.metricName,
          categoryType: metricDefault?.categoryType ?? dto.categoryType,
          unitType: metricDefault?.unitType ?? dto.unitType,
          periodType: dto.periodType,
          note: metricDefault?.note ?? dto.note,
          isActivity: dto.isActivity ?? false,
        },
      });
      await this.createActivity(
        user,
        {
          metricId: metric.id,
          activityType: MetricActivityTypeEnum.METRIC_CREATE,
          payload: {
            metricName: metric.metricName,
            categoryType: metric.categoryType,
            unitType: metric.unitType,
            note: metric.note,
          },
        },
        { tx: tx },
      );
    });
  }

  async createReport(
    user: UserDto,
    dto: CreateMetricReportDto,
    governanceReport: GovernanceReport,
  ): Promise<{ report: any; metrics: any[] }> {
    const maxCodeResult = await this.prismaService.metricReport.aggregate({
      _max: { code: true },
      where: { organizationId: user.organizationId },
    });
    const code = (maxCodeResult._max.code ?? 0) + 1;
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const report = await tx.metricReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          governanceReportId: governanceReport.id,
          name: dto.name ?? `MH_${code}_${dateFormat}`,
          code: code,
        },
      });
      const activityMetricIdsOnUserSector = await tx.organization.findFirst({
        where: { id: user.organizationId },
        select: {
          sectors: {
            select: {
              sector: {
                select: { metricDefaults: { select: { id: true } } },
              },
            },
          },
        },
      });
      const activityMetricDefaultIds = [
        ...new Set(
          activityMetricIdsOnUserSector?.sectors.flatMap((s) =>
            s.sector.metricDefaults.map((md) => md.id),
          ) ?? [],
        ),
      ];
      const strategyDetails = await tx.strategyDetail.findMany({
        where: { strategy: { reportId: governanceReport.strategyReportId } },
        include: { strategy: true },
      });
      const impactIdsWanted = [
        ...new Set(
          strategyDetails
            .map((d) => this.utilService.jsonToNumberList(d.impactIds))
            .flat(1),
        ),
      ];

      const financialIdsWanted = [
        ...new Set(
          strategyDetails
            .map((d) => this.utilService.jsonToNumberList(d.financialIds))
            .flat(1),
        ),
      ];

      const impacts = await tx.impact.findMany({
        where: { id: { in: impactIdsWanted } },
        include: {
          industry: {
            include: {
              details: {
                where: { isSelected: true, metricDefaultIds: { not: null } },
              },
            },
          },
        },
      });

      const financials = await tx.financial.findMany({
        where: { id: { in: financialIdsWanted } },
        include: {
          industry: {
            include: {
              details: {
                where: { isSelected: true, metricDefaultIds: { not: null } },
              },
            },
          },
        },
      });
      const getMetricIds = (items: any[]) =>
        items.flatMap(
          (item: any) =>
            item.industry?.details?.flatMap((detail) =>
              this.utilService.jsonToNumberList(detail.metricDefaultIds),
            ) || [],
        );

      const allMetricDefaultIds = Array.from(
        new Set([
          ...getMetricIds(impacts),
          ...getMetricIds(financials),
          ...activityMetricDefaultIds,
        ]),
      );
      const metricDefaults = await tx.metricDefault.findMany({
        where: { id: { in: allMetricDefaultIds } },
        select: {
          id: true,
          metricName: true,
          categoryType: true,
          unitType: true,
          note: true,
        },
      });

      const mdById = new Map(metricDefaults.map((md) => [md.id, md]));

      const seen = new Set<string>();
      const toCreate: Array<{
        createdUserId: number;
        reportId: number;
        strategyDetailId: number | null;
        defaultId: number;
        isActivity?: boolean;
      }> = [];

      const makeKey = (
        reportId: number,
        strategyDetailId: number | null,
        defaultId: number,
        isActivity: boolean,
      ) =>
        `${reportId}|${strategyDetailId ?? 'null'}|${defaultId}|${
          isActivity ? 1 : 0
        }`;

      for (const sd of strategyDetails) {
        const sdImpactIds = this.utilService.jsonToNumberList(sd.impactIds);
        const relevantImpacts = impacts.filter((i) =>
          sdImpactIds.includes(i.id),
        );

        for (const impact of relevantImpacts) {
          if (!impact.industry?.details) continue;

          for (const detail of impact.industry.details) {
            if (
              impact.industryDetailId &&
              impact.industryDetailId !== detail.id
            )
              continue;

            const ids = this.utilService.jsonToNumberList(
              detail.metricDefaultIds,
            );
            for (const defaultId of ids) {
              if (!mdById.has(defaultId)) continue;
              const key = makeKey(report.id, sd.id, defaultId, false);
              if (seen.has(key)) continue;
              seen.add(key);
              toCreate.push({
                createdUserId: user.id,
                reportId: report.id,
                strategyDetailId: sd.id,
                defaultId,
              });
            }
          }
        }
      }

      for (const sd of strategyDetails) {
        const sdFinancialIds = this.utilService.jsonToNumberList(
          sd.financialIds,
        );
        const relevantFinancials = financials.filter((f) =>
          sdFinancialIds.includes(f.id),
        );

        for (const financial of relevantFinancials) {
          if (!financial.industry?.details) continue;

          for (const detail of financial.industry.details) {
            if (
              financial.industryDetailId &&
              financial.industryDetailId !== detail.id
            )
              continue;

            const ids = this.utilService.jsonToNumberList(
              detail.metricDefaultIds,
            );
            for (const defaultId of ids) {
              if (!mdById.has(defaultId)) continue;
              const key = makeKey(report.id, sd.id, defaultId, false);
              if (seen.has(key)) continue;
              seen.add(key);
              toCreate.push({
                createdUserId: user.id,
                reportId: report.id,
                strategyDetailId: sd.id,
                defaultId,
              });
            }
          }
        }
      }

      for (const defaultId of activityMetricDefaultIds) {
        if (!mdById.has(defaultId)) continue;
        const key = makeKey(report.id, null, defaultId, true);
        if (seen.has(key)) continue;
        seen.add(key);
        toCreate.push({
          createdUserId: user.id,
          reportId: report.id,
          strategyDetailId: null,
          defaultId,
          isActivity: true,
        });
      }

      if (toCreate.length > 0) {
        await tx.metric.createMany({
          data: toCreate,
        });
      }

      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, { tx });

      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.METRIC_REPORT_CREATE,
          effectedUserIds,
          payload: { reportId: report.id },
        },
      ]);
      return await this.getMetrics(user, { reportId: report.id }, { tx });
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: any; metrics: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      hasPerformance: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        hasPerformance: false,
      });
      if (!lastReport) {
        return {
          report: null,
          metrics: [],
        };
      }
    }
    const { report, metrics } = await this.getMetrics(user, {
      reportId: lastReport.id,
    });
    return {
      report: report,
      metrics: metrics,
    };
  }

  async createHelp(user: UserDto, dto: CreateMetricHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.metricHelp.deleteMany({
        where: { metricId: dto.metricId },
      });
      const help = await tx.metricHelp.create({
        data: {
          createdUserId: user.id,
          helpedUserId: dto.helpedUserId,
          metricId: dto.metricId,
        },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.METRIC_HELP,
          effectedUserIds: [dto.helpedUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async createSupervisor(
    user: UserDto,
    dto: CreateMetricSupervisorDto,
    request: Request,
    args: {
      supervisorUser?: User;
      supervisorProfile?: MetricSupervisorProfile;
    },
  ) {
    let password: string;
    await this.prismaService.$transaction(async (tx) => {
      if (!args.supervisorUser) {
        password = this.utilService.getRandomString(
          this.utilService.getRandomInteger(8, 12),
        );
        const hash = await bcrypt.hash(password, 10);
        const role = await tx.role.findFirstOrThrow({
          where: { key: RoleEnum.EMPLOYEE },
        });
        const permission = await tx.permission.findFirstOrThrow({
          where: { key: PermissionEnum.METRIC_SUPERVISOR },
        });
        args.supervisorUser = await tx.user.create({
          data: {
            name: dto.name,
            surname: dto.surname,
            email: dto.email,
            password: hash,
            isOtp: true,
            isActive: true,
            userOrganizations: {
              create: {
                organizationId: user.organizationId,
                roleId: role.id,
                title: dto.title,
              },
            },
            userPermissions: {
              create: {
                organizationId: user.organizationId,
                permissionId: permission.id,
              },
            },
          },
        });
      }

      if (!args.supervisorProfile) {
        args.supervisorProfile = await tx.metricSupervisorProfile.create({
          data: {
            userId: args.supervisorUser.id,
            organizationId: user.organizationId,
            department: dto.department,
          },
        });
      }
      const supervisor = await tx.metricSupervisor.findFirst({
        where: {
          metricId: dto.metricId,
          profileId: args.supervisorProfile.id,
        },
      });
      if (!supervisor) {
        await tx.metricSupervisor.deleteMany({
          where: { metricId: dto.metricId },
        });
        const supervisor = await tx.metricSupervisor.create({
          data: {
            createdUserId: user.id,
            metricId: dto.metricId,
            profileId: args.supervisorProfile.id,
          },
        });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.METRIC_SUPERVISOR,
            effectedUserIds: [args.supervisorUser.id],
            payload: { supervisorId: supervisor.id },
          },
        ]);
      }
    });
    if (password) {
      this.mailService
        .sendMetricSupervisorInvite(user, {
          request: request,
          supervisorUser: args.supervisorUser,
          password: password,
        })
        .catch((err) => this.logger.error(err));
    }
  }

  async createActivity(
    user: UserDto,
    args: {
      metricId: number;
      activityType: MetricActivityTypeEnum;
      relatedId?: number;
      payload?: object;
    },
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ) {
    const dbService = options?.tx ?? this.prismaService;
    await dbService.metricActivity.create({
      data: {
        createdUserId: user.id,
        metricId: args.metricId,
        activityType: args.activityType,
        relatedId: args.relatedId,
        payload: args.payload,
      },
    });
  }

  async updateMetric(
    user: UserDto,
    dto: UpdateMetricDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      removeHelps?: boolean;
    },
  ) {
    await this.prismaService.$transaction(async (tx) => {
      tx = options?.tx ?? tx;
      const metric = await tx.metric.update({
        where: {
          id: dto.metricId,
          report: { organizationId: user.organizationId },
        },
        data: {
          facilityIds: dto.facilityIds,
          metricName: dto.metricName,
          goalName: dto.goalName,
          categoryType: dto.categoryType,
          unitType: dto.unitType,
          periodType: dto.periodType,
          note: dto.note,
        },
      });
      if (options?.removeHelps) {
        await tx.metricHelp.deleteMany({
          where: { metricId: dto.metricId },
        });
      }
      await this.createActivity(
        user,
        {
          metricId: metric.id,
          activityType: MetricActivityTypeEnum.METRIC_UPDATE,
          payload: {
            metricName: metric.metricName,
            goalName: metric.goalName,
            categoryType: metric.categoryType,
            unitType: metric.unitType,
            note: metric.note,
          },
        },
        { tx: tx },
      );
    });
  }

  async updateReport(user: UserDto, dto: UpdateMetricReportDto) {
    await this.prismaService.metricReport.update({
      where: { id: dto.reportId },
      data: { name: dto.name },
    });
  }

  async updateSupervisor(
    user: UserDto,
    dto: UpdateMetricSupervisorDto,
    supervisorUser: User,
    request: Request,
  ) {
    if (dto.sendMail) {
      const password = this.utilService.getRandomString(
        this.utilService.getRandomInteger(8, 12),
      );
      const hash = await bcrypt.hash(password, 10);
      await this.prismaService.user.update({
        where: { id: supervisorUser.id },
        data: { password: hash },
      });
      this.mailService
        .sendMetricSupervisorInvite(user, {
          request: request,
          supervisorUser: supervisorUser,
          password: password,
        })
        .catch((err) => this.logger.error(err));
    }
  }

  async deleteMetric(user: UserDto, dto: DeleteMetricDto): Promise<void> {
    await this.prismaService.$transaction(async (tx) => {
      await tx.metric.delete({
        where: {
          id: dto.metricId,
          report: { organizationId: user.organizationId },
        },
      });
      await this.createActivity(
        user,
        {
          metricId: dto.metricId,
          activityType: MetricActivityTypeEnum.METRIC_DELETE,
        },
        { tx: tx },
      );
    });
  }

  async deleteReport(user: UserDto, dto: DeleteMetricReportDto) {
    await this.prismaService.metricReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteHelp(user: UserDto, dto: DeleteMetricHelpDto) {
    await this.prismaService.metricHelp.delete({
      where: { id: dto.helpId },
    });
  }
}
