import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetStrategyDto } from '../dtos/strategy/get-strategy.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteStrategyReportDto } from '../dtos/strategy/delete-strategy-report.dto';
import { StrategyReport, PrismaClient, Prisma, Strategy } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateStrategyReportDto } from '../dtos/strategy/update-strategy-report.dto';
import { CreateStrategyHelpDto } from '../dtos/strategy/create-strategy-help.dto';
import { CreateStrategyHelpAnswerDto } from '../dtos/strategy/create-strategy-help-answer.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { GetStrategyReportDto } from '../dtos/strategy/get-strategy-report.dto';
import { GetStrategyReportUserDto } from '../dtos/strategy/get-strategy-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetStrategyHelpDetailDto } from '../dtos/strategy/get-strategy-help-detail.dto';
import { GetCurrentStrategyDto } from '../dtos/strategy/get-current-strategy.dto';
import { ActivityService } from '../../activity/activity.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateActivityArgType } from '../../activity/types/create-activity-arg.type';
import { CreateStrategyReportDto } from '../dtos/strategy/create-strategy-report.dto';
import { UpdateStrategyDto } from '../dtos/strategy/update-strategy.dto';
import { DeleteStrategyHelpDto } from '../dtos/strategy/delete-strategy-help.dto';
import { UpdateStrategyTermDto } from '../dtos/strategy/update-strategy-term.dto';
import { UpdateStrategyDetailCombineDto } from '../dtos/strategy/update-strategy-detail-combine.dto';
import { UpdateStrategyDetailSeparateDto } from '../dtos/strategy/update-strategy-detail-separate.dto';
import { StrategyHelpTypeEnum } from '../enums/strategy-help-type.enum';
import { UtilService } from '../../common/services/util.service';

@Injectable()
export class StrategyService {
  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
    private utilService: UtilService,
  ) {}

  async getStrategies(
    user: UserDto,
    dto: GetStrategyDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<{ report: any; strategies: any[] }> {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.strategyReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        code: true,
        isRoadMap: true,
        isCompleted: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
        financialReport: { select: { id: true, name: true } },
        impactReport: { select: { id: true, name: true } },
        terms: { select: { minValue: true, maxValue: true, type: true } },
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const helpType = report.isRoadMap
      ? StrategyHelpTypeEnum.DESCRIPTION_AND_TERMS
      : StrategyHelpTypeEnum.STRATEGY_FOCUS_AREA;
    const strategies = await dbService.strategy.findMany({
      select: {
        id: true,
        strategyFocusArea: true,
        description: true,
        shortTerm: true,
        mediumTerm: true,
        longTerm: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
        updatedUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        details: {
          select: {
            id: true,
            priorityIssue: true,
            subPriorityIssues: true,
            minorPriorityIssues: true,
            focusAreas: true,
            categories: {
              select: {
                financialDetail: {
                  select: {
                    categoryDescription: true,
                    categoryType: true,
                    periodType: true,
                  },
                },
                impactDetail: {
                  select: {
                    categoryDescription: true,
                    categoryType: true,
                    realityType: true,
                  },
                },
              },
            },
          },
        },
        helps: {
          take: 1,
          select: {
            id: true,
            helpType: true,
            status: true,
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
          where: { helpType: helpType },
          orderBy: { createdAt: 'desc' },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
      },
    });
    for (const s of strategies) {
      const createdUser = plainToInstance(UserDataDto, s.createdUser, {
        excludeExtraneousValues: true,
      });
      const updatedUser = plainToInstance(UserDataDto, s.updatedUser, {
        excludeExtraneousValues: true,
      });
      let helpedUser: UserDataDto;
      if (s.helps.length > 0) {
        helpedUser = plainToInstance(UserDataDto, s.helps[0].helpedUser, {
          excludeExtraneousValues: true,
        });
        helpedUser['helpType'] = s.helps[0].helpType;
        helpedUser['status'] = s.helps[0].status;
      }
      s['responsibleUserInfo'] = createdUser;
      if (helpedUser) {
        s['responsibleUserInfo'] = helpedUser;
      } else if (updatedUser) {
        s['responsibleUserInfo'] = updatedUser;
      }

      if (s['responsibleUserInfo']) {
        s['responsibleUserInfo']['isMe'] =
          s['responsibleUserInfo'].id == user.id;
      }
      delete s.createdUser;
      delete s.updatedUser;
      delete s.helps;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    delete report.createdUser;
    return { report: report, strategies: strategies };
  }

  async getCurrentStrategies(
    user: UserDto,
    dto: GetCurrentStrategyDto,
  ): Promise<{ report: any; strategies: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let strategyReport: StrategyReport;
    if (dto.reportId) {
      strategyReport = await this.prismaService.strategyReport.findFirstOrThrow(
        {
          where: {
            id: dto.reportId,
            organizationId: dto.subsidiaryId,
            isCompleted: true,
          },
        },
      );
    } else {
      strategyReport = await this.getLastReport(dto.subsidiaryId, {
        isCompleted: true,
      });
      if (!strategyReport) {
        return { report: null, strategies: [] };
      }
    }
    const { report, strategies } = await this.getStrategies(user, {
      reportId: strategyReport.id,
    });
    return {
      report: report,
      strategies: strategies,
    };
  }

  async getReports(user: UserDto, dto: GetStrategyReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.strategyReport.findMany({
      select: {
        id: true,
        name: true,
        code: true,
        isRoadMap: true,
        isCompleted: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
      },
      where: {
        organizationId: dto.subsidiaryId,
        isRoadMap: dto.isRoadMap === undefined ? undefined : dto.isRoadMap,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
        governanceReports:
          dto.hasGovernance === undefined
            ? undefined
            : dto.hasGovernance === true
              ? { some: {} }
              : { none: {} },
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      delete r.createdUser;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetStrategyReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        OR: [
          {
            createdStrategyReports: {
              some: { id: dto.reportId, organizationId: dto.subsidiaryId },
            },
          },
          {
            createdStrategies: {
              some: {
                report: { id: dto.reportId, organizationId: dto.subsidiaryId },
              },
            },
          },
          {
            updatedStrategies: {
              some: {
                report: { id: dto.reportId, organizationId: dto.subsidiaryId },
              },
            },
          },
          {
            helpedStrategyHelps: {
              some: {
                strategy: {
                  report: {
                    id: dto.reportId,
                    organizationId: dto.subsidiaryId,
                  },
                },
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetStrategyHelpDetailDto,
  ): Promise<{ report: any; strategies: any[] }> {
    const result = await this.getStrategies(user, {
      reportId: dto.reportId,
    });
    const helps = await this.prismaService.strategyHelp.findMany({
      where: {
        helpedUserId: user.id,
        strategy: { reportId: dto.reportId },
        status: HelpStatusEnum.PENDING,
      },
    });
    const helpStrategyIds = helps.map((h) => h.strategyId);
    result.strategies = result.strategies.filter((s: Strategy) =>
      helpStrategyIds.includes(s.id),
    );
    return {
      report: result.report,
      strategies: result.strategies,
    };
  }

  private async getLastReport(
    organizationId: number,
    options?: { isCompleted?: boolean },
  ): Promise<StrategyReport> {
    const report = await this.prismaService.strategyReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createReport(
    user: UserDto,
    dto: CreateStrategyReportDto,
  ): Promise<{ report: any; strategies: any[] }> {
    const financialReport = await this.prismaService.financialReport.findFirst({
      where: {
        organizationId: user.organizationId,
        code: dto.materialityCode,
      },
      include: {
        significances: true,
        financials: { include: { details: true } },
      },
    });
    const impactReport = await this.prismaService.impactReport.findFirst({
      where: {
        organizationId: user.organizationId,
        code: dto.materialityCode,
      },
      include: { significances: true, impacts: { include: { details: true } } },
    });
    if (!financialReport && !impactReport) {
      throw new BadRequestException('Önceliklendirme analizi bulunamadı.');
    }
    if (financialReport && financialReport.significances.length == 0) {
      throw new BadRequestException('Finansal önemlilik tamamlanmadı.');
    }
    if (impactReport && impactReport.significances.length == 0) {
      throw new BadRequestException('Etki önemliliği tamamlanmadı.');
    }
    const strategyObj: {
      [key: string]: {
        financialIds: Set<number>;
        impactIds: Set<number>;
        subPriorityIssues: Set<string>;
        minorPriorityIssues: Set<string>;
        focusAreas: Set<string>;
        categories: {
          financialDetailId?: number;
          impactDetailId?: number;
        }[];
      };
    } = {};
    if (financialReport) {
      for (const financial of financialReport.financials) {
        const categories = financial.details.map((d) => ({
          financialDetailId: d.id,
        }));

        if (strategyObj[financial.priorityIssue]) {
          strategyObj[financial.priorityIssue].financialIds.add(financial.id);
          if (financial.subPriorityIssue) {
            strategyObj[financial.priorityIssue].subPriorityIssues.add(
              financial.subPriorityIssue,
            );
          }
          if (financial.minorPriorityIssue) {
            strategyObj[financial.priorityIssue].subPriorityIssues.add(
              financial.minorPriorityIssue,
            );
          }
          this.utilService
            .jsonToStringList(financial.focusAreas)
            .map((focusArea) =>
              strategyObj[financial.priorityIssue].focusAreas.add(focusArea),
            );
          categories.map((c) =>
            strategyObj[financial.priorityIssue].categories.push(c),
          );
        } else {
          strategyObj[financial.priorityIssue] = {
            financialIds: new Set([financial.id]),
            impactIds: new Set(),
            subPriorityIssues: new Set(
              financial.subPriorityIssue ? [financial.subPriorityIssue] : [],
            ),
            minorPriorityIssues: new Set(
              financial.minorPriorityIssue
                ? [financial.minorPriorityIssue]
                : [],
            ),
            focusAreas: new Set(
              this.utilService.jsonToStringList(financial.focusAreas),
            ),
            categories: categories,
          };
        }
      }
    }
    if (impactReport) {
      for (const impact of impactReport.impacts) {
        const categories = impact.details.map((d) => ({
          impactDetailId: d.id,
        }));

        if (strategyObj[impact.priorityIssue]) {
          strategyObj[impact.priorityIssue].impactIds.add(impact.id);
          this.utilService
            .jsonToStringList(impact.focusAreas)
            .map((focusArea) =>
              strategyObj[impact.priorityIssue].focusAreas.add(focusArea),
            );
          if (impact.subPriorityIssue) {
            strategyObj[impact.priorityIssue].subPriorityIssues.add(
              impact.subPriorityIssue,
            );
          }
          if (impact.minorPriorityIssue) {
            strategyObj[impact.priorityIssue].subPriorityIssues.add(
              impact.minorPriorityIssue,
            );
          }
          categories.map((c) =>
            strategyObj[impact.priorityIssue].categories.push(c),
          );
        } else {
          strategyObj[impact.priorityIssue] = {
            financialIds: new Set(),
            impactIds: new Set([impact.id]),
            subPriorityIssues: new Set(
              impact.subPriorityIssue ? [impact.subPriorityIssue] : [],
            ),
            minorPriorityIssues: new Set(
              impact.minorPriorityIssue ? [impact.minorPriorityIssue] : [],
            ),
            focusAreas: new Set(
              this.utilService.jsonToStringList(impact.focusAreas),
            ),
            categories: categories,
          };
        }
      }
    }
    const strategiesCreate: Prisma.StrategyUncheckedCreateNestedManyWithoutReportInput['create'] =
      Object.entries(strategyObj).map(([priorityIssue, data]) => ({
        createdUserId: user.id,
        details: {
          create: {
            createdUserId: user.id,
            financialIds: Array.from(data.financialIds),
            impactIds: Array.from(data.impactIds),
            priorityIssue: priorityIssue,
            subPriorityIssues: Array.from(data.subPriorityIssues),
            minorPriorityIssues: Array.from(data.minorPriorityIssues),
            focusAreas: Array.from(data.focusAreas),
            categories: {
              createMany: {
                data: data.categories.map((c) => ({
                  createdUserId: user.id,
                  financialDetailId: c.financialDetailId,
                  impactDetailId: c.impactDetailId,
                })),
              },
            },
          },
        } as Prisma.StrategyDetailUncheckedCreateNestedManyWithoutStrategyInput,
      }));

    const maxCodeResult = await this.prismaService.strategyReport.aggregate({
      _max: { code: true },
      where: { organizationId: user.organizationId },
    });
    const code = (maxCodeResult._max.code ?? 0) + 1;
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const report = await tx.strategyReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          financialReportId: financialReport?.id,
          impactReportId: impactReport?.id,
          name: dto.name ?? `ST_${code}_${dateFormat}`,
          code: code,
          strategies: { create: strategiesCreate },
        },
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.STRATEGY_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: report.id },
        },
      ]);
      return await this.getStrategies(
        user,
        { reportId: report.id },
        { tx: tx },
      );
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: any; strategies: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      isCompleted: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        isCompleted: false,
      });
      if (!lastReport) {
        return {
          report: null,
          strategies: [],
        };
      }
    }
    const { report, strategies } = await this.getStrategies(user, {
      reportId: lastReport.id,
    });
    return {
      report: report,
      strategies: strategies,
    };
  }

  async createHelp(user: UserDto, dto: CreateStrategyHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.strategyHelp.deleteMany({
        where: {
          strategyId: dto.strategyId,
          status: HelpStatusEnum.PENDING,
          helpType: dto.helpType,
        },
      });
      const help = await tx.strategyHelp.create({
        data: {
          createdUserId: user.id,
          helpedUserId: dto.helpedUserId,
          strategyId: dto.strategyId,
          helpType: dto.helpType,
        },
      });
      await this.updateStrategy(
        user,
        {
          strategyId: dto.strategyId,
          strategyFocusArea:
            dto.helpType == StrategyHelpTypeEnum.STRATEGY_FOCUS_AREA
              ? null
              : undefined,
          description:
            dto.helpType == StrategyHelpTypeEnum.DESCRIPTION_AND_TERMS
              ? null
              : undefined,
          shortTerm:
            dto.helpType == StrategyHelpTypeEnum.DESCRIPTION_AND_TERMS
              ? null
              : undefined,
          mediumTerm:
            dto.helpType == StrategyHelpTypeEnum.DESCRIPTION_AND_TERMS
              ? null
              : undefined,
          longTerm:
            dto.helpType == StrategyHelpTypeEnum.DESCRIPTION_AND_TERMS
              ? null
              : undefined,
        },
        { tx: tx, removeUpdatedUserId: true },
      );
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.STRATEGY_HELP,
          effectedUserIds: [dto.helpedUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async createHelpAnswer(user: UserDto, dto: CreateStrategyHelpAnswerDto) {
    const effectedUserIds: number[] = [];
    await this.prismaService.$transaction(async (tx) => {
      for (const a of dto.answers) {
        await this.updateStrategy(
          user,
          {
            strategyId: a.strategyId,
            strategyFocusArea: a.strategyFocusArea,
            description: a.description,
            shortTerm: a.shortTerm,
            mediumTerm: a.mediumTerm,
            longTerm: a.longTerm,
          },
          { tx: tx, removeUpdatedUserId: true },
        );
        const help = await tx.strategyHelp.findFirstOrThrow({
          where: {
            helpedUserId: user.id,
            strategy: { id: a.strategyId, reportId: dto.reportId },
            status: HelpStatusEnum.PENDING,
          },
        });
        effectedUserIds.push(help.createdUserId);
        await tx.strategyHelp.update({
          where: { id: help.id },
          data: {
            status: HelpStatusEnum.APPROVED,
            strategyFocusArea: a.strategyFocusArea,
            description: a.description,
            shortTerm: a.shortTerm,
            mediumTerm: a.mediumTerm,
            longTerm: a.longTerm,
          },
        });
      }
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.STRATEGY_HELP_ANSWER,
          effectedUserIds: effectedUserIds,
          payload: { reportId: dto.reportId },
        },
      ]);
    });
  }

  async updateStrategy(
    user: UserDto,
    dto: UpdateStrategyDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      removeUpdatedUserId?: boolean;
      removeHelps?: boolean;
    },
  ) {
    await this.prismaService.$transaction(async (tx) => {
      tx = options?.tx ?? tx;
      const strategy = await tx.strategy.update({
        where: {
          id: dto.strategyId,
          report: { organizationId: user.organizationId },
        },
        data: {
          updatedUserId: options?.removeUpdatedUserId ? null : user.id,
          strategyFocusArea: dto.strategyFocusArea,
          description: dto.description,
          shortTerm: dto.shortTerm,
          mediumTerm: dto.mediumTerm,
          longTerm: dto.longTerm,
        },
        include: { report: true },
      });
      if (options?.removeHelps) {
        const helpType = strategy.report.isRoadMap
          ? StrategyHelpTypeEnum.DESCRIPTION_AND_TERMS
          : StrategyHelpTypeEnum.STRATEGY_FOCUS_AREA;
        await tx.strategyHelp.deleteMany({
          where: {
            strategyId: dto.strategyId,
            helpType: helpType,
          },
        });
      }
    });
  }

  async updateReport(user: UserDto, dto: UpdateStrategyReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.strategyReport.update({
        where: { id: dto.reportId },
        data: {
          name: dto.name,
          isRoadMap: dto.isRoadMap ? true : undefined,
          isCompleted: dto.isCompleted ? true : undefined,
        },
      });
      const activityArgs: CreateActivityArgType[] = [];
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      if (dto.isRoadMap) {
        tx.strategyHelp.deleteMany({
          where: {
            strategy: { reportId: dto.reportId },
            helpType: StrategyHelpTypeEnum.STRATEGY_FOCUS_AREA,
            status: HelpStatusEnum.PENDING,
          },
        });
        activityArgs.push({
          activityType: ActivityEnum.STRATEGY_REPORT_ROAD_MAP,
          effectedUserIds: effectedUserIds,
          payload: { reportId: dto.reportId },
        });
      }
      if (dto.isCompleted) {
        activityArgs.push({
          activityType: ActivityEnum.STRATEGY_REPORT_COMPLETE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: dto.reportId },
        });
      }
      await this.activityService.createActivity(tx, user, activityArgs);
    });
  }

  async updateDetailsCombine(
    user: UserDto,
    dto: UpdateStrategyDetailCombineDto,
  ): Promise<void> {
    await this.prismaService.$transaction(async (tx) => {
      await tx.strategy.update({
        where: { id: dto.strategyIds[0] },
        data: {
          updatedUserId: user.id,
          strategyFocusArea: dto.strategyFocusArea,
        },
      });
      await tx.strategyDetail.updateMany({
        where: { strategyId: { in: dto.strategyIds } },
        data: { strategyId: dto.strategyIds[0] },
      });
      await tx.strategy.deleteMany({
        where: { id: { in: dto.strategyIds.slice(1) } },
      });
      await tx.strategyHelp.deleteMany({
        where: { strategyId: { in: dto.strategyIds } },
      });
    });
  }

  async updateDetailsSeparate(
    user: UserDto,
    dto: UpdateStrategyDetailSeparateDto,
    strategy: Strategy,
  ): Promise<void> {
    await this.prismaService.$transaction(async (tx) => {
      await tx.strategy.update({
        where: { id: strategy.id },
        data: {
          updatedUserId: user.id,
          strategyFocusArea: dto.details[0].strategyFocusArea,
        },
      });
      for (const d of dto.details.slice(1)) {
        await tx.strategy.create({
          data: {
            createdUserId: user.id,
            reportId: strategy.reportId,
            strategyFocusArea: d.strategyFocusArea,
            details: { connect: { id: d.detailId } },
          },
        });
      }
      await tx.strategyHelp.deleteMany({
        where: { strategyId: strategy.id },
      });
    });
  }

  async updateTerms(user: UserDto, dto: UpdateStrategyTermDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.strategyTerm.deleteMany({
        where: {
          reportId: dto.reportId,
          type: { notIn: dto.terms.map((t) => t.type) },
        },
      });
      for (const t of dto.terms) {
        const term = await tx.strategyTerm.findFirst({
          where: { reportId: dto.reportId, type: t.type },
        });
        await tx.strategyTerm.upsert({
          where: { id: term?.id ?? 0 },
          create: {
            createdUserId: user.id,
            reportId: dto.reportId,
            minValue: t.minValue,
            maxValue: t.maxValue,
            type: t.type,
          },
          update: {
            createdUserId: user.id,
            minValue: t.minValue,
            maxValue: t.maxValue,
          },
        });
      }
    });
  }

  async deleteReport(user: UserDto, dto: DeleteStrategyReportDto) {
    await this.prismaService.strategyReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteHelp(user: UserDto, dto: DeleteStrategyHelpDto) {
    await this.prismaService.strategyHelp.delete({
      where: { id: dto.helpId },
    });
  }
}
