import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetGovernanceDto } from '../dtos/governance/get-governance.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { addMinutes, addMonths, format } from 'date-fns';
import * as bcrypt from 'bcrypt';
import { DeleteGovernanceReportDto } from '../dtos/governance/delete-governance-report.dto';
import {
  GovernanceReport,
  PrismaClient,
  Prisma,
  StrategyReport,
  Governance,
  GovernanceMemberProfile,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { GetGovernanceReportDto } from '../dtos/governance/get-governance-report.dto';
import { GetGovernanceReportUserDto } from '../dtos/governance/get-governance-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetCurrentGovernanceDto } from '../dtos/governance/get-current-governance.dto';
import { ActivityService } from '../../activity/activity.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateGovernanceReportDto } from '../dtos/governance/create-governance-report.dto';
import { UpdateGovernanceDto } from '../dtos/governance/update-governance.dto';
import { CreateGovernanceDto } from '../dtos/governance/create-governance.dto';
import { CreateGovernanceMemberDto } from '../dtos/governance/create-governance-member.dto';
import { UtilService } from '../../common/services/util.service';
import { CreateGovernanceMeetingDto } from '../dtos/governance/create-governance-meeting.dto';
import { GetGovernanceMemberDto } from '../dtos/governance/get-governance-member.dto';
import { GetGovernanceMeetingDto } from '../dtos/governance/get-governance-meeting.dto';
import { GetGovernancePriorityIssueDto } from '../dtos/governance/get-governance-priority-issue.dto';
import { UpdateGovernanceMemberDto } from '../dtos/governance/update-governance-member.dto';
import { DeleteGovernanceMemberDto } from '../dtos/governance/delete-governance-member.dto';
import { DeleteGovernanceMeetingDto } from '../dtos/governance/delete-governance-meeting.dto';
import { UpdateGovernanceMeetingDto } from '../dtos/governance/update-governance-meeting.dto';
import { MailService } from '../../mail/mail.service';
import { GetGovernanceMemberProfileDto } from '../dtos/governance/get-governance-member-profile.dto';
import { CreateGovernanceMemberProfileDto } from '../dtos/governance/create-governance-member-profile.dto';
import { DeleteGovernanceDto } from '../dtos/governance/delete-governance.dto';
import { RoleEnum } from '../../common/enums/role.enum';
import { PermissionEnum } from '../../common/enums/permission.enum';
import { CreateGovernanceMemberProfileCodeDto } from '../dtos/governance/create-governance-member-profile-code.dto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class GovernanceService {
  private readonly logger = new Logger(GovernanceService.name);
  private readonly FRONT_URL: string;

  constructor(
    private prismaService: PrismaService,
    private configService: ConfigService,
    private activityService: ActivityService,
    private mailService: MailService,
    private organizationService: OrganizationService,
    private utilService: UtilService,
    private organizationUtilService: OrganizationUtilService,
  ) {
    this.FRONT_URL = this.configService.getOrThrow('FRONT_URL');
  }

  async getGovernances(
    user: UserDto,
    dto: GetGovernanceDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<{ report: any; governances: any[] }> {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.governanceReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        code: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
        strategyReport: { select: { id: true, name: true } },
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const governances = await dbService.governance.findMany({
      select: {
        id: true,
        parentId: true,
        name: true,
        description: true,
        meetingFrequencyType: true,
        priorityIssues: true,
        order: true,
        isDefault: true,
        isTop: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
        members: {
          select: {
            id: true,
            department: true,
            isLead: true,
            profile: {
              select: {
                id: true,
                userId: true,
                name: true,
                surname: true,
                email: true,
                genderType: true,
                educationType: true,
                experienceYear: true,
                experienceType: true,
                experienceText: true,
                birthDate: true,
                tokenExpiredAt: true,
              },
            },
          },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
      },
    });
    for (const g of governances) {
      g['createdUserInfo'] = plainToInstance(UserDataDto, g.createdUser, {
        excludeExtraneousValues: true,
      });
      delete g.createdUser;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    delete report.createdUser;
    return { report: report, governances: governances };
  }

  async getCurrentGovernances(
    user: UserDto,
    dto: GetCurrentGovernanceDto,
  ): Promise<{ report: any; governances: any[] }> {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let governanceReport: GovernanceReport;
    if (dto.reportId) {
      governanceReport =
        await this.prismaService.governanceReport.findFirstOrThrow({
          where: {
            id: dto.reportId,
            organizationId: dto.subsidiaryId,
            governances: { some: { members: {} } },
          },
        });
    } else {
      governanceReport = await this.getLastReport(dto.subsidiaryId, {
        hasMember: true,
      });
      if (!governanceReport) {
        return { report: null, governances: [] };
      }
    }
    const { report, governances } = await this.getGovernances(user, {
      reportId: governanceReport.id,
    });
    return {
      report: report,
      governances: governances,
    };
  }

  async getReports(user: UserDto, dto: GetGovernanceReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.governanceReport.findMany({
      select: {
        id: true,
        name: true,
        code: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            email: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        governances: { select: { members: true } },
      },
      where: { organizationId: dto.subsidiaryId },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      r['hasMember'] = r.governances.some((g) => g.members.length > 0);
      delete r.createdUser;
      delete r.governances;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetGovernanceReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        OR: [
          {
            createdGovernanceReports: {
              some: { id: dto.reportId, organizationId: dto.subsidiaryId },
            },
          },
          {
            createdGovernances: {
              some: {
                report: { id: dto.reportId, organizationId: dto.subsidiaryId },
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getMembers(user: UserDto, dto: GetGovernanceMemberDto) {
    const memberProfiles =
      await this.prismaService.governanceMemberProfile.findMany({
        select: {
          id: true,
          userId: true,
          name: true,
          surname: true,
          email: true,
          genderType: true,
          educationType: true,
          experienceYear: true,
          experienceType: true,
          experienceText: true,
          birthDate: true,
          tokenExpiredAt: true,
          members: {
            select: {
              id: true,
              governance: {
                select: {
                  id: true,
                  name: true,
                  report: { select: { id: true, name: true } },
                },
              },
              department: true,
              isLead: true,
            },
          },
        },
        where: {
          organizationId: user.organizationId,
          members: dto.reportId
            ? { some: { governance: { reportId: dto.reportId } } }
            : undefined,
        },
      });
    return memberProfiles;
  }

  async getMemberProfile(dto: GetGovernanceMemberProfileDto) {
    const memberProfile =
      await this.prismaService.governanceMemberProfile.findFirstOrThrow({
        select: {
          name: true,
          surname: true,
          email: true,
          genderType: true,
          educationType: true,
          experienceYear: true,
          experienceType: true,
          experienceText: true,
          birthDate: true,
          organization: {
            select: { id: true, name: true, image: true },
          },
        },
        where: {
          userId: null,
          token: dto.token,
          tokenExpiredAt: { gt: new Date() },
        },
      });
    return memberProfile;
  }

  async getMeetings(user: UserDto, dto: GetGovernanceMeetingDto) {
    const meetings = await this.prismaService.governanceMeeting.findMany({
      select: {
        id: true,
        governance: { select: { id: true, name: true } },
        memberProfileIds: true,
        fileIds: true,
        meetingDate: true,
        createdAt: true,
        updatedAt: true,
      },
      where: {
        governance: {
          report: { id: dto.reportId, organizationId: user.organizationId },
        },
      },
    });
    const fileIds = [
      ...new Set(
        meetings
          .map((m) => this.utilService.jsonToNumberList(m.fileIds))
          .flat(1),
      ),
    ];
    const files = await this.prismaService.fileManager.findMany({
      select: {
        id: true,
        name: true,
        url: true,
        type: true,
        size: true,
        createdAt: true,
      },
      where: {
        id: { in: fileIds },
        organizationId: user.organizationId,
      },
    });
    for (const m of meetings) {
      m['files'] = files.filter((f) =>
        this.utilService.jsonToNumberList(m.fileIds).includes(f.id),
      );
      delete m.fileIds;
    }
    return meetings;
  }

  async getPriorityIssues(user: UserDto, dto: GetGovernancePriorityIssueDto) {
    const report = await this.prismaService.governanceReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
    });
    const strategyDetails = await this.prismaService.strategyDetail.findMany({
      distinct: ['priorityIssue'],
      where: { strategy: { reportId: report.strategyReportId } },
    });
    const priorityIssues = strategyDetails.map((d) => d.priorityIssue);
    return { priorityIssues: priorityIssues };
  }

  private async getLastReport(
    organizationId: number,
    options?: { hasMember?: boolean },
  ): Promise<GovernanceReport> {
    const report = await this.prismaService.governanceReport.findFirst({
      where: {
        organizationId: organizationId,
        governances:
          options?.hasMember === undefined
            ? undefined
            : options.hasMember
              ? { some: { members: { some: {} } } }
              : { none: { members: { some: {} } } },
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async createGovernance(user: UserDto, dto: CreateGovernanceDto) {
    const governance = await this.prismaService.$transaction(async (tx) => {
      const governance = await tx.governance.create({
        data: {
          createdUserId: user.id,
          reportId: dto.reportId,
          parentId: dto.parentId,
          name: dto.name,
          order: dto.order,
        },
      });
      if (dto.subId) {
        await tx.governance.update({
          where: { id: dto.subId },
          data: { parentId: governance.id },
        });
      }
      if (dto.parentId) {
        await tx.governance.updateMany({
          where: {
            id: { not: governance.id },
            reportId: governance.reportId,
            parentId: governance.parentId,
            order: { gte: governance.order },
          },
          data: { order: { increment: 1 } },
        });
      }
      return governance;
    });
    return governance;
  }

  async createReport(
    user: UserDto,
    dto: CreateGovernanceReportDto,
    strategyReport: StrategyReport,
  ): Promise<{ report: any; governances: any[] }> {
    const governancesCreate: Prisma.GovernanceCreateManyInput[] = [];
    const strategies = await this.prismaService.strategy.findMany({
      where: { reportId: strategyReport.id, strategyFocusArea: { not: null } },
      include: { details: true },
    });

    const maxCodeResult = await this.prismaService.governanceReport.aggregate({
      _max: { code: true },
      where: { organizationId: user.organizationId },
    });
    const code = (maxCodeResult._max.code ?? 0) + 1;
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const report = await tx.governanceReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          strategyReportId: strategyReport.id,
          name: dto.name ?? `YT_${code}_${dateFormat}`,
          code: code,
        },
      });
      const topDefaultGovernance = await tx.governance.create({
        data: {
          createdUserId: user.id,
          reportId: report.id,
          name: 'Yönetim Kurulu',
          order: 1,
          isDefault: true,
          isTop: true,
        },
      });
      const defaultGovernance = await tx.governance.create({
        data: {
          createdUserId: user.id,
          reportId: report.id,
          parentId: topDefaultGovernance.id,
          name: 'Sürdürülebilirlik Komitesi',
          order: 1,
          isDefault: true,
        },
      });
      let order = 1;
      for (const s of strategies) {
        const priorityIssues = [
          ...new Set(s.details.map((d) => d.priorityIssue)),
        ];
        governancesCreate.push({
          createdUserId: user.id,
          reportId: report.id,
          parentId: defaultGovernance.id,
          name: s.strategyFocusArea,
          priorityIssues: priorityIssues,
          order: order++,
        });
      }
      await tx.governance.createMany({ data: governancesCreate });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.GOVERNANCE_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: report.id },
        },
      ]);
      return await this.getGovernances(
        user,
        { reportId: report.id },
        { tx: tx },
      );
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: any; governances: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      hasMember: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        hasMember: false,
      });
      if (!lastReport) {
        return {
          report: null,
          governances: [],
        };
      }
    }
    const { report, governances } = await this.getGovernances(user, {
      reportId: lastReport.id,
    });
    return {
      report: report,
      governances: governances,
    };
  }

  async createMember(
    user: UserDto,
    dto: CreateGovernanceMemberDto,
    memberProfile?: GovernanceMemberProfile,
  ) {
    let sendMail = false;
    await this.prismaService.$transaction(async (tx) => {
      if (dto.isLead) {
        await tx.governanceMember.updateMany({
          where: { governanceId: dto.governanceId, isLead: true },
          data: { isLead: false },
        });
      }
      if (!memberProfile) {
        const memberUser = await tx.user.findFirst({
          where: {
            email: dto.email,
            userOrganizations: {
              some: { organizationId: user.organizationId },
            },
          },
        });
        if (!memberUser) {
          sendMail = true;
        }
        const token = this.utilService.getRandomString(
          this.utilService.getRandomInteger(120, 191),
        );
        memberProfile = await tx.governanceMemberProfile.create({
          data: {
            userId: memberUser?.id,
            organizationId: user.organizationId,
            name: memberUser?.name ?? dto.name,
            surname: memberUser?.surname ?? dto.surname,
            email: dto.email,
            genderType: dto.genderType,
            educationType: dto.educationType,
            experienceYear: dto.experienceYear,
            experienceType: dto.experienceType,
            experienceText: dto.experienceText,
            birthDate: dto.birthDate,
            token: memberUser ? undefined : token,
            tokenExpiredAt: memberUser ? undefined : addMonths(new Date(), 1),
          },
        });
      }
      await tx.governanceMember.create({
        data: {
          createdUserId: user.id,
          governanceId: dto.governanceId,
          profileId: memberProfile.id,
          department: dto.department,
          isLead: dto.isLead,
        },
      });
    });
    const url = this.FRONT_URL + `/governance?token=${memberProfile.token}`;
    if (sendMail) {
      const organizationName = (
        await this.prismaService.organization.findFirstOrThrow({
          where: { id: user.organizationId },
        })
      ).name;
      this.mailService
        .sendGovernanceInvite({
          url: url,
          email: memberProfile.email,
          fullName: `${memberProfile.name} ${memberProfile.surname}`,
          organizationName: organizationName,
        })
        .catch((err) => this.logger.error(err));
    }
    return { email: memberProfile.email, url: url, sendMail: sendMail };
  }

  async createMemberProfile(
    dto: CreateGovernanceMemberProfileDto,
    memberProfile: GovernanceMemberProfile,
  ): Promise<void> {
    const roles = await this.prismaService.role.findMany();
    const hasDefaultGovernance = await this.prismaService.governance.findFirst({
      where: {
        isDefault: true,
        report: { organizationId: memberProfile.organizationId },
        members: { some: { profileId: memberProfile.id } },
      },
    });
    let roleId: number;
    let permissionIds: number[];
    if (hasDefaultGovernance) {
      const managerRole = roles.find((r) => r.key == RoleEnum.MANAGER);
      const assistantRole = roles.find((r) => r.key == RoleEnum.ASSISTANT);
      roleId = assistantRole.id;
      const managerUserPermissions =
        await this.prismaService.userPermission.findMany({
          where: {
            organizationId: memberProfile.organizationId,
            user: { userOrganizations: { some: { roleId: managerRole.id } } },
          },
        });
      if (managerUserPermissions.length == 0) {
        throw new BadRequestException('Yetkilendirme bulunamadı.');
      }
      permissionIds = [
        ...new Set(managerUserPermissions.map((up) => up.permissionId)),
      ];
    } else {
      const employeeRole = roles.find((r) => r.key == RoleEnum.EMPLOYEE);
      roleId = employeeRole.id;
      const governanceMemberPermission =
        await this.prismaService.permission.findFirstOrThrow({
          where: { key: PermissionEnum.GOVERNANCE_MEMBER },
        });
      permissionIds = [governanceMemberPermission.id];
    }

    const hash = await bcrypt.hash(dto.password, 10);
    await this.prismaService.$transaction(async (tx) => {
      const user = await tx.user.create({
        data: {
          name: memberProfile.name,
          surname: memberProfile.surname,
          email: memberProfile.email,
          password: hash,
          isActive: true,
          userOrganizations: {
            create: {
              organizationId: memberProfile.organizationId,
              roleId: roleId,
            },
          },
          agreements: {
            create: {
              personalData: dto.personalData,
              kvkk: dto.kvkk,
              userAgreement: dto.userAgreement,
              privacyPolicy: dto.privacyPolicy,
            },
          },
          userPermissions: {
            createMany: {
              data: permissionIds.map(
                (pId) =>
                  ({
                    organizationId: memberProfile.organizationId,
                    permissionId: pId,
                  }) as Prisma.UserPermissionCreateManyUserInput,
              ),
            },
          },
        },
      });
      await tx.governanceMemberProfile.update({
        where: { id: memberProfile.id },
        data: {
          userId: user.id,
          name: dto.name,
          surname: dto.surname,
          genderType: dto.genderType,
          educationType: dto.educationType,
          experienceYear: dto.experienceYear,
          experienceType: dto.experienceType,
          experienceText: dto.experienceText,
          birthDate: dto.birthDate,
        },
      });
    });
  }

  async createMemberProfileCode(
    dto: CreateGovernanceMemberProfileCodeDto,
    memberProfile: GovernanceMemberProfile,
  ) {
    const code = this.utilService.getRandomString(6).toUpperCase();
    const updatedMemberProfile =
      await this.prismaService.governanceMemberProfile.update({
        where: { id: memberProfile.id },
        data: { code: code, codeExpiredAt: addMinutes(new Date(), 3) },
      });
    this.mailService
      .sendGovernanceInviteCode({ memberProfile: updatedMemberProfile })
      .catch((err) => this.logger.error(err));
    return code;
  }

  async createMeeting(
    user: UserDto,
    dto: CreateGovernanceMeetingDto,
  ): Promise<void> {
    await this.prismaService.governanceMeeting.create({
      data: {
        createdUserId: user.id,
        governanceId: dto.governanceId,
        memberProfileIds: dto.memberProfileIds,
        fileIds: dto.fileIds,
        meetingDate: dto.meetingDate,
      },
    });
  }

  async updateGovernance(
    user: UserDto,
    dto: UpdateGovernanceDto,
    governance: Governance,
  ) {
    const updatedGovernance = await this.prismaService.$transaction(
      async (tx) => {
        const updatedGovernance = await tx.governance.update({
          where: {
            id: dto.governanceId,
            report: { organizationId: user.organizationId },
          },
          data: {
            parentId: dto.parentId,
            name: dto.name,
            description: dto.description,
            meetingFrequencyType: dto.meetingFrequencyType,
            priorityIssues: dto.priorityIssues,
            order: dto.order,
          },
        });
        if (dto.order) {
          if (governance.order > dto.order) {
            await tx.governance.updateMany({
              where: {
                id: { not: governance.id },
                reportId: governance.reportId,
                parentId: governance.parentId,
                AND: [
                  { order: { gte: dto.order } },
                  { order: { lt: governance.order } },
                ],
              },
              data: { order: { increment: 1 } },
            });
          } else if (governance.order < dto.order) {
            await tx.governance.updateMany({
              where: {
                id: { not: governance.id },
                reportId: governance.reportId,
                parentId: governance.parentId,
                AND: [
                  { order: { gt: governance.order } },
                  { order: { lte: dto.order } },
                ],
              },
              data: { order: { decrement: 1 } },
            });
          }
        }
        return updatedGovernance;
      },
    );
    return updatedGovernance;
  }

  async updateMember(
    user: UserDto,
    dto: UpdateGovernanceMemberDto,
    governance: Governance,
  ) {
    let sendMail = dto.sendMail;
    let memberProfile: GovernanceMemberProfile;
    await this.prismaService.$transaction(async (tx) => {
      if (dto.isLead) {
        await tx.governanceMember.updateMany({
          where: {
            id: { not: dto.memberId },
            governanceId: dto.governanceId ?? governance.id,
            isLead: true,
          },
          data: { isLead: false },
        });
      } else {
        if (dto.governanceId && dto.governanceId != governance.id) {
          dto.isLead = false;
        }
      }
      const member = await tx.governanceMember.update({
        where: { id: dto.memberId },
        data: {
          governanceId: dto.governanceId,
          department: dto.department,
          isLead: dto.isLead,
        },
        include: { profile: true },
      });
      memberProfile = member.profile;
      if (memberProfile.userId) {
        sendMail = false;
      } else {
        const token = this.utilService.getRandomString(
          this.utilService.getRandomInteger(120, 191),
        );
        memberProfile = await tx.governanceMemberProfile.update({
          where: { id: member.profileId },
          data: {
            name: dto.name,
            surname: dto.surname,
            email: dto.email,
            genderType: dto.genderType,
            educationType: dto.educationType,
            experienceYear: dto.experienceYear,
            experienceType: dto.experienceType,
            experienceText: dto.experienceText,
            birthDate: dto.birthDate,
            token: sendMail ? token : undefined,
            tokenExpiredAt: sendMail ? addMonths(new Date(), 1) : undefined,
          },
        });
      }
    });
    const url = this.FRONT_URL + `/governance?token=${memberProfile.token}`;
    if (sendMail) {
      const organizationName = (
        await this.prismaService.organization.findFirstOrThrow({
          where: { id: user.organizationId },
        })
      ).name;
      this.mailService
        .sendGovernanceInvite({
          url: url,
          email: memberProfile.email,
          fullName: `${memberProfile.name} ${memberProfile.surname}`,
          organizationName: organizationName,
        })
        .catch((err) => this.logger.error(err));
    }
    return { email: memberProfile.email, url: url };
  }

  async updateMeeting(
    user: UserDto,
    dto: UpdateGovernanceMeetingDto,
  ): Promise<void> {
    await this.prismaService.governanceMeeting.update({
      where: { id: dto.meetingId },
      data: {
        governanceId: dto.governanceId,
        memberProfileIds: dto.memberProfileIds,
        fileIds: dto.fileIds,
        meetingDate: dto.meetingDate,
      },
    });
  }

  async deleteGovernance(
    user: UserDto,
    dto: DeleteGovernanceDto,
    governance: Governance,
  ) {
    const reportGovernances = await this.prismaService.governance.findMany({
      where: { reportId: governance.reportId },
    });
    const deletedIds = [governance.id];
    const iteratorSize = new Set(
      reportGovernances
        .filter((g) => g.parentId && !g.isDefault)
        .map((g) => g.parentId),
    ).size;
    let parentIds: number[] = [governance.id];
    for (let i = 0; i < iteratorSize; i++) {
      const subIds = reportGovernances
        .filter((g) => parentIds.includes(g.parentId))
        .map((g) => g.id);
      if (subIds.length == 0) {
        break;
      }
      parentIds = subIds;
      deletedIds.push(...subIds);
    }

    await this.prismaService.$transaction(async (tx) => {
      await tx.governance.deleteMany({
        where: { id: { in: deletedIds } },
      });
      await tx.governanceMember.deleteMany({
        where: { governanceId: { in: deletedIds } },
      });
      await tx.governanceMeeting.deleteMany({
        where: { governanceId: { in: deletedIds } },
      });
    });
  }

  async deleteReport(user: UserDto, dto: DeleteGovernanceReportDto) {
    await this.prismaService.governanceReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteMember(
    user: UserDto,
    dto: DeleteGovernanceMemberDto,
  ): Promise<void> {
    await this.prismaService.governanceMember.delete({
      where: {
        id: dto.memberId,
        governance: { report: { organizationId: user.organizationId } },
      },
    });
  }

  async deleteMeeting(
    user: UserDto,
    dto: DeleteGovernanceMeetingDto,
  ): Promise<void> {
    await this.prismaService.governanceMeeting.delete({
      where: {
        id: dto.meetingId,
        governance: { report: { organizationId: user.organizationId } },
      },
    });
  }
}
