import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteGovernanceReportDto } from '../dtos/governance/delete-governance-report.dto';
import {
  Governance,
  GovernanceMemberProfile,
  StrategyReport,
} from '@prisma/client';
import { GetGovernanceReportDto } from '../dtos/governance/get-governance-report.dto';
import { GetGovernanceReportUserDto } from '../dtos/governance/get-governance-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetCurrentGovernanceDto } from '../dtos/governance/get-current-governance.dto';
import { UpdateGovernanceDto } from '../dtos/governance/update-governance.dto';
import { CreateGovernanceReportDto } from '../dtos/governance/create-governance-report.dto';
import { CreateGovernanceDto } from '../dtos/governance/create-governance.dto';
import { CreateGovernanceMemberDto } from '../dtos/governance/create-governance-member.dto';
import { CreateGovernanceMeetingDto } from '../dtos/governance/create-governance-meeting.dto';
import { UpdateGovernanceMemberDto } from '../dtos/governance/update-governance-member.dto';
import { DeleteGovernanceMemberDto } from '../dtos/governance/delete-governance-member.dto';
import { DeleteGovernanceMeetingDto } from '../dtos/governance/delete-governance-meeting.dto';
import { UpdateGovernanceMeetingDto } from '../dtos/governance/update-governance-meeting.dto';
import { CreateGovernanceMemberProfileDto } from '../dtos/governance/create-governance-member-profile.dto';
import { GovernanceExperienceTypeEnum } from '../enums/governance-experience-type.enum';
import { DeleteGovernanceDto } from '../dtos/governance/delete-governance.dto';
import { UserService } from '../../common/services/user.service';
import { differenceInSeconds } from 'date-fns';
import { CreateGovernanceMemberProfileCodeDto } from '../dtos/governance/create-governance-member-profile-code.dto';

@Injectable()
export class GovernanceValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private userService: UserService,
  ) {}

  async getCurrentGovernances(
    user: UserDto,
    dto: GetCurrentGovernanceDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetGovernanceReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetGovernanceReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async createGovernance(
    user: UserDto,
    dto: CreateGovernanceDto,
  ): Promise<void> {
    await this.prismaService.governanceReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    if (dto.parentId && dto.subId) {
      throw new BadRequestException('Üst ve alt ilişki aynı anda eklenemez.');
    }
    if (!dto.parentId && !dto.subId) {
      throw new BadRequestException('Üst veya alt ilişki eklemelisiniz.');
    }
    if (dto.parentId) {
      await this.prismaService.governance.findFirstOrThrow({
        where: {
          id: dto.parentId,
          report: { id: dto.reportId, organizationId: user.organizationId },
        },
      });
      const maxGovernance = await this.prismaService.governance.aggregate({
        _max: { order: true },
        where: {
          reportId: dto.reportId,
          parentId: dto.parentId,
        },
      });
      if (
        maxGovernance._max.order &&
        dto.order > maxGovernance._max.order + 1
      ) {
        throw new BadRequestException('Geçersiz sıra numarası.');
      }
      if (!maxGovernance._max.order) {
        dto.order = 1;
      }
    }
    if (dto.subId) {
      await this.prismaService.governance.findFirstOrThrow({
        where: {
          id: dto.subId,
          report: { id: dto.reportId, organizationId: user.organizationId },
          parentId: null,
          isTop: false,
        },
      });
      dto.order = 1;
    }
  }

  async createReport(
    user: UserDto,
    dto: CreateGovernanceReportDto,
  ): Promise<StrategyReport> {
    const governanceReportCount =
      await this.prismaService.governanceReport.count({
        where: { organizationId: user.organizationId },
      });
    if (governanceReportCount > 0) {
      throw new BadRequestException(
        'Birden fazla yönetişim raporu oluşturulamaz.',
      );
    }
    const strategyReport =
      await this.prismaService.strategyReport.findFirstOrThrow({
        where: {
          id: dto.strategyReportId,
          organizationId: user.organizationId,
          isCompleted: true,
        },
        include: { governanceReports: true },
      });
    if (strategyReport.governanceReports.length > 0) {
      throw new BadRequestException(
        'Bu strateji raporu için zaten bir yönetişim raporu oluşturulmuştur.',
      );
    }
    return strategyReport;
  }

  async createMember(user: UserDto, dto: CreateGovernanceMemberDto) {
    await this.prismaService.governance.findFirstOrThrow({
      where: {
        id: dto.governanceId,
        report: { organizationId: user.organizationId },
      },
    });

    let memberProfile: GovernanceMemberProfile;
    if (dto.memberProfileId) {
      memberProfile =
        await this.prismaService.governanceMemberProfile.findFirstOrThrow({
          where: {
            id: dto.memberProfileId,
            organizationId: user.organizationId,
          },
        });
      const memberExist = await this.prismaService.governanceMember.findFirst({
        where: {
          governanceId: dto.governanceId,
          profileId: dto.memberProfileId,
        },
      });
      if (memberExist) {
        throw new BadRequestException(
          `${memberProfile.email} adresine sahip üye zaten bu kartta mevcut.`,
        );
      }
    } else {
      if (!dto.name || !dto.surname || !dto.email) {
        throw new BadRequestException('Eksik alanları doldurunuz.');
      }
      const memberExist = await this.prismaService.governanceMember.findFirst({
        where: {
          governanceId: dto.governanceId,
          profile: { email: dto.email },
        },
      });
      if (memberExist) {
        throw new BadRequestException(
          `${dto.email} adresine sahip üye zaten bu kartta mevcut.`,
        );
      }
      const existOtherOrg = await this.prismaService.user.findFirst({
        where: {
          email: dto.email,
          userOrganizations: {
            some: { organizationId: { not: user.organizationId } },
          },
        },
      });
      if (existOtherOrg) {
        throw new BadRequestException(
          'İlgili email adresi başka bir şirkette mevcuttur.',
        );
      }
      memberProfile =
        await this.prismaService.governanceMemberProfile.findFirst({
          where: {
            organizationId: user.organizationId,
            email: dto.email,
          },
        });
    }

    if (
      dto.experienceType == GovernanceExperienceTypeEnum.OTHER &&
      !dto.experienceText
    ) {
      throw new BadRequestException('Deneyim açıklaması giriniz.');
    } else if (
      dto.experienceType === undefined ||
      dto.experienceType != GovernanceExperienceTypeEnum.OTHER
    ) {
      dto.experienceText = undefined;
    }

    return memberProfile;
  }

  async createMemberProfile(
    dto: CreateGovernanceMemberProfileDto,
  ): Promise<GovernanceMemberProfile> {
    const memberProfile =
      await this.prismaService.governanceMemberProfile.findFirstOrThrow({
        where: {
          userId: null,
          token: dto.token,
          code: dto.code,
          tokenExpiredAt: { gt: new Date() },
          codeExpiredAt: { gt: new Date() },
        },
      });
    const existOtherOrg = await this.prismaService.user.findFirst({
      where: {
        email: memberProfile.email,
        userOrganizations: {
          some: { organizationId: { not: memberProfile.organizationId } },
        },
      },
    });
    if (existOtherOrg) {
      throw new BadRequestException(
        'İlgili email adresi başka bir şirkette mevcuttur.',
      );
    }
    if (
      dto.experienceType &&
      dto.experienceType == GovernanceExperienceTypeEnum.OTHER &&
      !dto.experienceText
    ) {
      throw new BadRequestException('Deneyim açıklaması giriniz.');
    } else if (
      dto.experienceType &&
      dto.experienceType != GovernanceExperienceTypeEnum.OTHER
    ) {
      dto.experienceText = null;
    } else if (dto.experienceType === undefined) {
      dto.experienceText = undefined;
    }
    return memberProfile;
  }

  async createMemberProfileCode(dto: CreateGovernanceMemberProfileCodeDto) {
    const memberProfile =
      await this.prismaService.governanceMemberProfile.findFirstOrThrow({
        where: {
          userId: null,
          token: dto.token,
          tokenExpiredAt: { gt: new Date() },
        },
      });
    if (memberProfile.codeExpiredAt) {
      const diffSeconds = differenceInSeconds(
        memberProfile.codeExpiredAt,
        new Date(),
      );
      if (diffSeconds > 0) {
        throw new BadRequestException(
          `Maile gelen kodu kullanınız. ${diffSeconds} saniye sonra tekrar kod üretebilirsiniz.`,
        );
      }
    }
    return memberProfile;
  }

  async createMeeting(
    user: UserDto,
    dto: CreateGovernanceMeetingDto,
  ): Promise<void> {
    const governance = await this.prismaService.governance.findFirstOrThrow({
      where: {
        id: dto.governanceId,
        report: { organizationId: user.organizationId },
      },
    });
    const memberProfileCount =
      await this.prismaService.governanceMemberProfile.count({
        where: {
          id: { in: dto.memberProfileIds },
          userId: { not: null },
          members: { some: { governance: { reportId: governance.reportId } } },
        },
      });
    if (memberProfileCount != dto.memberProfileIds.length) {
      throw new BadRequestException('Geçersiz üyeler.');
    }
    if (dto.fileIds?.length > 0) {
      const fileCount = await this.prismaService.fileManager.count({
        where: {
          id: { in: dto.fileIds },
          organizationId: user.organizationId,
        },
      });
      if (fileCount != dto.fileIds.length) {
        throw new BadRequestException('Geçersiz dosyalar.');
      }
    }
  }

  async updateGovernance(
    user: UserDto,
    dto: UpdateGovernanceDto,
  ): Promise<Governance> {
    const governance = await this.prismaService.governance.findFirstOrThrow({
      where: {
        id: dto.governanceId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (dto.parentId) {
      await this.prismaService.governance.findFirstOrThrow({
        where: {
          id: dto.parentId,
          reportId: governance.reportId,
        },
      });
    }
    if (dto.priorityIssues?.length > 0) {
      const strategyDetails = await this.prismaService.strategyDetail.findMany({
        where: { strategy: { reportId: governance.report.strategyReportId } },
      });
      const detailPriorityIssues = [
        ...new Set(strategyDetails.map((d) => d.priorityIssue)),
      ];
      const invalidPriorityIssue = dto.priorityIssues.some(
        (p) => !detailPriorityIssues.includes(p),
      );
      if (invalidPriorityIssue) {
        throw new BadRequestException('Geçersiz öncelikli konu.');
      }
    }
    if (dto.order) {
      const maxGovernance = await this.prismaService.governance.aggregate({
        _max: { order: true },
        where: {
          reportId: governance.reportId,
          parentId: governance.parentId,
        },
      });
      if (maxGovernance._max.order && dto.order > maxGovernance._max.order) {
        throw new BadRequestException('Geçersiz sıra numarası.');
      }
      if (!maxGovernance._max.order) {
        dto.order = 1;
      }
    }
    return governance;
  }

  async updateMember(
    user: UserDto,
    dto: UpdateGovernanceMemberDto,
  ): Promise<Governance> {
    const member = await this.prismaService.governanceMember.findFirstOrThrow({
      where: {
        id: dto.memberId,
        governance: { report: { organizationId: user.organizationId } },
      },
      include: { governance: true, profile: true },
    });
    if (dto.email && dto.email != member.profile.email) {
      const memberExist = await this.prismaService.governanceMember.findFirst({
        where: {
          id: { not: member.id },
          profile: { organizationId: user.organizationId, email: dto.email },
        },
      });
      if (memberExist) {
        throw new BadRequestException(
          `${dto.email} adresine sahip üye zaten şirketinizde mevcut.`,
        );
      }
    }
    if (dto.governanceId && dto.governanceId != member.governanceId) {
      await this.prismaService.governance.findFirstOrThrow({
        where: { id: dto.governanceId, reportId: member.governance.reportId },
      });
    }
    if (dto.isLead === false) {
      throw new BadRequestException('Başkanlık durumu kaldırılamaz.');
    }
    if (member.profile.userId) {
      if (
        dto.name ||
        dto.surname ||
        dto.email ||
        dto.genderType ||
        dto.educationType ||
        dto.experienceYear ||
        dto.experienceType ||
        dto.experienceText ||
        dto.birthDate
      ) {
        throw new BadRequestException(
          'Kullanıcı profilini oluşturduğu için bu alanlar güncellenemez.',
        );
      }
      if (dto.sendMail) {
        throw new BadRequestException(
          'Kullanıcı profilini oluşturduğu için davet edilemez.',
        );
      }
    }
    if (
      dto.experienceType &&
      dto.experienceType == GovernanceExperienceTypeEnum.OTHER &&
      !dto.experienceText
    ) {
      throw new BadRequestException('Deneyim açıklaması giriniz.');
    } else if (
      dto.experienceType &&
      dto.experienceType != GovernanceExperienceTypeEnum.OTHER
    ) {
      dto.experienceText = null;
    } else if (dto.experienceType === undefined) {
      dto.experienceText = undefined;
    }
    return member.governance;
  }

  async updateMeeting(
    user: UserDto,
    dto: UpdateGovernanceMeetingDto,
  ): Promise<void> {
    const meeting = await this.prismaService.governanceMeeting.findFirstOrThrow(
      {
        where: {
          id: dto.meetingId,
          governance: { report: { organizationId: user.organizationId } },
        },
        include: { governance: true },
      },
    );
    if (dto.governanceId && dto.governanceId != meeting.governanceId) {
      await this.prismaService.governance.findFirstOrThrow({
        where: {
          id: dto.governanceId,
          reportId: meeting.governance.reportId,
        },
      });
    }
    if (dto.memberProfileIds?.length > 0) {
      const memberProfileCount =
        await this.prismaService.governanceMemberProfile.count({
          where: {
            id: { in: dto.memberProfileIds },
            userId: { not: null },
            members: {
              some: { governance: { reportId: meeting.governance.reportId } },
            },
          },
        });
      if (memberProfileCount != dto.memberProfileIds.length) {
        throw new BadRequestException('Geçersiz üyeler.');
      }
    }
    if (dto.fileIds?.length > 0) {
      const fileCount = await this.prismaService.fileManager.count({
        where: {
          id: { in: dto.fileIds },
          organizationId: user.organizationId,
        },
      });
      if (fileCount != dto.fileIds.length) {
        throw new BadRequestException('Geçersiz dosyalar.');
      }
    }
  }

  async deleteGovernance(
    user: UserDto,
    dto: DeleteGovernanceDto,
  ): Promise<Governance> {
    const governance = await this.prismaService.governance.findFirstOrThrow({
      where: {
        id: dto.governanceId,
        report: { organizationId: user.organizationId },
        isDefault: false,
      },
    });
    await this.userService.checkPassword(user, dto.password);
    return governance;
  }

  async deleteReport(
    user: UserDto,
    dto: DeleteGovernanceReportDto,
  ): Promise<void> {
    const report = await this.prismaService.governanceReport.findFirstOrThrow({
      select: { _count: { select: { metricReports: true } } },
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    if (report._count.metricReports > 0) {
      throw new BadRequestException(
        'Bu rapor, Metrikler ve Hedefler modülünde kullanıldığı için silinemez.',
      );
    }
  }

  async deleteMember(
    user: UserDto,
    dto: DeleteGovernanceMemberDto,
  ): Promise<void> {
    await this.prismaService.governanceMember.findFirstOrThrow({
      where: {
        id: dto.memberId,
        governance: { report: { organizationId: user.organizationId } },
      },
    });
  }

  async deleteMeeting(
    user: UserDto,
    dto: DeleteGovernanceMeetingDto,
  ): Promise<void> {
    await this.prismaService.governanceMeeting.findFirstOrThrow({
      where: {
        id: dto.meetingId,
        governance: { report: { organizationId: user.organizationId } },
      },
    });
  }
}
