import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteStrategyReportDto } from '../dtos/strategy/delete-strategy-report.dto';
import { UpdateStrategyReportDto } from '../dtos/strategy/update-strategy-report.dto';
import { CreateStrategyHelpDto } from '../dtos/strategy/create-strategy-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { CreateStrategyHelpAnswerDto } from '../dtos/strategy/create-strategy-help-answer.dto';
import { Strategy, StrategyReport } from '@prisma/client';
import { GetStrategyReportDto } from '../dtos/strategy/get-strategy-report.dto';
import { GetStrategyReportUserDto } from '../dtos/strategy/get-strategy-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetStrategyHelpDetailDto } from '../dtos/strategy/get-strategy-help-detail.dto';
import { GetCurrentStrategyDto } from '../dtos/strategy/get-current-strategy.dto';
import { UserService } from '../../common/services/user.service';
import { StrategyHelpTypeEnum } from '../enums/strategy-help-type.enum';
import { DeleteStrategyHelpDto } from '../dtos/strategy/delete-strategy-help.dto';
import {
  StrategyTermDto,
  UpdateStrategyTermDto,
} from '../dtos/strategy/update-strategy-term.dto';
import { UpdateStrategyDto } from '../dtos/strategy/update-strategy.dto';
import { UpdateStrategyDetailCombineDto } from '../dtos/strategy/update-strategy-detail-combine.dto';
import { UpdateStrategyDetailSeparateDto } from '../dtos/strategy/update-strategy-detail-separate.dto';
import { StrategyTermTypeEnum } from '../enums/strategy-term-type.enum';

@Injectable()
export class StrategyValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private userService: UserService,
  ) {}

  private reportRoadMapCheck(report: StrategyReport): void {
    if (report.isRoadMap) {
      throw new BadRequestException(
        'Strateji raporunun yol haritası oluşturulduğu için işlem yapılamaz.',
      );
    }
  }

  private reportCompletedCheck(report: StrategyReport): void {
    if (report.isCompleted) {
      throw new BadRequestException(
        'Strateji raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async getCurrentStrategies(
    user: UserDto,
    dto: GetCurrentStrategyDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetStrategyReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetStrategyReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetStrategyHelpDetailDto,
  ): Promise<void> {
    const helpCount = await this.prismaService.strategyHelp.count({
      where: {
        helpedUserId: user.id,
        strategy: {
          report: { id: dto.reportId, organizationId: user.organizationId },
        },
        status: HelpStatusEnum.PENDING,
      },
    });
    if (helpCount == 0) {
      throw new BadRequestException(
        'Bu rapor için bekleyen atamanız bulunmamaktadır.',
      );
    }
  }

  async createHelp(user: UserDto, dto: CreateStrategyHelpDto): Promise<void> {
    if (user.id == dto.helpedUserId) {
      throw new BadRequestException('Kendinizi ekleyemezsiniz.');
    }
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: { userId: dto.helpedUserId, organizationId: user.organizationId },
    });
    const strategy = await this.prismaService.strategy.findFirstOrThrow({
      where: {
        id: dto.strategyId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(strategy.report);
    if (dto.helpType == StrategyHelpTypeEnum.STRATEGY_FOCUS_AREA) {
      this.reportRoadMapCheck(strategy.report);
    } else if (dto.helpType == StrategyHelpTypeEnum.DESCRIPTION_AND_TERMS) {
      if (!strategy.report.isRoadMap) {
        throw new BadRequestException(
          'Yol haritası oluşturulmadan bu istek yapılamaz.',
        );
      }
    }
    const help = await this.prismaService.strategyHelp.findFirst({
      where: {
        helpedUserId: dto.helpedUserId,
        strategyId: dto.strategyId,
        status: HelpStatusEnum.PENDING,
        helpType: dto.helpType,
      },
    });
    if (help) {
      throw new BadRequestException(
        `Bu kullanıcıya zaten şu an aktif bir atama var.`,
      );
    }
  }

  async createHelpAnswer(
    user: UserDto,
    dto: CreateStrategyHelpAnswerDto,
  ): Promise<void> {
    const report = await this.prismaService.strategyReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
    const strategyIds = dto.answers.map((a) => a.strategyId);
    const helpCount = await this.prismaService.strategyHelp.count({
      where: {
        helpedUserId: user.id,
        strategy: {
          id: { in: strategyIds },
          reportId: dto.reportId,
        },
        status: HelpStatusEnum.PENDING,
      },
    });
    if (helpCount == 0) {
      throw new BadRequestException(
        'Bu rapor için bekleyen atamanız bulunmamaktadır.',
      );
    }
    if (
      strategyIds.length != new Set(strategyIds).size ||
      strategyIds.length != helpCount
    ) {
      throw new BadRequestException('Strateji verileri eşleşmiyor.');
    }
    for (const a of dto.answers) {
      if (report.isRoadMap) {
        if (a.strategyFocusArea) {
          throw new BadRequestException(
            'Yol haritasında strateji odak alanı girilemez.',
          );
        }
        if (!a.description || !a.shortTerm || !a.mediumTerm || !a.longTerm) {
          throw new BadRequestException('Açıklama ve dönemleri girmelisiniz.');
        }
      } else {
        if (!a.strategyFocusArea) {
          throw new BadRequestException('Strateji odak alanı girmelisiniz.');
        }
        if (a.description || a.shortTerm || a.mediumTerm || a.longTerm) {
          throw new BadRequestException(
            'Yol haritası oluşmadan açıklama ve dönemler girilemez.',
          );
        }
      }
    }
  }

  async updateStrategy(user: UserDto, dto: UpdateStrategyDto): Promise<void> {
    const strategy = await this.prismaService.strategy.findFirstOrThrow({
      where: {
        id: dto.strategyId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    this.reportCompletedCheck(strategy.report);
    if (strategy.report.isRoadMap) {
      if (dto.strategyFocusArea) {
        throw new BadRequestException('Yol haritasında bu alan girilemez.');
      }
    } else {
      if (dto.description || dto.shortTerm || dto.mediumTerm || dto.longTerm) {
        throw new BadRequestException(
          'Yol haritası oluşmadan bu alanlar girilemez.',
        );
      }
    }
  }

  async updateReport(
    user: UserDto,
    dto: UpdateStrategyReportDto,
  ): Promise<void> {
    const report = await this.prismaService.strategyReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { strategies: true },
    });
    this.reportCompletedCheck(report);
    if (dto.isRoadMap !== undefined) {
      this.reportRoadMapCheck(report);
    }
    if (dto.isRoadMap) {
      if (dto.password) {
        await this.userService.checkPassword(user, dto.password);
      } else {
        throw new BadRequestException('Şifre girilmesi zorunludur.');
      }
      const uncompletedStrategies = report.strategies.filter(
        (s) => !s.strategyFocusArea,
      );
      if (uncompletedStrategies.length > 0) {
        throw new BadRequestException(
          'Strateji raporunun yol haritası oluşturulabilmesi için tüm eksik veriler girilmelidir.',
        );
      }
    }
    if (dto.isCompleted) {
      if (!report.isRoadMap) {
        throw new BadRequestException(
          'Strateji raporunun yol haritası oluşturulmadığı için rapor tamamlanamaz.',
        );
      }
      if (dto.password) {
        await this.userService.checkPassword(user, dto.password);
      } else {
        throw new BadRequestException('Şifre girilmesi zorunludur.');
      }
      const uncompletedStrategies = report.strategies.filter(
        (s) =>
          !s.strategyFocusArea ||
          !s.description ||
          !s.shortTerm ||
          !s.mediumTerm ||
          !s.longTerm,
      );
      if (uncompletedStrategies.length > 0) {
        throw new BadRequestException(
          'Strateji raporunun tamamlanabilmesi için tüm eksik veriler girilmelidir.',
        );
      }
    }
  }

  async updateDetailsCombine(
    user: UserDto,
    dto: UpdateStrategyDetailCombineDto,
  ): Promise<void> {
    const strategies = await this.prismaService.strategy.findMany({
      where: {
        id: { in: dto.strategyIds },
        report: { organizationId: user.organizationId, isRoadMap: false },
      },
    });
    const reportSize = new Set(strategies.map((s) => s.reportId)).size;
    if (reportSize != 1) {
      throw new BadRequestException('Geçersiz rapor verisi.');
    }
    if (strategies.length != dto.strategyIds.length) {
      throw new BadRequestException('Geçersiz stratejiler.');
    }
  }

  async updateDetailsSeparate(
    user: UserDto,
    dto: UpdateStrategyDetailSeparateDto,
  ): Promise<Strategy> {
    const detailIds = [...new Set(dto.details.map((d) => d.detailId))];
    const strategy = await this.prismaService.strategy.findFirstOrThrow({
      where: {
        id: dto.strategyId,
        report: { organizationId: user.organizationId, isRoadMap: false },
      },
      include: { details: true },
    });
    if (
      detailIds.length != dto.details.length ||
      detailIds.length != strategy.details.length
    ) {
      throw new BadRequestException('Geçersiz strateji detay verileri.');
    }
    for (const detail of strategy.details) {
      if (!detailIds.includes(detail.id)) {
        throw new BadRequestException('Eksik strateji detay verileri.');
      }
    }
    return strategy;
  }

  async updateTerms(user: UserDto, dto: UpdateStrategyTermDto): Promise<void> {
    await this.prismaService.strategyReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
        isRoadMap: true,
      },
    });
    const termTypes = dto.terms.map((t) => t.type);
    if (termTypes.length != new Set(termTypes).size) {
      throw new BadRequestException(
        'Aynı tip vadeden birden fazla giremezsiniz.',
      );
    }
    let short: StrategyTermDto;
    let medium: StrategyTermDto;
    let long: StrategyTermDto;
    for (const t of dto.terms) {
      if (t.minValue > t.maxValue) {
        throw new BadRequestException('Geçersiz vade aralığı.');
      }
      if (t.type == StrategyTermTypeEnum.SHORT) {
        short = t;
      } else if (t.type == StrategyTermTypeEnum.MEDIUM) {
        medium = t;
      } else if (t.type == StrategyTermTypeEnum.LONG) {
        long = t;
      }
    }
    if (short && medium) {
      if (short.maxValue > medium.minValue) {
        throw new BadRequestException('Geçersiz kısa ve orta vade aralığı.');
      }
    }
    if (short && long) {
      if (short.maxValue > long.minValue) {
        throw new BadRequestException('Geçersiz kısa ve uzun vade aralığı.');
      }
    }
    if (medium && long) {
      if (medium.maxValue > long.minValue) {
        throw new BadRequestException('Geçersiz orta ve uzun vade aralığı.');
      }
    }
  }

  async deleteReport(
    user: UserDto,
    dto: DeleteStrategyReportDto,
  ): Promise<void> {
    const report = await this.prismaService.strategyReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    this.reportCompletedCheck(report);
  }

  async deleteHelp(user: UserDto, dto: DeleteStrategyHelpDto): Promise<void> {
    const help = await this.prismaService.strategyHelp.findFirst({
      where: {
        id: dto.helpId,
        strategy: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: { strategy: { include: { report: true } } },
    });
    if (!help) {
      throw new BadRequestException('Bu atamayı silemezsiniz.');
    }
    this.reportCompletedCheck(help.strategy.report);
  }
}
