import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteMetricReportDto } from '../dtos/metric/delete-metric-report.dto';
import { UpdateMetricReportDto } from '../dtos/metric/update-metric-report.dto';
import { CreateMetricHelpDto } from '../dtos/metric/create-metric-help.dto';
import { GetMetricReportDto } from '../dtos/metric/get-metric-report.dto';
import { GetMetricReportUserDto } from '../dtos/metric/get-metric-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetMetricHelpDetailDto } from '../dtos/metric/get-metric-help-detail.dto';
import { GetCurrentMetricDto } from '../dtos/metric/get-current-metric.dto';
import { DeleteMetricHelpDto } from '../dtos/metric/delete-metric-help.dto';
import { UpdateMetricDto } from '../dtos/metric/update-metric.dto';
import {
  MetricDefault,
  MetricSupervisorProfile,
  PrismaClient,
  User,
} from '@prisma/client';
import { CreateMetricReportDto } from '../dtos/metric/create-metric-report.dto';
import { MetricCategoryTypeEnum } from '../enums/metric-category-type.enum';
import { CreateMetricDto } from '../dtos/metric/create-metric.dto';
import { DeleteMetricDto } from '../dtos/metric/delete-metric.dto';
import { GetDefaultMetricDto } from '../dtos/metric/get-default-metric.dto';
import { CreateMetricSupervisorDto } from '../dtos/metric/create-metric-supervisor.dto';
import { addMinutes, differenceInSeconds } from 'date-fns';
import { GetMetricSupervisorDetailDto } from '../dtos/metric/get-metric-supervisor-detail.dto';
import { GetMetricActivityDto } from '../dtos/metric/get-metric-activity.dto';
import { UpdateMetricSupervisorDto } from '../dtos/metric/update-metric-supervisor.dto';
import { ApiClientService } from '../../common/services/api-client.service';
import { UtilService } from '../../common/services/util.service';
import { GetMetricChartDetailDto } from '../dtos/metric/get-metric-chart-detail.dto';
import { ChartFilterKeyEnum } from '../../admin/chart/enums/chart.enum';
import { ITXClientDenyList } from '@prisma/client/runtime/library';

@Injectable()
export class MetricValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
    private apiClientService: ApiClientService,
    private utilService: UtilService,
  ) {}

  async getChartsDetail(user: UserDto, dto: GetMetricChartDetailDto) {
    const template = await this.prismaService.chartTemplate.findFirstOrThrow({
      where: { id: dto.templateId },
    });

    let yearFilter = false;
    let filterValues: number[] = [];
    let metricIds: number[] = [];
    if (dto.filterValues) {
      filterValues = dto.filterValues
        .split(',')
        .map(Number)
        .filter((id) => id);
      if (
        filterValues.length == 0 ||
        filterValues.length != [...new Set(filterValues)].length
      ) {
        throw new BadRequestException('Geçersiz filtre verileri.');
      }

      if (template.filterKey === ChartFilterKeyEnum.FACILITY) {
        metricIds = filterValues;
      } else if (template.filterKey === ChartFilterKeyEnum.YEAR) {
        yearFilter = true;
      }
    }

    if (metricIds.length == 0) {
      metricIds = dto.metricIds
        .split(',')
        .map(Number)
        .filter((id) => id);
    }
    if (
      metricIds.length == 0 ||
      metricIds.length != [...new Set(metricIds)].length
    ) {
      throw new BadRequestException('Geçersiz metrik verileri.');
    }
    const metrics = await this.prismaService.metric.findMany({
      where: {
        id: { in: metricIds },
        report: { organizationId: user.organizationId },
        performances: yearFilter
          ? { some: { year: { in: filterValues } } }
          : undefined,
      },
      include: {
        performances: {
          where: { year: yearFilter ? { in: filterValues } : undefined },
          orderBy: { year: 'asc' },
        },
      },
    });
    const reportIds = [...new Set(metrics.map((m) => m.reportId))];
    if (reportIds.length > 1) {
      throw new BadRequestException('Farklı raporlardaki veriler getirilemez.');
    }

    return { template: template, metrics: metrics };
  }

  async getCurrentMetrics(
    user: UserDto,
    dto: GetCurrentMetricDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getDefaultMetrics(user: UserDto, dto: GetDefaultMetricDto) {
    const metricReport = await this.prismaService.metricReport.findFirstOrThrow(
      {
        where: { id: dto.reportId, organizationId: user.organizationId },
        include: { governanceReport: true },
      },
    );
    return metricReport.governanceReport;
  }

  async getActivities(user: UserDto, dto: GetMetricActivityDto): Promise<void> {
    await this.prismaService.metric.findFirstOrThrow({
      where: {
        id: dto.metricId,
        report: { organizationId: user.organizationId },
      },
    });
  }

  async getReports(user: UserDto, dto: GetMetricReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetMetricReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getHelpDetail(
    user: UserDto,
    dto: GetMetricHelpDetailDto,
  ): Promise<void> {
    const helpCount = await this.prismaService.metricHelp.count({
      where: {
        helpedUserId: user.id,
        metric: {
          report: { id: dto.reportId, organizationId: user.organizationId },
        },
      },
    });
    if (helpCount == 0) {
      throw new BadRequestException(
        'Bu rapor için bekleyen atamanız bulunmamaktadır.',
      );
    }
  }

  async getSupervisorDetail(
    user: UserDto,
    dto: GetMetricSupervisorDetailDto,
  ): Promise<void> {
    const supervisorCount = await this.prismaService.metricSupervisor.count({
      where: {
        profile: { userId: user.id },
        metric: {
          report: { id: dto.reportId, organizationId: user.organizationId },
        },
      },
    });
    if (supervisorCount == 0) {
      throw new BadRequestException(
        'Bu rapor için bekleyen veri sorumlusu atamanız bulunmamaktadır.',
      );
    }
  }

  async createMetric(
    user: UserDto,
    dto: CreateMetricDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ) {
    const dbService = options?.tx ?? this.prismaService;

    if (!dto.reportId) {
      dto.reportId = (
        await dbService.metricReport.findFirstOrThrow({
          where: { id: dto.reportId, organizationId: user.organizationId },
        })
      ).id;
    }
    const report = await dbService.metricReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { governanceReport: true },
    });

    if (dto.strategyDetailId) {
      await dbService.strategyDetail.findFirstOrThrow({
        where: {
          id: dto.strategyDetailId,
          strategy: { reportId: report.governanceReport.strategyReportId },
        },
      });
    } else if (dto.isActivity && dto.metricDefaultId) {
      const userSector = await dbService.userOrganization.findFirst({
        select: {
          organization: {
            select: {
              sectors: {
                select: {
                  sector: {
                    select: { metricDefaults: { select: { id: true } } },
                  },
                },
              },
            },
          },
        },
        where: {
          userId: user.id,
          organizationId: report.governanceReport.organizationId,
        },
      });
      if (!userSector) {
        throw new BadRequestException(
          'Kullanıcının şirketi bulunamadı veya kullanıcının bulunduğu sektörde metrik bulunamadı.',
        );
      }
      const isValidMetricDefault = userSector.organization.sectors.flatMap(
        (sector) =>
          sector.sector.metricDefaults.some(
            (md) => md.id === dto.metricDefaultId,
          ),
      );
      if (!isValidMetricDefault) {
        throw new BadRequestException(
          'Kullanıcının bulunduğu sektöre ait metrik bulunamadı.',
        );
      }
    }

    const facilityIds = (await this.apiClientService.getFacilities(user)).map(
      (f) => f.id,
    );
    const invalidFacility = dto.facilityIds.some(
      (id) => !facilityIds.includes(id),
    );
    if (invalidFacility) {
      throw new BadRequestException('Geçersiz tesis verisi.');
    }

    let metricDefault: MetricDefault;
    if (dto.metricDefaultId) {
      metricDefault = await dbService.metricDefault.findFirstOrThrow({
        where: { id: dto.metricDefaultId },
      });
      const defaultFacilityIds = (
        await dbService.metric.findMany({
          where: {
            reportId: dto.reportId,
            defaultId: dto.metricDefaultId,
            facilityIds: { not: null },
          },
        })
      )
        .map((m) => this.utilService.jsonToNumberList(m.facilityIds))
        .flat(1);
      if (defaultFacilityIds.length > 0) {
        const invalidFacility = dto.facilityIds.some((id) =>
          defaultFacilityIds.includes(id),
        );
        if (invalidFacility) {
          throw new BadRequestException(
            'Bu metrik için tesis zaten kullanılıyor.',
          );
        }
      }
    } else {
      if (!dto.metricName || !dto.categoryType) {
        throw new BadRequestException('Metrik adı ve kategori girilmelidir.');
      }
      if (dto.categoryType == MetricCategoryTypeEnum.QUANTITATIVE) {
        if (!dto.unitType) {
          throw new BadRequestException('Birim girilmelidir.');
        }
      } else if (dto.categoryType == MetricCategoryTypeEnum.ARGUMENT) {
        dto.unitType = undefined;
      }
    }
    return metricDefault;
  }

  async createReport(user: UserDto, dto: CreateMetricReportDto) {
    const strategyReportCount = await this.prismaService.strategyReport.count({
      where: { organizationId: user.organizationId },
    });
    if (strategyReportCount > 0) {
      throw new BadRequestException(
        'Birden fazla metrik raporu oluşturulamaz.',
      );
    }
    const governanceReport =
      await this.prismaService.governanceReport.findFirstOrThrow({
        where: {
          id: dto.governanceReportId,
          organizationId: user.organizationId,
        },
      });
    return governanceReport;
  }

  async createHelp(user: UserDto, dto: CreateMetricHelpDto): Promise<void> {
    await this.prismaService.metric.findFirstOrThrow({
      where: {
        id: dto.metricId,
        report: { organizationId: user.organizationId },
      },
    });
    if (user.id == dto.helpedUserId) {
      throw new BadRequestException('Kendinizi ekleyemezsiniz.');
    }
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: {
        userId: dto.helpedUserId,
        organizationId: user.organizationId,
      },
    });
    const help = await this.prismaService.metricHelp.findFirst({
      where: {
        helpedUserId: dto.helpedUserId,
        metricId: dto.metricId,
      },
    });
    if (help) {
      throw new BadRequestException(
        `Bu kullanıcıya zaten şu an aktif bir atama var.`,
      );
    }
  }

  async createSupervisor(user: UserDto, dto: CreateMetricSupervisorDto) {
    await this.prismaService.metric.findFirstOrThrow({
      where: {
        id: dto.metricId,
        report: { organizationId: user.organizationId },
      },
    });

    let supervisorUser: User;
    let supervisorProfile: MetricSupervisorProfile;
    if (dto.profileId) {
      const profile =
        await this.prismaService.metricSupervisorProfile.findFirstOrThrow({
          where: {
            id: dto.profileId,
            organizationId: user.organizationId,
          },
          include: { user: true },
        });
      supervisorUser = profile.user;
      supervisorProfile = profile;
    } else {
      if (!dto.name || !dto.surname || !dto.email || !dto.department) {
        throw new BadRequestException('Eksik alanları doldurunuz.');
      }
      const emailUser = await this.prismaService.user.findFirst({
        where: { email: dto.email },
        include: {
          userOrganizations: {
            where: { organizationId: { not: user.organizationId } },
          },
          metricSupervisorProfiles: {
            where: { organizationId: user.organizationId },
          },
        },
      });
      if (emailUser?.userOrganizations?.length > 0) {
        throw new BadRequestException(
          'İlgili email adresi başka bir şirkette mevcuttur.',
        );
      }
      if (emailUser?.metricSupervisorProfiles?.length > 0) {
        throw new BadRequestException(
          'Bu kullanıcının zaten veri sorumlusu profili oluşturulmuştur.',
        );
      }
      supervisorUser = emailUser;
    }
    return {
      supervisorUser: supervisorUser,
      supervisorProfile: supervisorProfile,
    };
  }

  async updateMetric(user: UserDto, dto: UpdateMetricDto): Promise<void> {
    const metric = await this.prismaService.metric.findFirstOrThrow({
      where: {
        id: dto.metricId,
        report: { organizationId: user.organizationId },
      },
    });
    if (metric.defaultId) {
      if (dto.metricName || dto.categoryType || dto.unitType || dto.note) {
        throw new BadRequestException(
          'Sistem tarafından atanan varsayılan alanlar güncellenemez.',
        );
      }
    }
    if (dto.facilityIds) {
      const facilityIds = (await this.apiClientService.getFacilities(user)).map(
        (f) => f.id,
      );
      const invalidFacility = dto.facilityIds.some(
        (id) => !facilityIds.includes(id),
      );
      if (invalidFacility) {
        throw new BadRequestException('Geçersiz tesis verisi.');
      }
    }
    if (metric.defaultId && dto.facilityIds?.length > 0) {
      const defaultFacilityIds = (
        await this.prismaService.metric.findMany({
          where: {
            id: { not: metric.id },
            reportId: metric.reportId,
            defaultId: metric.defaultId,
            facilityIds: { not: null },
          },
        })
      )
        .map((m) => this.utilService.jsonToNumberList(m.facilityIds))
        .flat(1);
      if (defaultFacilityIds.length > 0) {
        const invalidFacility = dto.facilityIds.some((id) =>
          defaultFacilityIds.includes(id),
        );
        if (invalidFacility) {
          throw new BadRequestException(
            'Bu metrik için tesis zaten kullanılıyor.',
          );
        }
      }
    }
  }

  async updateReport(user: UserDto, dto: UpdateMetricReportDto): Promise<void> {
    await this.prismaService.metricReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async updateSupervisor(user: UserDto, dto: UpdateMetricSupervisorDto) {
    const profile =
      await this.prismaService.metricSupervisorProfile.findFirstOrThrow({
        where: {
          id: dto.profileId,
          organizationId: user.organizationId,
        },
        include: { user: true },
      });
    if (dto.sendMail) {
      if (!profile.user.isOtp) {
        throw new BadRequestException(
          'Kullanıcı şifresini değiştirdiği için yeni şifre oluşturulamaz.',
        );
      }
      const diff = differenceInSeconds(
        addMinutes(profile.user.updatedAt, 1),
        new Date(),
      );
      if (diff > 0) {
        throw new BadRequestException(
          `${diff} saniye sonra tekrar atama yapabilirsiniz.`,
        );
      }
    }
    return profile.user;
  }

  async deleteMetric(user: UserDto, dto: DeleteMetricDto): Promise<void> {
    await this.prismaService.metric.findFirstOrThrow({
      where: {
        id: dto.metricId,
        report: { organizationId: user.organizationId },
        helps: { none: {} },
        supervisors: { none: {} },
      },
    });
  }

  async deleteReport(user: UserDto, dto: DeleteMetricReportDto): Promise<void> {
    await this.prismaService.metricReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteHelp(user: UserDto, dto: DeleteMetricHelpDto): Promise<void> {
    await this.prismaService.metricHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        metric: { report: { organizationId: user.organizationId } },
      },
    });
  }
}
