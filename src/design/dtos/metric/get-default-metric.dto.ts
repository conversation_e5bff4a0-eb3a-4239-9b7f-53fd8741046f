import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetDefaultMetricDto {
  @ApiProperty({ type: Number })
  @Transform(({ value }) => +value)
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  strategyDetailId?: number;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  isActivity?: boolean;
}
