import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetMetricChartDetailDto {
  @ApiProperty({ type: Number })
  @Transform(({ value }) => +value)
  @IsNumber()
  templateId: number;

  @ApiProperty({ type: String })
  @IsString()
  metricIds: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  filterValues?: string;
}
