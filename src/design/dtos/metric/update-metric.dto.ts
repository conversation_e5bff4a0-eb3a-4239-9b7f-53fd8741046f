import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import { MetricCategoryTypeEnum } from '../../enums/metric-category-type.enum';
import { MetricSupervisorPeriodTypeEnum } from '../../enums/metric-supervisor-period-type.enum';

export class UpdateMetricDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  metricId: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  facilityIds?: number[];

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  metricName?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  goalName?: string;

  @ApiProperty({ type: Number, enum: MetricCategoryTypeEnum, required: false })
  @IsEnum(MetricCategoryTypeEnum)
  @IsOptional()
  categoryType?: MetricCategoryTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  unitType?: string;

  @ApiProperty({
    type: Number,
    enum: MetricSupervisorPeriodTypeEnum,
    required: false,
  })
  @IsEnum(MetricSupervisorPeriodTypeEnum)
  @IsOptional()
  periodType?: MetricSupervisorPeriodTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  note?: string;
}
