import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import { MetricCategoryTypeEnum } from '../../enums/metric-category-type.enum';
import { MetricSupervisorPeriodTypeEnum } from '../../enums/metric-supervisor-period-type.enum';

export class CreateMetricDto {
  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  reportId?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  strategyDetailId?: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isActivity?: boolean;

  @ApiProperty({ type: Number, isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(1)
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  facilityIds: number[];

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  metricDefaultId?: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  metricName?: string;

  @ApiProperty({ type: Number, enum: MetricCategoryTypeEnum, required: false })
  @IsEnum(MetricCategoryTypeEnum)
  @IsOptional()
  categoryType?: MetricCategoryTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  unitType?: string;

  @ApiProperty({
    type: Number,
    enum: MetricSupervisorPeriodTypeEnum,
    required: false,
  })
  @IsEnum(MetricSupervisorPeriodTypeEnum)
  @IsOptional()
  periodType?: MetricSupervisorPeriodTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  note?: string;
}
