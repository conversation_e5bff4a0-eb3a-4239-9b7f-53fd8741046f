import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class CreateMetricSupervisorDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  metricId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  profileId?: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  surname?: string;

  @ApiProperty({ type: String, required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  department?: string;
}
