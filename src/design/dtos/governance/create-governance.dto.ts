import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, MinLength } from 'class-validator';

export class CreateGovernanceDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  parentId?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  subId?: number;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ type: Number })
  @IsNumber()
  order: number;
}
