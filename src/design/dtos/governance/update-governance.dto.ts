import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';
import { GovernanceMeetingFrequencyTypeEnum } from '../../enums/governance-meeting-frequency-type.enum';

export class UpdateGovernanceDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  governanceId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  parentId?: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;

  @ApiProperty({
    type: Number,
    enum: GovernanceMeetingFrequencyTypeEnum,
    required: false,
  })
  @IsEnum(GovernanceMeetingFrequencyTypeEnum)
  @IsOptional()
  meetingFrequencyType?: GovernanceMeetingFrequencyTypeEnum;

  @ApiProperty({ type: String, isArray: true, required: false })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsString({ each: true })
  @IsOptional()
  priorityIssues?: string[];

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  order?: number;
}
