import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsN<PERSON>ber,
  IsO<PERSON>al,
  <PERSON>String,
  <PERSON><PERSON>ength,
  <PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { GovernanceGenderTypeEnum } from '../../enums/governance-gender-type.enum';
import { GovernanceEducationTypeEnum } from '../../enums/governance-education-type.enum';
import { GovernanceExperienceTypeEnum } from '../../enums/governance-experience-type.enum';
import { Transform } from 'class-transformer';

export class CreateGovernanceMemberProfileDto {
  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  token: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  code: string;

  @ApiProperty({ type: String })
  @IsString()
  name: string;

  @ApiProperty({ type: String })
  @IsString()
  surname: string;

  @ApiProperty({ type: Number, enum: GovernanceGenderTypeEnum })
  @IsEnum(GovernanceGenderTypeEnum)
  genderType: GovernanceGenderTypeEnum;

  @ApiProperty({ type: Number, enum: GovernanceEducationTypeEnum })
  @IsEnum(GovernanceEducationTypeEnum)
  educationType: GovernanceEducationTypeEnum;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(0)
  experienceYear: number;

  @ApiProperty({ type: Number, enum: GovernanceExperienceTypeEnum })
  @IsEnum(GovernanceExperienceTypeEnum)
  experienceType: GovernanceExperienceTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  experienceText?: string;

  @ApiProperty({
    type: String,
    example: new Date().toISOString().split('T')[0],
  })
  @Transform(({ value }) => new Date(value))
  @IsDate()
  birthDate: Date;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(6)
  @MaxLength(32)
  password: string;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  personalData: boolean;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  kvkk: boolean;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  userAgreement: boolean;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  privacyPolicy: boolean;
}
