import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsDate,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateGovernanceMeetingDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  governanceId: number;

  @ApiProperty({ type: Number, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  memberProfileIds: number[];

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  fileIds?: number[];

  @ApiProperty({
    type: String,
    example: new Date().toISOString().split('.')[0],
  })
  @Transform(({ value }) => new Date(value))
  @IsDate()
  meetingDate: Date;
}
