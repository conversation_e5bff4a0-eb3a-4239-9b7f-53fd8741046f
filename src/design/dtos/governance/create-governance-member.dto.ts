import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  MinLength,
} from 'class-validator';
import { GovernanceGenderTypeEnum } from '../../enums/governance-gender-type.enum';
import { GovernanceEducationTypeEnum } from '../../enums/governance-education-type.enum';
import { GovernanceExperienceTypeEnum } from '../../enums/governance-experience-type.enum';
import { Transform } from 'class-transformer';

export class CreateGovernanceMemberDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  governanceId: number;

  @ApiProperty({ type: String })
  @IsString()
  department: string;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  memberProfileId?: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isLead?: boolean;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  surname?: string;

  @ApiProperty({ type: String, required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    type: Number,
    enum: GovernanceGenderTypeEnum,
    required: false,
  })
  @IsEnum(GovernanceGenderTypeEnum)
  @IsOptional()
  genderType?: GovernanceGenderTypeEnum;

  @ApiProperty({
    type: Number,
    enum: GovernanceEducationTypeEnum,
    required: false,
  })
  @IsEnum(GovernanceEducationTypeEnum)
  @IsOptional()
  educationType?: GovernanceEducationTypeEnum;

  @ApiProperty({
    type: Number,
    required: false,
  })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(0)
  @IsOptional()
  experienceYear?: number;

  @ApiProperty({
    type: Number,
    enum: GovernanceExperienceTypeEnum,
    required: false,
  })
  @IsEnum(GovernanceExperienceTypeEnum)
  @IsOptional()
  experienceType?: GovernanceExperienceTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  experienceText?: string;

  @ApiProperty({
    type: String,
    example: new Date().toISOString().split('T')[0],
    required: false,
  })
  @Transform(({ value }) => new Date(value))
  @IsDate()
  @IsOptional()
  birthDate?: Date;
}
