import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';

export class GetCurrentGovernanceDto {
  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  subsidiaryId?: number;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  reportId?: number;
}
