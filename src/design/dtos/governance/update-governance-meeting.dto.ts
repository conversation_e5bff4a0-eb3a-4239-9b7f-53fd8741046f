import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsDate,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateGovernanceMeetingDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  meetingId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  governanceId?: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  memberProfileIds?: number[];

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  fileIds?: number[];

  @ApiProperty({
    type: String,
    example: new Date().toISOString().split('.')[0],
    required: false,
  })
  @Transform(({ value }) => new Date(value))
  @IsDate()
  @IsOptional()
  meetingDate?: Date;
}
