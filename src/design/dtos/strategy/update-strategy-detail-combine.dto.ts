import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

export class UpdateStrategyDetailCombineDto {
  @ApiProperty({ type: Number, isArray: true })
  @IsArray()
  @ArrayUnique()
  @ArrayMinSize(2)
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  strategyIds: number[];

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  strategyFocusArea?: string;
}
