import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber } from 'class-validator';
import { StrategyHelpTypeEnum } from '../../enums/strategy-help-type.enum';

export class CreateStrategyHelpDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  helpedUserId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  strategyId: number;

  @ApiProperty({ type: Number, enum: StrategyHelpTypeEnum })
  @IsEnum(StrategyHelpTypeEnum)
  helpType: StrategyHelpTypeEnum;
}
