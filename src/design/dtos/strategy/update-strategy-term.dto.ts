import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsEnum,
  IsNumber,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { StrategyTermTypeEnum } from '../../enums/strategy-term-type.enum';

export class StrategyTermDto {
  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(25)
  minValue: number;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(25)
  maxValue: number;

  @ApiProperty({ type: Number, enum: StrategyTermTypeEnum })
  @IsEnum(StrategyTermTypeEnum)
  type: StrategyTermTypeEnum;
}

export class UpdateStrategyTermDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: StrategyTermDto, isArray: true })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(3)
  @ValidateNested({ each: true })
  @Type(() => StrategyTermDto)
  terms: StrategyTermDto[];
}
