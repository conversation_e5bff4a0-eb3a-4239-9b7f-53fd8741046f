import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class StrategyDetailSeparateDto {
  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  detailId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  strategyFocusArea?: string;
}

export class UpdateStrategyDetailSeparateDto {
  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  strategyId: number;

  @ApiProperty({ type: StrategyDetailSeparateDto, isArray: true })
  @IsArray()
  @ArrayMinSize(2)
  @ValidateNested({ each: true })
  @Type(() => StrategyDetailSeparateDto)
  details: StrategyDetailSeparateDto[];
}
