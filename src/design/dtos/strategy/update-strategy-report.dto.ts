import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

export class UpdateStrategyReportDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isRoadMap?: boolean;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  isCompleted?: boolean;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  password?: string;
}
