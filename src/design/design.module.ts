import { Modu<PERSON> } from '@nestjs/common';
import { CommonModule } from '../common/common.module';
import { StrategyController } from './controllers/strategy.controller';
import { StrategyService } from './services/strategy.service';
import { StrategyValidation } from './validations/strategy.validation';
import { ActivityModule } from '../activity/activity.module';
import { OrganizationModule } from '../organization/organization.module';
import { GovernanceController } from './controllers/governance.controller';
import { GovernanceService } from './services/governance.service';
import { GovernanceValidation } from './validations/governance.validation';
import { MailModule } from '../mail/mail.module';
import { MetricController } from './controllers/metric.controller';
import { MetricService } from './services/metric.service';
import { MetricValidation } from './validations/metric.validation';

@Module({
  imports: [ActivityModule, OrganizationModule, MailModule, CommonModule],
  controllers: [GovernanceController, MetricController, StrategyController],
  providers: [
    GovernanceService,
    GovernanceValidation,
    MetricService,
    MetricValidation,
    StrategyService,
    StrategyValidation,
  ],
  exports: [MetricService],
})
export class DesignModule {}
