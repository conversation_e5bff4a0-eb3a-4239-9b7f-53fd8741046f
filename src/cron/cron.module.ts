import { Module, Provider } from '@nestjs/common';
import { NotificationCron } from './notification.cron';
import { CommonModule } from '../common/common.module';
import { BullModule } from '@nestjs/bullmq';

const imports: any[] = [CommonModule];
const providers: Provider[] = [];
if (process.env.APP_ENV == 'production') {
  imports.push(BullModule.registerQueue({ name: 'mail' }));
  providers.push(NotificationCron);
}

@Module({
  imports: imports,
  providers: providers,
})
export class CronModule {}
