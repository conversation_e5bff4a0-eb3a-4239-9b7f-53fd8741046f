import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { endOfToday, getHours, startOfToday, startOfYesterday } from 'date-fns';
import { InjectQueue } from '@nestjs/bullmq';
import { Job, Queue } from 'bullmq';
import { PrismaService } from '../common/services/prisma.service';
import { NotificationMailDto } from '../mail/dtos/notification-mail.dto';

@Injectable()
export class NotificationCron {
  private readonly logger = new Logger(NotificationCron.name);
  private readonly startHour: number = 6;
  private readonly endHour: number = 14;

  constructor(
    private readonly prismaService: PrismaService,
    @InjectQueue('mail') private readonly mailQueue: Queue,
  ) {}

  //@Cron(CronExpression.EVERY_10_SECONDS)
  @Cron('0 6-14 * * 1-5')
  async notificationCron() {
    this.logger.debug('notification cron job started');

    const nowHour = getHours(new Date());
    const endDate = endOfToday();
    endDate.setHours(nowHour - 1);

    let startDate: Date;
    if (nowHour == this.startHour) {
      startDate = startOfYesterday();
      startDate.setHours(this.endHour);
    } else {
      startDate = startOfToday();
      startDate.setHours(nowHour - 1);
    }
    this.logger.debug(`startDate: ${startDate}, endDate: ${endDate}`);

    const users = await this.prismaService.user.findMany({
      select: {
        name: true,
        surname: true,
        email: true,
        notifications: {
          where: { isRead: false, createdAt: { gte: startDate, lte: endDate } },
        },
      },
      where: {
        notifications: {
          some: { isRead: false, createdAt: { gte: startDate, lte: endDate } },
        },
      },
    });

    const promises: Promise<Job>[] = [];
    for (const user of users) {
      const dto: NotificationMailDto = {
        name: user.name,
        surname: user.surname,
        email: user.email,
        unreadCount: user.notifications.length,
      };
      const queue = this.mailQueue.add('mail', dto, { delay: 1000 });
      promises.push(queue);
    }
    await Promise.all(promises);

    this.logger.debug(
      `notification cron job finished. ${promises.length} mails sent.`,
    );
  }
}
