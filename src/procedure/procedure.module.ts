import { Module } from '@nestjs/common';
import { ProcedureController } from './procedure.controller';
import { ProcedureService } from './procedure.service';
import { CommonModule } from '../common/common.module';
import { ProcedureValidation } from './procedure.validation';
import { QuickStartModule } from '../quick-start/quick-start.module';

@Module({
  imports: [QuickStartModule, CommonModule],
  controllers: [ProcedureController],
  providers: [ProcedureService, ProcedureValidation],
  exports: [ProcedureService],
})
export class ProcedureModule {}
