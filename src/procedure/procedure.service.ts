import { Injectable } from '@nestjs/common';
import { PrismaService } from '../common/services/prisma.service';
import { UserDto } from '../common/dtos/user.dto';
import { GetCategoryDto } from './dtos/get-category.dto';
import { CreateOrganizationProcedurePolicyDto } from './dtos/create-organization-procedure-policy.dto';
import { GetOrganizationProcedurePolicyDto } from './dtos/get-organization-procedure-policy.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../common/dtos/user-data.dto';
import { DeleteOrganizationProcedurePolicyDto } from './dtos/delete-organization-procedure-policy.dto';
import { UpdateOrganizationProcedurePolicyDto } from './dtos/update-organization-procedure-policy.dto';
import { CreateOrganizationProcedureCategoryDto } from './dtos/create-organization-procedure-category.dto';
import {
  OrganizationProcedureCategory,
  OrganizationProcedurePolicy,
  PrismaClient,
  Procedure,
  ProcedureCategory,
  ProcedurePolicy,
} from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { DeleteOrganizationProcedureCategoryDto } from './dtos/delete-organization-procedure-category.dto';
import { UpdateOrganizationProcedureCategoryDto } from './dtos/update-organization-procedure-category.dto';
import { ProcedureValidation } from './procedure.validation';
import { UtilService } from '../common/services/util.service';
import { QuickStartService } from '../quick-start/quick-start.service';
import { QuickTypeEnum } from '../quick-start/enums/quick-type.enum';

@Injectable()
export class ProcedureService {
  constructor(
    private prismaService: PrismaService,
    private procedureValidation: ProcedureValidation,
    private utilService: UtilService,
    private quickStartService: QuickStartService,
  ) {}

  async getProcedures(user: UserDto) {
    const procedures = await this.prismaService.procedure.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        categoryButton: true,
        policyButton: true,
      },
      orderBy: { order: 'asc' },
    });
    return procedures;
  }

  async getCategories(organizationId: number, dto: GetCategoryDto) {
    let orgProcedureCategories =
      await this.prismaService.organizationProcedureCategory.findMany({
        select: {
          id: true,
          categoryId: true,
          name: true,
          description: true,
          year: true,
          isDeleted: true,
          createdAt: true,
          updatedAt: true,
          createdUser: {
            select: {
              id: true,
              name: true,
              surname: true,
              image: true,
              userOrganizations: true,
            },
          },
          organizationProcedurePolicies: {
            where: { organizationId: organizationId },
          },
        },
        where: {
          organizationId: organizationId,
          procedure: { slug: dto.procedureSlug },
        },
        orderBy: { order: 'asc' },
      });
    let categories = await this.prismaService.procedureCategory.findMany({
      select: {
        id: true,
        name: true,
        policilies: {
          select: {
            organizationProcedurePolicies: {
              where: { organizationId: organizationId },
            },
          },
        },
      },
      where: { procedure: { slug: dto.procedureSlug } },
      orderBy: { order: 'asc' },
    });
    const excludedCategoryIds = orgProcedureCategories
      .filter((opc) => opc.categoryId)
      .map((opc) => opc.categoryId);
    orgProcedureCategories = orgProcedureCategories.filter(
      (opc) => !opc.isDeleted,
    );
    for (const opc of orgProcedureCategories) {
      opc['isCustom'] = true;
      opc['createdUserInfo'] = plainToInstance(UserDataDto, opc.createdUser, {
        excludeExtraneousValues: true,
      });
      if (opc.categoryId) {
        const category = categories.find((c) => c.id == opc.categoryId);
        opc['totalPolicyCount'] =
          category.policilies.length +
          opc.organizationProcedurePolicies.filter((opp) => !opp.policyId)
            .length;
      } else {
        opc['totalPolicyCount'] = opc.organizationProcedurePolicies.length;
      }
      opc['completedPolicyCount'] = opc.organizationProcedurePolicies.length;
      delete opc.categoryId;
      delete opc.isDeleted;
      delete opc.createdUser;
      delete opc.organizationProcedurePolicies;
    }
    categories = categories.filter((c) => !excludedCategoryIds.includes(c.id));
    for (const c of categories) {
      c['totalPolicyCount'] = c.policilies.length;
      c['completedPolicyCount'] = c.policilies.filter(
        (p) => p.organizationProcedurePolicies.length > 0,
      ).length;
      delete c.policilies;
    }
    return [...orgProcedureCategories, ...categories];
  }

  async getOrganizationProcedurePolicy(
    organizationId: number,
    dto: GetOrganizationProcedurePolicyDto,
  ) {
    let customCategoryId: number;
    let defaultCategoryId: number;
    if (dto.isCustom) {
      const orgProcedureCategory =
        await this.prismaService.organizationProcedureCategory.findFirstOrThrow(
          {
            where: {
              id: dto.categoryId,
              organizationId: organizationId,
            },
          },
        );
      customCategoryId = orgProcedureCategory.id;
      defaultCategoryId = orgProcedureCategory.categoryId;
    } else {
      defaultCategoryId = dto.categoryId;
    }
    let orgProcedurePolicies =
      await this.prismaService.organizationProcedurePolicy.findMany({
        select: {
          id: true,
          policyId: true,
          name: true,
          description: true,
          responsibleFullName: true,
          responsibleTitle: true,
          year: true,
          fileIds: true,
          isDeleted: true,
          createdAt: true,
          updatedAt: true,
          createdUser: {
            select: {
              id: true,
              name: true,
              surname: true,
              image: true,
              userOrganizations: true,
            },
          },
          organizationProcedureCategory: {
            select: {
              id: true,
              name: true,
              procedure: { select: { id: true, name: true, slug: true } },
            },
          },
          policy: { select: { id: true, name: true } },
        },
        where: {
          organizationId: organizationId,
          organizationProcedureCategoryId: customCategoryId ?? 0,
        },
        orderBy: { order: 'asc' },
      });
    let policies = await this.prismaService.procedurePolicy.findMany({
      select: { id: true, name: true },
      where: { categoryId: defaultCategoryId ?? 0 },
      orderBy: { order: 'asc' },
    });
    const excludedPolicyIds = orgProcedurePolicies
      .filter((opp) => opp.policyId)
      .map((opp) => opp.policyId);
    orgProcedurePolicies = orgProcedurePolicies.filter((opp) => !opp.isDeleted);
    const fileIds = [
      ...new Set(
        orgProcedurePolicies
          .filter((opp) => opp.fileIds)
          .map((opp) => this.utilService.jsonToNumberList(opp.fileIds))
          .flat(1),
      ),
    ];
    const files = await this.prismaService.fileManager.findMany({
      select: {
        id: true,
        name: true,
        url: true,
        type: true,
        size: true,
        createdAt: true,
      },
      where: { id: { in: fileIds }, organizationId: organizationId },
    });
    for (const opp of orgProcedurePolicies) {
      opp['isCustom'] = true;
      opp['createdUserInfo'] = plainToInstance(UserDataDto, opp.createdUser, {
        excludeExtraneousValues: true,
      });
      if (opp.fileIds) {
        const oppFileIds = this.utilService.jsonToNumberList(opp.fileIds);
        opp['files'] = files.filter((f) => oppFileIds.includes(f.id));
      }
      delete opp.policyId;
      delete opp.isDeleted;
      delete opp.createdUser;
      delete opp.fileIds;
    }
    policies = policies.filter((p) => !excludedPolicyIds.includes(p.id));
    return [...orgProcedurePolicies, ...policies];
  }

  async createOrganizationProcedureCategory(
    user: UserDto,
    dto: CreateOrganizationProcedureCategoryDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      categoryId?: number;
      isDeleted?: boolean;
    },
  ): Promise<OrganizationProcedureCategory> {
    const dbService = options?.tx ?? this.prismaService;
    const procedure = await dbService.procedure.findFirstOrThrow({
      where: { slug: dto.procedureSlug },
    });
    const lastCategory =
      await dbService.organizationProcedureCategory.findFirst({
        where: {
          organizationId: user.organizationId,
          procedureId: procedure.id,
        },
        orderBy: { order: 'desc' },
      });
    if (options?.categoryId) {
      const orgProcedureCategory =
        await dbService.organizationProcedureCategory.findFirst({
          where: {
            organizationId: user.organizationId,
            procedureId: procedure.id,
            categoryId: options.categoryId,
          },
        });
      if (orgProcedureCategory) {
        return orgProcedureCategory;
      }
    }
    return await dbService.organizationProcedureCategory.create({
      data: {
        createdUserId: user.id,
        organizationId: user.organizationId,
        procedureId: procedure.id,
        categoryId: options?.categoryId,
        name: dto.name,
        description: dto.description,
        year: dto.year,
        order: lastCategory ? lastCategory.order + 1 : 1,
        isDeleted: options?.isDeleted,
      },
    });
  }

  async createOrganizationProcedurePolicy(
    user: UserDto,
    dto: CreateOrganizationProcedurePolicyDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      isDeleted?: boolean;
    },
  ) {
    const defaultCategory =
      await this.procedureValidation.createOrganizationProcedurePolicy(
        user,
        dto,
        options?.tx,
      );
    let organizationProcedureCategoryId: number = dto.categoryId;
    await this.prismaService.$transaction(async (tx) => {
      tx = options?.tx ?? tx;
      if (!dto.isCustomCategory) {
        organizationProcedureCategoryId = (
          await this.createOrganizationProcedureCategory(
            user,
            {
              procedureSlug: dto.procedureSlug,
              name: defaultCategory.name,
            },
            { tx: tx, categoryId: defaultCategory.id },
          )
        ).id;
      }
      const lastPolicy = await tx.organizationProcedurePolicy.findFirst({
        where: {
          organizationId: user.organizationId,
          organizationProcedureCategoryId: organizationProcedureCategoryId,
        },
        orderBy: { order: 'desc' },
      });
      await tx.organizationProcedurePolicy.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          organizationProcedureCategoryId: organizationProcedureCategoryId,
          policyId: dto.policyId,
          name: dto.name,
          description: dto.description,
          responsibleFullName: dto.responsibleFullName,
          responsibleTitle: dto.responsibleTitle,
          order: lastPolicy ? lastPolicy.order + 1 : 1,
          year: dto.year,
          fileIds: dto.fileIds,
          isDeleted: options?.isDeleted,
        },
      });
      await this.quickStartService.completeQuickStart(
        user,
        QuickTypeEnum.POLICY_CREATE,
        tx,
      );
    });
  }

  async updateOrganizationProcedureCategory(
    user: UserDto,
    dto: UpdateOrganizationProcedureCategoryDto,
  ): Promise<void> {
    if (dto.isCustom) {
      await this.prismaService.organizationProcedureCategory.update({
        where: { id: dto.id, organizationId: user.organizationId },
        data: {
          name: dto.name,
          description: dto.description,
          year: dto.year,
        },
      });
    } else {
      const defaultCategory =
        await this.prismaService.procedureCategory.findFirstOrThrow({
          select: { procedure: true },
          where: { id: dto.id },
        });
      await this.createOrganizationProcedureCategory(
        user,
        {
          procedureSlug: defaultCategory.procedure.slug,
          name: dto.name,
          description: dto.description,
          year: dto.year,
        },
        { categoryId: dto.id },
      );
    }
  }

  async updateOrganizationProcedurePolicy(
    user: UserDto,
    dto: UpdateOrganizationProcedurePolicyDto,
  ) {
    if (dto.isCustom) {
      await this.prismaService.organizationProcedurePolicy.update({
        where: { id: dto.id, organizationId: user.organizationId },
        data: {
          name: dto.name,
          description: dto.description,
          responsibleFullName: dto.responsibleFullName,
          responsibleTitle: dto.responsibleTitle,
          year: dto.year,
          fileIds: dto.fileIds,
        },
      });
    } else {
      const policy = await this.prismaService.procedurePolicy.findFirstOrThrow({
        select: { category: { select: { id: true, procedure: true } } },
        where: { id: dto.id },
      });
      await this.createOrganizationProcedurePolicy(user, {
        isCustomCategory: false,
        procedureSlug: policy.category.procedure.slug,
        categoryId: policy.category.id,
        policyId: dto.id,
        name: dto.name,
        description: dto.description,
        responsibleFullName: dto.responsibleFullName,
        responsibleTitle: dto.responsibleTitle,
        year: dto.year,
        fileIds: dto.fileIds,
      });
    }
  }

  async deleteOrganizationProcedureCategory(
    user: UserDto,
    dto: DeleteOrganizationProcedureCategoryDto,
    procedure: Procedure,
    category?: ProcedureCategory,
    orgProcedureCategory?: OrganizationProcedureCategory,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      if (dto.isCustom) {
        await tx.organizationProcedurePolicy.deleteMany({
          where: {
            organizationId: user.organizationId,
            organizationProcedureCategoryId: dto.id,
          },
        });
        if (orgProcedureCategory.categoryId) {
          await tx.organizationProcedureCategory.update({
            where: { id: dto.id, organizationId: user.organizationId },
            data: { isDeleted: true },
          });
        } else {
          await tx.organizationProcedureCategory.delete({
            where: { id: dto.id, organizationId: user.organizationId },
          });
        }
      } else {
        if (orgProcedureCategory) {
          await tx.organizationProcedureCategory.update({
            where: { id: orgProcedureCategory.id },
            data: { isDeleted: true },
          });
        } else {
          await this.createOrganizationProcedureCategory(
            user,
            { procedureSlug: procedure.slug, name: category.name },
            { tx: tx, categoryId: category.id, isDeleted: true },
          );
        }
      }
    });
  }

  async deleteOrganizationProcedurePolicy(
    user: UserDto,
    dto: DeleteOrganizationProcedurePolicyDto,
    procedure: Procedure,
    policy?: ProcedurePolicy,
    orgProcedurePolicy?: OrganizationProcedurePolicy,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      if (dto.isCustom) {
        if (orgProcedurePolicy.policyId) {
          await tx.organizationProcedurePolicy.update({
            where: { id: dto.id, organizationId: user.organizationId },
            data: { isDeleted: true },
          });
        } else {
          await this.prismaService.organizationProcedurePolicy.delete({
            where: { id: dto.id, organizationId: user.organizationId },
          });
        }
      } else {
        if (orgProcedurePolicy) {
          await tx.organizationProcedurePolicy.update({
            where: { id: orgProcedurePolicy.id },
            data: { isDeleted: true },
          });
        } else {
          await this.createOrganizationProcedurePolicy(
            user,
            {
              procedureSlug: procedure.slug,
              categoryId: policy.categoryId,
              isCustomCategory: false,
              policyId: policy.id,
              name: policy.name,
            },
            { tx: tx, isDeleted: true },
          );
        }
      }
    });
  }
}
