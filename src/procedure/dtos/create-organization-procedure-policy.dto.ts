import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
} from 'class-validator';
import { getYear } from 'date-fns';

export class CreateOrganizationProcedurePolicyDto {
  @ApiProperty({ type: String })
  @IsString()
  procedureSlug: string;

  @ApiProperty({ type: Number })
  @IsNumber()
  categoryId: number;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  isCustomCategory: boolean;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  policyId?: number;

  @ApiProperty({ type: String })
  @IsString()
  name: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  responsibleFullName?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  responsibleTitle?: string;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1900)
  @Max(getYear(new Date()))
  @IsOptional()
  year?: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @ArrayUnique()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  fileIds?: number[];
}
