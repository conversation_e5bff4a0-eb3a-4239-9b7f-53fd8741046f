import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';
import { getYear } from 'date-fns';

export class CreateOrganizationProcedureCategoryDto {
  @ApiProperty({ type: String })
  @IsString()
  procedureSlug: string;

  @ApiProperty({ type: String })
  @IsString()
  name: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1900)
  @Max(getYear(new Date()))
  @IsOptional()
  year?: number;
}
