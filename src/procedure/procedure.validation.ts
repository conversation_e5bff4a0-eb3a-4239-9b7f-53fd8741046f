import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../common/services/prisma.service';
import { UserDto } from '../common/dtos/user.dto';
import { DeleteOrganizationProcedurePolicyDto } from './dtos/delete-organization-procedure-policy.dto';
import { CreateOrganizationProcedurePolicyDto } from './dtos/create-organization-procedure-policy.dto';
import {
  OrganizationProcedureCategory,
  OrganizationProcedurePolicy,
  PrismaClient,
  Procedure,
  ProcedureCategory,
  ProcedurePolicy,
} from '@prisma/client';
import { DeleteOrganizationProcedureCategoryDto } from './dtos/delete-organization-procedure-category.dto';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateOrganizationProcedurePolicyDto } from './dtos/update-organization-procedure-policy.dto';

@Injectable()
export class ProcedureValidation {
  constructor(private prismaService: PrismaService) {}

  async createOrganizationProcedurePolicy(
    user: UserDto,
    dto: CreateOrganizationProcedurePolicyDto,
    tx?: Omit<PrismaClient, ITXClientDenyList>,
  ): Promise<ProcedureCategory> {
    const dbService = tx ?? this.prismaService;
    let defaultCategory: ProcedureCategory;
    let defaultCategoryId: number;
    if (dto.isCustomCategory) {
      const customCategory =
        await dbService.organizationProcedureCategory.findFirstOrThrow({
          where: {
            id: dto.categoryId,
            organizationId: user.organizationId,
            procedure: { slug: dto.procedureSlug },
          },
        });
      defaultCategoryId = customCategory.categoryId;
    } else {
      defaultCategory = await dbService.procedureCategory.findFirstOrThrow({
        where: { id: dto.categoryId, procedure: { slug: dto.procedureSlug } },
      });
      defaultCategoryId = defaultCategory.id;
    }
    if (dto.policyId) {
      await dbService.procedurePolicy.findFirstOrThrow({
        where: { id: dto.policyId, categoryId: defaultCategoryId },
      });
      const orgProcedurePolicy =
        await dbService.organizationProcedurePolicy.findFirst({
          where: {
            organizationId: user.organizationId,
            policyId: dto.policyId,
          },
        });
      if (orgProcedurePolicy) {
        throw new BadRequestException(
          'Bu politika için zaten kayıtlı veriniz bulunmaktadır.',
        );
      }
    }
    if (dto.fileIds?.length > 0) {
      const fileCount = await this.prismaService.fileManager.count({
        where: {
          id: { in: dto.fileIds },
          organizationId: user.organizationId,
        },
      });
      if (fileCount != dto.fileIds.length) {
        throw new BadRequestException('Geçersiz dosyalar.');
      }
    }
    return defaultCategory;
  }

  async updateOrganizationProcedurePolicy(
    user: UserDto,
    dto: UpdateOrganizationProcedurePolicyDto,
  ): Promise<void> {
    if (dto.fileIds?.length > 0) {
      const fileCount = await this.prismaService.fileManager.count({
        where: {
          id: { in: dto.fileIds },
          organizationId: user.organizationId,
        },
      });
      if (fileCount != dto.fileIds.length) {
        throw new BadRequestException('Geçersiz dosyalar.');
      }
    }
  }

  async deleteOrganizationProcedureCategory(
    user: UserDto,
    dto: DeleteOrganizationProcedureCategoryDto,
  ): Promise<{
    procedure: Procedure;
    category?: ProcedureCategory;
    orgProcedureCategory?: OrganizationProcedureCategory;
  }> {
    if (dto.isCustom) {
      const result =
        await this.prismaService.organizationProcedureCategory.findFirstOrThrow(
          {
            where: { id: dto.id, organizationId: user.organizationId },
            include: { procedure: true },
          },
        );
      return { procedure: result.procedure, orgProcedureCategory: result };
    } else {
      const category =
        await this.prismaService.procedureCategory.findFirstOrThrow({
          where: { id: dto.id },
          include: { procedure: true },
        });
      const orgProcedureCategory =
        await this.prismaService.organizationProcedureCategory.findFirst({
          where: {
            organizationId: user.organizationId,
            categoryId: dto.id,
          },
        });
      return {
        procedure: category.procedure,
        category: category,
        orgProcedureCategory: orgProcedureCategory,
      };
    }
  }

  async deleteOrganizationProcedurePolicy(
    user: UserDto,
    dto: DeleteOrganizationProcedurePolicyDto,
  ): Promise<{
    procedure: Procedure;
    policy?: ProcedurePolicy;
    orgProcedurePolicy?: OrganizationProcedurePolicy;
  }> {
    if (dto.isCustom) {
      const orgProcedurePolicy =
        await this.prismaService.organizationProcedurePolicy.findFirstOrThrow({
          where: { id: dto.id, organizationId: user.organizationId },
          include: {
            organizationProcedureCategory: { include: { procedure: true } },
          },
        });
      return {
        procedure: orgProcedurePolicy.organizationProcedureCategory.procedure,
        orgProcedurePolicy: orgProcedurePolicy,
      };
    } else {
      const policy = await this.prismaService.procedurePolicy.findFirstOrThrow({
        where: { id: dto.id },
        include: { category: { include: { procedure: true } } },
      });
      const orgProcedurePolicy =
        await this.prismaService.organizationProcedurePolicy.findFirst({
          where: { organizationId: user.organizationId, policyId: dto.id },
        });
      return {
        procedure: policy.category.procedure,
        policy: policy,
        orgProcedurePolicy: orgProcedurePolicy,
      };
    }
  }
}
