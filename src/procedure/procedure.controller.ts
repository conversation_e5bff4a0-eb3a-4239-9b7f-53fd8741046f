import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ProcedureService } from './procedure.service';
import { User } from '../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../common/dtos/general-response.dto';
import { UserDto } from '../common/dtos/user.dto';
import { ProcedureValidation } from './procedure.validation';
import { GetCategoryDto } from './dtos/get-category.dto';
import { CreateOrganizationProcedurePolicyDto } from './dtos/create-organization-procedure-policy.dto';
import { GetOrganizationProcedurePolicyDto } from './dtos/get-organization-procedure-policy.dto';
import { UpdateOrganizationProcedurePolicyDto } from './dtos/update-organization-procedure-policy.dto';
import { DeleteOrganizationProcedurePolicyDto } from './dtos/delete-organization-procedure-policy.dto';
import { CreateOrganizationProcedureCategoryDto } from './dtos/create-organization-procedure-category.dto';
import { DeleteOrganizationProcedureCategoryDto } from './dtos/delete-organization-procedure-category.dto';
import { UpdateOrganizationProcedureCategoryDto } from './dtos/update-organization-procedure-category.dto';
import { PermissionEnum } from '../common/enums/permission.enum';
import { Permissions } from '../common/decorators/permission.decorator';

@ApiTags('Procedures')
@Controller('procedures')
@Permissions(PermissionEnum.PROCEDURE)
export class ProcedureController {
  constructor(
    private procedureValidation: ProcedureValidation,
    private procedureService: ProcedureService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getProcedures(@User() user: UserDto) {
    const procedures = await this.procedureService.getProcedures(user);
    return new GeneralResponseDto().setData(procedures);
  }

  @Get('categories')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCategories(@User() user: UserDto, @Query() dto: GetCategoryDto) {
    const categories = await this.procedureService.getCategories(
      user.organizationId,
      dto,
    );
    return new GeneralResponseDto().setData(categories);
  }

  @Get('policies')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getOrganizationProcedurePolicy(
    @User() user: UserDto,
    @Query() dto: GetOrganizationProcedurePolicyDto,
  ) {
    const orgProcedures =
      await this.procedureService.getOrganizationProcedurePolicy(
        user.organizationId,
        dto,
      );
    return new GeneralResponseDto().setData(orgProcedures);
  }

  @Post('categories')
  @ApiBearerAuth()
  @ApiBody({ type: CreateOrganizationProcedureCategoryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createOrganizationProcedureCategory(
    @User() user: UserDto,
    @Body() dto: CreateOrganizationProcedureCategoryDto,
  ) {
    await this.procedureService.createOrganizationProcedureCategory(user, dto);
    return new GeneralResponseDto();
  }

  @Post('policies')
  @ApiBearerAuth()
  @ApiBody({ type: CreateOrganizationProcedurePolicyDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createOrganizationProcedurePolicy(
    @User() user: UserDto,
    @Body() dto: CreateOrganizationProcedurePolicyDto,
  ) {
    await this.procedureService.createOrganizationProcedurePolicy(user, dto);
    return new GeneralResponseDto();
  }

  @Put('categories')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateOrganizationProcedureCategoryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateOrganizationProcedureCategory(
    @User() user: UserDto,
    @Body() dto: UpdateOrganizationProcedureCategoryDto,
  ) {
    await this.procedureService.updateOrganizationProcedureCategory(user, dto);
    return new GeneralResponseDto();
  }

  @Put('policies')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateOrganizationProcedurePolicyDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateOrganizationProcedurePolicy(
    @User() user: UserDto,
    @Body() dto: UpdateOrganizationProcedurePolicyDto,
  ) {
    await this.procedureValidation.updateOrganizationProcedurePolicy(user, dto);
    await this.procedureService.updateOrganizationProcedurePolicy(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('categories')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteOrganizationProcedureCategoryDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteOrganizationProcedureCategory(
    @User() user: UserDto,
    @Body() dto: DeleteOrganizationProcedureCategoryDto,
  ) {
    const { procedure, category, orgProcedureCategory } =
      await this.procedureValidation.deleteOrganizationProcedureCategory(
        user,
        dto,
      );
    await this.procedureService.deleteOrganizationProcedureCategory(
      user,
      dto,
      procedure,
      category,
      orgProcedureCategory,
    );
    return new GeneralResponseDto();
  }

  @Delete('policies')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteOrganizationProcedurePolicyDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteOrganizationProcedurePolicy(
    @User() user: UserDto,
    @Body() dto: DeleteOrganizationProcedurePolicyDto,
  ) {
    const { procedure, policy, orgProcedurePolicy } =
      await this.procedureValidation.deleteOrganizationProcedurePolicy(
        user,
        dto,
      );
    await this.procedureService.deleteOrganizationProcedurePolicy(
      user,
      dto,
      procedure,
      policy,
      orgProcedurePolicy,
    );
    return new GeneralResponseDto();
  }
}
