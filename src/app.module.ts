import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import { AuthGuard } from './common/guards/auth.guard';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { CommonModule } from './common/common.module';
import { ErrorValidationPipe } from './common/pipes/error-validation.pipe';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { PrismaExceptionFilter } from './common/filters/prisma-exception.filter';
import { RoleGuard } from './common/guards/role.guard';
import { PermissionGuard } from './common/guards/permission.guard';
import { AuthModule } from './auth/auth.module';
import { OrganizationModule } from './organization/organization.module';
import { FileManagerModule } from './file-manager/file-manager.module';
import { join } from 'path';
import { ServeStaticModule } from '@nestjs/serve-static';
import { ProcedureModule } from './procedure/procedure.module';
import { QuickStartModule } from './quick-start/quick-start.module';
import { AnalysisModule } from './analysis/analysis.module';
import { ActivityModule } from './activity/activity.module';
import { NotificationModule } from './notification/notification.module';
import { CarbonModule } from './carbon/carbon.module';
import { MailModule } from './mail/mail.module';
import { InsightModule } from './insight/insight.module';
import { OrganizationTypeGuard } from './common/guards/organization-type.guard';
import { DesignModule } from './design/design.module';
import { AdminGuard } from './common/guards/admin.guard';
import { AdminModule } from './admin/admin.module';
import { ObservationModule } from './observation/observation.module';
import { BullModule } from '@nestjs/bullmq';
import { CronModule } from './cron/cron.module';
import { ScheduleModule } from '@nestjs/schedule';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 60 seconds
        limit: 600,
        skipIf: () => {
          return process.env.APP_ENV != 'production';
        },
      },
    ]),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
      serveRoot: '/public',
    }),
    ScheduleModule.forRoot(),
    ...(process.env.APP_ENV == 'production'
      ? [
          BullModule.forRoot({
            connection: {
              host: 'localhost',
              port: 6379,
            },
            prefix: process.env.APP_NAME,
          }),
        ]
      : []),

    ActivityModule,
    AdminModule,
    AnalysisModule,
    AuthModule,
    CarbonModule,
    CommonModule,
    CronModule,
    DesignModule,
    FileManagerModule,
    InsightModule,
    MailModule,
    NotificationModule,
    ObservationModule,
    OrganizationModule,
    ProcedureModule,
    QuickStartModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RoleGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PermissionGuard,
    },
    {
      provide: APP_GUARD,
      useClass: OrganizationTypeGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AdminGuard,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: PrismaExceptionFilter,
    },
    {
      provide: APP_PIPE,
      useClass: ErrorValidationPipe,
    },
  ],
})
export class AppModule {}
