import { BadRequestException, Injectable } from '@nestjs/common';
import { UserDto } from '../common/dtos/user.dto';
import { OrganizationService } from '../organization/services/organization.service';
import { GetActivityReqDto } from './dtos/req/get-activity.req.dto';

@Injectable()
export class ActivityValidation {
  constructor(private organizationService: OrganizationService) {}

  async getActivities(user: UserDto, dto: GetActivityReqDto) {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }
}
