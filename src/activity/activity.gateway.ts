import { UserDto } from '../common/dtos/user.dto';
import { UtilService } from '../common/services/util.service';
import { GetActivityResDto } from './dtos/res/get-activity.res.dto';
import { ActivityTabType } from './types/activity-tab.type';
import { ActivityTabEnum } from './enums/activity-tab.enum';
import { OrganizationUtilService } from '../common/services/organization.util.service';
import { Activity, PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { ActivityUtilService } from '../common/services/activity.util.service';
import { SocketIoGateway } from '../common/gateways/socket-io.gateway';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ActivityGateway {
  constructor(
    private socketIoGateway: SocketIoGateway,
    private utilService: UtilService,
    private activityUtilService: ActivityUtilService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async sendActivity(
    user: UserDto,
    activities: Activity[],
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<void> {
    const userIds = await this.organizationUtilService.getOrganizationUserIds(
      user,
      { tx: options?.tx },
    );
    const activityListData =
      await this.activityUtilService.getDataForActivityList(
        user.organizationId,
        activities,
        { tx: options?.tx },
      );
    const activityTabEntries = Object.entries(ActivityTabType);
    for (const userId of userIds) {
      const activityList = await this.activityUtilService.getActivityList(
        userId,
        user.organizationId,
        activities,
        { tx: options?.tx, data: activityListData },
      );
      for (const activity of activities) {
        const activityTabs: ActivityTabEnum[] = [];
        activityTabEntries.map(([tab, activityTypes]) => {
          if (activityTypes.includes(activity.activityType)) {
            activityTabs.push(tab as ActivityTabEnum);
          }
        });
        const activityContent: GetActivityResDto = {
          id: activity.id,
          organizationId: activity.organizationId,
          content: activityList[activity.id].content,
          actions: activityList[activity.id].actions,
          createdAt: activity.createdAt,
          createdUser: activity['createdUserInfo'],
        };
        for (const tab of activityTabs) {
          this.socketIoGateway.sendEmit(
            this.utilService.socketRoom(userId, user.organizationId),
            `createActivity-${tab}`,
            activityContent,
          );
        }
      }
    }
  }
}
