import { ActivityTabEnum } from '../enums/activity-tab.enum';
import { ActivityEnum } from '../enums/activity.enum';

export const ActivityTabType = {
  [ActivityTabEnum.ALL]: [
    ActivityEnum.SITUATION_REPORT_CREATE,
    ActivityEnum.SITUATION_REPORT_COMPLETE,
    ActivityEnum.SITUATION_HELP,
    ActivityEnum.SITUATION_HELP_ANSWER,
    ActivityEnum.TREND_REPORT_CREATE,
    ActivityEnum.TREND_REPORT_COMPLETE,
    ActivityEnum.TREND_HELP,
    ActivityEnum.TREND_HELP_ANSWER,
    ActivityEnum.RISK_REPORT_CREATE,
    ActivityEnum.RISK_REPORT_COMPLETE,
    ActivityEnum.RISK_HELP,
    ActivityEnum.RISK_HELP_ANSWER,
    ActivityEnum.CHAIN_REPORT_CREATE,
    ActivityEnum.CHAIN_REPORT_COMPLETE,
    ActivityEnum.CHAIN_HELP,
    ActivityEnum.CHAIN_HELP_ANSWER,
    ActivityEnum.CAPITAL_REPORT_CREATE,
    ActivityEnum.CAPITAL_REPORT_COMPLETE,
    ActivityEnum.CAPITAL_HELP,
    ActivityEnum.CAPITAL_HELP_ANSWER,
    ActivityEnum.FINANCIAL_REPORT_CREATE,
    ActivityEnum.FINANCIAL_REPORT_COMPLETE,
    ActivityEnum.FINANCIAL_HELP,
    ActivityEnum.FINANCIAL_HELP_ANSWER,
    ActivityEnum.FINANCIAL_REVIEW,
    ActivityEnum.FINANCIAL_REVIEW_ANSWER,
    ActivityEnum.IMPACT_REPORT_CREATE,
    ActivityEnum.IMPACT_REPORT_COMPLETE,
    ActivityEnum.IMPACT_HELP,
    ActivityEnum.IMPACT_HELP_ANSWER,
    ActivityEnum.IMPACT_REVIEW,
    ActivityEnum.IMPACT_REVIEW_ANSWER,
    ActivityEnum.INDUSTRY_REPORT_CREATE,
    ActivityEnum.INDUSTRY_REPORT_COMPLETE,
    ActivityEnum.INDUSTRY_HELP,
    ActivityEnum.INDUSTRY_HELP_ANSWER,
    ActivityEnum.OPINION_REPORT_CREATE,
    ActivityEnum.OPINION_REPORT_COMPLETE,
    ActivityEnum.OPINION_HELP,
    ActivityEnum.OPINION_HELP_ANSWER,
    ActivityEnum.TENDENCY_REPORT_CREATE,
    ActivityEnum.TENDENCY_REPORT_COMPLETE,
    ActivityEnum.TENDENCY_HELP,
    ActivityEnum.TENDENCY_HELP_ANSWER,
    ActivityEnum.FINANCIAL_SIGNIFICANCE_CREATE,
    ActivityEnum.IMPACT_SIGNIFICANCE_CREATE,
    ActivityEnum.STRATEGY_REPORT_CREATE,
    ActivityEnum.STRATEGY_REPORT_ROAD_MAP,
    ActivityEnum.STRATEGY_REPORT_COMPLETE,
    ActivityEnum.STRATEGY_HELP,
    ActivityEnum.STRATEGY_HELP_ANSWER,
    ActivityEnum.GOVERNANCE_REPORT_CREATE,
    ActivityEnum.METRIC_REPORT_CREATE,
    ActivityEnum.METRIC_HELP,
    ActivityEnum.METRIC_SUPERVISOR,
  ],
  [ActivityTabEnum.SITUATION]: [
    ActivityEnum.SITUATION_REPORT_CREATE,
    ActivityEnum.SITUATION_REPORT_COMPLETE,
    ActivityEnum.SITUATION_HELP,
    ActivityEnum.SITUATION_HELP_ANSWER,
  ],
  [ActivityTabEnum.TREND]: [
    ActivityEnum.TREND_REPORT_CREATE,
    ActivityEnum.TREND_REPORT_COMPLETE,
    ActivityEnum.TREND_HELP,
    ActivityEnum.TREND_HELP_ANSWER,
  ],
  [ActivityTabEnum.RISK]: [
    ActivityEnum.RISK_REPORT_CREATE,
    ActivityEnum.RISK_REPORT_COMPLETE,
    ActivityEnum.RISK_HELP,
    ActivityEnum.RISK_HELP_ANSWER,
  ],
  [ActivityTabEnum.CHAIN]: [
    ActivityEnum.CHAIN_REPORT_CREATE,
    ActivityEnum.CHAIN_REPORT_COMPLETE,
    ActivityEnum.CHAIN_HELP,
    ActivityEnum.CHAIN_HELP_ANSWER,
  ],
  [ActivityTabEnum.CAPITAL]: [
    ActivityEnum.CAPITAL_REPORT_CREATE,
    ActivityEnum.CAPITAL_REPORT_COMPLETE,
    ActivityEnum.CAPITAL_HELP,
    ActivityEnum.CAPITAL_HELP_ANSWER,
  ],
  [ActivityTabEnum.FINANCIAL]: [
    ActivityEnum.FINANCIAL_REPORT_CREATE,
    ActivityEnum.FINANCIAL_REPORT_COMPLETE,
    ActivityEnum.FINANCIAL_HELP,
    ActivityEnum.FINANCIAL_HELP_ANSWER,
    ActivityEnum.FINANCIAL_REVIEW,
    ActivityEnum.FINANCIAL_REVIEW_ANSWER,
  ],
  [ActivityTabEnum.FINANCIAL_SIGNIFICANCE]: [
    ActivityEnum.FINANCIAL_SIGNIFICANCE_CREATE,
  ],
  [ActivityTabEnum.IMPACT]: [
    ActivityEnum.IMPACT_REPORT_CREATE,
    ActivityEnum.IMPACT_REPORT_COMPLETE,
    ActivityEnum.IMPACT_HELP,
    ActivityEnum.IMPACT_HELP_ANSWER,
    ActivityEnum.IMPACT_REVIEW,
    ActivityEnum.IMPACT_REVIEW_ANSWER,
  ],
  [ActivityTabEnum.IMPACT_SIGNIFICANCE]: [
    ActivityEnum.IMPACT_SIGNIFICANCE_CREATE,
  ],
  [ActivityTabEnum.INDUSTRY]: [
    ActivityEnum.INDUSTRY_REPORT_CREATE,
    ActivityEnum.INDUSTRY_REPORT_COMPLETE,
    ActivityEnum.INDUSTRY_HELP,
    ActivityEnum.INDUSTRY_HELP_ANSWER,
  ],
  [ActivityTabEnum.OPINION]: [
    ActivityEnum.OPINION_REPORT_CREATE,
    ActivityEnum.OPINION_REPORT_COMPLETE,
    ActivityEnum.OPINION_HELP,
    ActivityEnum.OPINION_HELP_ANSWER,
  ],
  [ActivityTabEnum.TENDENCY]: [
    ActivityEnum.TENDENCY_REPORT_CREATE,
    ActivityEnum.TENDENCY_REPORT_COMPLETE,
    ActivityEnum.TENDENCY_HELP,
    ActivityEnum.TENDENCY_HELP_ANSWER,
  ],
  [ActivityTabEnum.MATERIALITY]: [
    ActivityEnum.FINANCIAL_REPORT_CREATE,
    ActivityEnum.FINANCIAL_REPORT_COMPLETE,
    ActivityEnum.FINANCIAL_HELP,
    ActivityEnum.FINANCIAL_HELP_ANSWER,
    ActivityEnum.FINANCIAL_REVIEW,
    ActivityEnum.FINANCIAL_REVIEW_ANSWER,
    ActivityEnum.IMPACT_REPORT_CREATE,
    ActivityEnum.IMPACT_REPORT_COMPLETE,
    ActivityEnum.IMPACT_HELP,
    ActivityEnum.IMPACT_HELP_ANSWER,
    ActivityEnum.IMPACT_REVIEW,
    ActivityEnum.IMPACT_REVIEW_ANSWER,
  ],
  [ActivityTabEnum.STRATEGY]: [
    ActivityEnum.STRATEGY_REPORT_CREATE,
    ActivityEnum.STRATEGY_REPORT_ROAD_MAP,
    ActivityEnum.STRATEGY_REPORT_COMPLETE,
    ActivityEnum.STRATEGY_HELP,
    ActivityEnum.STRATEGY_HELP_ANSWER,
  ],
  [ActivityTabEnum.GOVERNANCE]: [ActivityEnum.GOVERNANCE_REPORT_CREATE],
  [ActivityTabEnum.METRIC]: [
    ActivityEnum.METRIC_REPORT_CREATE,
    ActivityEnum.METRIC_HELP,
    ActivityEnum.METRIC_SUPERVISOR,
  ],
};
