import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ActivityService } from './activity.service';
import { User } from '../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../common/dtos/general-response.dto';
import { UserDto } from '../common/dtos/user.dto';
import { GetActivityReqDto } from './dtos/req/get-activity.req.dto';
import { ActivityValidation } from './activity.validation';

@ApiTags('Activities')
@Controller('activities')
export class ActivityController {
  constructor(
    private activityService: ActivityService,
    private activityValidation: ActivityValidation,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getActivities(@User() user: UserDto, @Query() dto: GetActivityReqDto) {
    await this.activityValidation.getActivities(user, dto);
    const { paginationTotalCount, activityContents } =
      await this.activityService.getActivities(user, dto);
    return new GeneralResponseDto()
      .setPaginationTotalCount(paginationTotalCount)
      .setData(activityContents);
  }

  @Get('types')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getActivityTypes(): Promise<GeneralResponseDto> {
    const activityTypes = this.activityService.getActivityTypes();
    return new GeneralResponseDto().setData(activityTypes);
  }
}
