import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsN<PERSON>ber,
  IsO<PERSON>al,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ActivityTabEnum } from '../../enums/activity-tab.enum';

export class GetActivityReqDto {
  @ApiProperty({ type: String, enum: ActivityTabEnum })
  @IsEnum(ActivityTabEnum)
  activityTab: ActivityTabEnum;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsOptional()
  subsidiaryId?: number;

  @ApiProperty({ type: Number, required: false, description: 'starts with 0' })
  @Transform(({ value }) => +value)
  @IsNumber({ maxDecimalPlaces: 0 })
  @IsOptional()
  @Min(0)
  page?: number;

  @ApiProperty({ type: Number, required: false })
  @Transform(({ value }) => +value)
  @IsNumber({ maxDecimalPlaces: 0 })
  @IsOptional()
  @Min(0)
  @Max(50)
  limit?: number;

  @ApiProperty({ type: Boolean, required: false })
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  specialForMe?: boolean;

  @ApiProperty({
    type: String,
    required: false,
    description: '2024-02-15T00:00:00',
  })
  @Transform(({ value }) => new Date(value))
  @IsDate()
  @IsOptional()
  startDate?: Date;

  @ApiProperty({
    type: String,
    required: false,
    description: '2024-02-15T23:59:59',
  })
  @Transform(({ value }) => new Date(value))
  @IsDate()
  @IsOptional()
  endDate?: Date;
}
