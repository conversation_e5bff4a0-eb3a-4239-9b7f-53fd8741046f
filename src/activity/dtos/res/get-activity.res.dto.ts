import {
  Is<PERSON>rray,
  IsDateString,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ActivityActionDto } from '../req/activity-action.dto';
import { UserDataDto } from '../../../common/dtos/user-data.dto';

export class GetActivityResDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  id: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  organizationId: number;

  @ApiProperty({ type: String })
  @IsString()
  content: string;

  @ApiProperty({ type: ActivityActionDto, isArray: true })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ActivityActionDto)
  actions: ActivityActionDto[];

  @ApiProperty({ type: String })
  @IsDateString()
  createdAt: Date;

  @ApiProperty({ type: UserDataDto })
  @ValidateNested({ each: true })
  @Type(() => UserDataDto)
  createdUser: UserDataDto;
}
