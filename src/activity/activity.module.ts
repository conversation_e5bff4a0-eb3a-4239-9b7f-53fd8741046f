import { Module } from '@nestjs/common';
import { ActivityController } from './activity.controller';
import { ActivityService } from './activity.service';
import { CommonModule } from '../common/common.module';
import { NotificationModule } from '../notification/notification.module';
import { ActivityGateway } from './activity.gateway';
import { OrganizationModule } from '../organization/organization.module';
import { ActivityValidation } from './activity.validation';

@Module({
  imports: [NotificationModule, OrganizationModule, CommonModule],
  controllers: [ActivityController],
  providers: [ActivityService, ActivityValidation, ActivityGateway],
  exports: [ActivityService],
})
export class ActivityModule {}
