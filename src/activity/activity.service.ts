import { Injectable } from '@nestjs/common';
import { PrismaService } from '../common/services/prisma.service';
import { Prisma, PrismaClient } from '@prisma/client';
import { ActivityEnum } from './enums/activity.enum';
import { UtilService } from '../common/services/util.service';
import { NotificationService } from '../notification/notification.service';
import { UserDto } from '../common/dtos/user.dto';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { GetActivityReqDto } from './dtos/req/get-activity.req.dto';
import { ActivityGateway } from './activity.gateway';
import { ActivityUtilService } from '../common/services/activity.util.service';
import { GetActivityResDto } from './dtos/res/get-activity.res.dto';
import { ActivityTabType } from './types/activity-tab.type';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../common/dtos/user-data.dto';
import { CreateActivityArgType } from './types/create-activity-arg.type';

@Injectable()
export class ActivityService {
  constructor(
    private prismaService: PrismaService,
    private activityGateway: ActivityGateway,
    private notificationService: NotificationService,
    private activityUtilService: ActivityUtilService,
    private utilService: UtilService,
  ) {}

  async getActivities(user: UserDto, dto: GetActivityReqDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const activityWhere: Prisma.ActivityWhereInput = {
      organizationId: dto.subsidiaryId,
      activityType: { in: ActivityTabType[dto.activityTab] },
      effectedUserIds: dto.specialForMe
        ? { array_contains: user.id }
        : undefined,
      AND:
        dto.startDate && dto.endDate
          ? [
              { createdAt: { gte: dto.startDate } },
              { createdAt: { lte: dto.endDate } },
            ]
          : undefined,
    };
    const totalActivityCount = await this.prismaService.activity.count({
      where: activityWhere,
    });
    const activities = await this.prismaService.activity.findMany({
      skip: this.utilService.getPaginationSkip(dto.page, dto.limit),
      take: this.utilService.getPaginationTake(dto.limit),
      where: activityWhere,
      orderBy: { createdAt: 'desc' },
      include: { createdUser: true },
    });
    for (const activity of activities) {
      activity['createdUserInfo'] = plainToInstance(
        UserDataDto,
        activity.createdUser,
        { excludeExtraneousValues: true },
      );
      delete activity.createdUser;
    }
    const activityList = await this.activityUtilService.getActivityList(
      user.id,
      dto.subsidiaryId,
      activities,
    );
    const activityContents: GetActivityResDto[] = [];
    for (const activity of activities) {
      activityContents.push({
        id: activity.id,
        organizationId: activity.organizationId,
        content: activityList[activity.id].content,
        actions: activityList[activity.id].actions,
        createdAt: activity.createdAt,
        createdUser: activity['createdUserInfo'],
      });
    }
    return {
      paginationTotalCount: totalActivityCount,
      activityContents: activityContents,
    };
  }

  getActivityTypes() {
    return this.utilService.enumToArray(ActivityEnum);
  }

  async createActivity(
    tx: Omit<PrismaClient, ITXClientDenyList>,
    user: UserDto,
    args: CreateActivityArgType[],
    options?: { withoutNotification?: boolean; sendNotificationToMe?: boolean },
  ): Promise<void> {
    if (args.length == 0) {
      return;
    }
    const createActivityData: Prisma.ActivityCreateManyInput[] = [];
    const createdAt = new Date();
    args.map((arg) => {
      if (!Array.isArray(arg.targetUserIds)) {
        arg.targetUserIds = this.utilService.jsonToNumberList(
          arg.targetUserIds,
        );
      }
      if (!Array.isArray(arg.effectedUserIds)) {
        arg.effectedUserIds = this.utilService.jsonToNumberList(
          arg.effectedUserIds,
        );
      }
      if (arg.targetUserIds?.length > 0) {
        arg.targetUserIds = [...new Set(arg.targetUserIds)];
      }
      if (arg.effectedUserIds?.length > 0) {
        arg.effectedUserIds = [...new Set(arg.effectedUserIds)];
      }
      createActivityData.push({
        createdUserId: user.id,
        organizationId: user.organizationId,
        activityType: arg.activityType,
        targetUserIds:
          arg.targetUserIds?.length > 0 ? arg.targetUserIds : undefined,
        effectedUserIds:
          arg.effectedUserIds?.length > 0 ? arg.effectedUserIds : undefined,
        payload: arg.payload,
        createdAt: createdAt,
      });
    });
    await tx.activity.createMany({
      data: createActivityData,
    });
    const activityTypes = [...new Set(args.map((arg) => arg.activityType))];
    const activities = await tx.activity.findMany({
      where: {
        createdUserId: user.id,
        organizationId: user.organizationId,
        activityType: { in: activityTypes },
        createdAt: createdAt,
      },
      include: { createdUser: true },
    });
    activities.map((activity) => {
      activity['createdUserInfo'] = plainToInstance(
        UserDataDto,
        activity.createdUser,
        { excludeExtraneousValues: true },
      );
      delete activity.createdUser;
    });
    // TODO: activityListData'yı yani activity.util.service'teki fonksiyonları burada tek seferde çağır.
    // TODO: aşağıdaki fonksiyonlarda şu an 2 kere çağırıyor
    if (!options?.withoutNotification) {
      const notificationContents =
        await this.notificationService.createNotifications(
          tx,
          user,
          activities,
          { sendNotificationToMe: options?.sendNotificationToMe },
        );
      await this.notificationService.sendNotificationGateway(
        tx,
        user.organizationId,
        notificationContents,
      );
    }
    await this.activityGateway.sendActivity(user, activities, { tx: tx });
  }
}
