import { Logger } from '@nestjs/common';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { UserService } from '../services/user.service';
import { UserDto } from '../dtos/user.dto';
import { UtilService } from '../services/util.service';

@WebSocketGateway({
  cors: { origin: process.env.CORS_ORIGINS.split('; ') },
})
export class SocketIoGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  private readonly logger = new Logger(SocketIoGateway.name);
  @WebSocketServer() io: Server;

  constructor(
    private userService: UserService,
    private utilService: UtilService,
  ) {}

  afterInit() {
    this.logger.log('Initialized');
  }

  async handleConnection(client: Socket) {
    const user = await this.getUser(client);
    if (!user) {
      client.disconnect(true);
      return;
    }
    client.join(this.utilService.socketRoom(user.id, user.organizationId));
    client.emit('info', {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      organizationId: user.organizationId,
    });
    const { sockets } = this.io.sockets;
    this.logger.debug(
      `CONNECTED - userId: ${user.id}, organizationId: ${user.organizationId}, clientId: ${client.id}, totalConnect: ${sockets.size}`,
    );
  }

  async handleDisconnect(client: Socket) {
    const user = await this.getUser(client);
    if (!user) {
      client.disconnect(true);
      return;
    }
    const { sockets } = this.io.sockets;
    this.logger.debug(
      `DISCONNECTED - userId: ${user.id}, organizationId: ${user.organizationId}, clientId: ${client.id}, totalConnect: ${sockets.size}`,
    );
  }

  private async getUser(client: Socket): Promise<UserDto> {
    let user: UserDto;
    try {
      user = await this.userService.checkUser(
        client.handshake.headers.authorization,
      );
    } catch (error) {
      client.emit('info', { error: 'UNAUTHENTICATED' });
      this.logger.error(`UNAUTHENTICATED - clientId: ${client.id}`);
      this.logger.error(error);
      return;
    }
    return user;
  }

  sendEmit(room: string, event: string, data: any) {
    this.io.to(room).emit(event, data);
  }
}
