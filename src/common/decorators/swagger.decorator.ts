import { applyDecorators, Type } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import { GeneralResponseDto } from '../dtos/general-response.dto';

export function ApiOkResponseWithDto<TModel extends Type<unknown>>(
  model: TModel = undefined,
  isArray = true,
) {
  if (!model) {
    return applyDecorators(ApiOkResponse({ type: GeneralResponseDto }));
  }

  return applyDecorators(
    ApiExtraModels(model),
    ApiExtraModels(GeneralResponseDto),
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(GeneralResponseDto) },
          {
            properties: {
              data: isArray
                ? {
                    type: 'array',
                    items: { $ref: getSchemaPath(model) },
                  }
                : { $ref: getSchemaPath(model) },
            },
          },
        ],
      },
    }),
  );
}
