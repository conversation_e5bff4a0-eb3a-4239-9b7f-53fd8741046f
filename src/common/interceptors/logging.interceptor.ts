import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);
  private readonly reqMax = 1000;
  private readonly respMax = 1000;

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const http = context.switchToHttp();
    const req = http.getRequest<any>();
    const res = http.getResponse<any>();

    const method = req.method;
    const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
    const body = req.body ?? {};
    const query = req.query ?? {};
    const started = Date.now();

    this.logger.log(`➡️  ${method} - ${url} - ${req.user?.id}`);

    if (Object.keys(query).length) {
      this.logger.debug(`Query: ${this.serializeForLog(query, this.reqMax)}`);
    }
    if (Object.keys(body).length) {
      this.logger.debug(`Body: ${this.serializeForLog(body, this.reqMax)}`);
    }

    let capturedBody: any;
    const originalJson = res.json?.bind(res);
    const originalSend = res.send?.bind(res);

    if (originalJson) {
      res.json = (payload: any) => {
        capturedBody = payload;
        return originalJson(payload);
      };
    }
    if (originalSend) {
      res.send = (payload: any) => {
        if (capturedBody === undefined) capturedBody = payload;
        return originalSend(payload);
      };
    }

    res.on('finish', () => {
      const ms = Date.now() - started;
      const status = res.statusCode;
      const logged = this.serializeForLog(capturedBody, this.respMax);

      this.logger.log(`⬅️  ${method} - ${url} - ${status} - ${ms}ms`);
      this.logger.debug(`Response: ${logged}`);
    });

    return next.handle();
  }

  private limitString(input: string, max: number): string {
    if (input.length <= max) return input;
    return input.slice(0, max) + `…(+truncated ${input.length - max} chars)`;
  }

  private safeJson(input: any): string {
    try {
      return JSON.stringify(input);
    } catch {
      return '[unserializable]';
    }
  }

  private serializeForLog(payload: any, max: number): string {
    try {
      if (Buffer.isBuffer(payload)) {
        const str = payload.toString('utf8');
        return this.limitString(str, max);
      }
      if (typeof payload === 'string') {
        return this.limitString(payload, max);
      }
      const str = this.safeJson(payload);
      return this.limitString(str, max);
    } catch {
      return '[unserializable]';
    }
  }
}
