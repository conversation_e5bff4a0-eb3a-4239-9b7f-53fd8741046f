import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { ErrorDto } from './exception-response.dto';

export class GeneralResponseDto<T = unknown> {
  @ApiProperty({ type: Boolean, default: true, required: true })
  @IsBoolean()
  status: boolean;

  @ApiProperty({ type: Object, isArray: true, required: false })
  @IsArray()
  @IsOptional()
  data?: T | T[];

  @ApiProperty({
    type: Object,
    required: false,
    example: { property: ['error message'] },
  })
  @IsArray()
  @IsOptional()
  errors?: { [key: string]: string[] };

  @ApiProperty({ type: String, isArray: true, required: false })
  @IsArray()
  @IsOptional()
  messages?: string[];

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  paginationTotalCount?: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  errorCode?: string;

  @ApiProperty({ type: Object, required: false })
  @IsObject()
  @IsOptional()
  options?: object;

  constructor(status = true) {
    this.status = status;
  }

  setData(data?: T | T[]) {
    if (!data) {
      return this;
    }
    if (Array.isArray(data)) {
      this.data = data.filter((d) => d);
    } else {
      this.data = data;
    }
    return this;
  }

  setErrors(errors?: ErrorDto[]) {
    if (!errors) {
      return this;
    }
    this.errors = {};
    for (const e of errors) {
      this.errors[e.property] = e.messages;
    }
    if (Object.values(errors).length == 0) {
      this.errors = undefined;
    }
    return this;
  }

  setMessages(messages?: string[]) {
    if (!messages) {
      return this;
    }
    if (messages.length > 0) {
      this.messages = messages;
    }
    return this;
  }

  setPaginationTotalCount(count: number) {
    this.paginationTotalCount = count;
    return this;
  }

  setErrorCode(errorCode: string) {
    this.errorCode = errorCode;
    return this;
  }

  setOptions(options: object) {
    this.options = options;
    return this;
  }
}
