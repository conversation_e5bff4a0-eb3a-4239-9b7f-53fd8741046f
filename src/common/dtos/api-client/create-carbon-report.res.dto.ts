import { Expose, Transform, Type } from 'class-transformer';

class CreateCarbonReportValueResDto {
  @Expose({ name: 'numeric_value' })
  numericValue?: number;

  @Expose({ name: 'text_value' })
  textValue?: string;

  @Expose({ name: 'group_value_name' })
  groupValueName?: string;
}

export class CreateCarbonReportResDto {
  @Expose()
  id: number;

  @Expose({ name: 'facility_id' })
  facilityId: number;

  @Expose()
  year: number;

  @Expose({ name: 'months' })
  @Transform(({ value }) => String(value).split(',').map(Number))
  monthIds: number[];

  @Expose()
  description?: string;

  @Expose({ name: 'files' })
  @Transform(({ value }) => value.map((f) => f.id))
  fileIds: number[];

  @Expose({ name: 'result' })
  @Type(() => CreateCarbonReportValueResDto)
  values: CreateCarbonReportValueResDto[];
}
