import {
  IsBoolean,
  IsDate,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';
import { Expose, Transform } from 'class-transformer';

export class UserDataDto {
  @Expose()
  @IsNumber()
  id: number;

  @Expose({ groups: ['userProfile'] })
  @IsString()
  name: string;

  @Expose({ groups: ['userProfile'] })
  @IsString()
  surname: string;

  @Expose()
  @Transform(({ obj }) => {
    if (!obj.name && !obj.surname) {
      return undefined;
    }
    let fullName = obj.name ?? '';
    if (obj.name && obj.surname) {
      fullName += ' ' + obj.surname;
    } else if (!obj.name && obj.surname) {
      fullName += obj.surname;
    }
    return fullName;
  })
  @IsString()
  fullName: string;

  @Expose()
  @IsString()
  email: string;

  @Expose({ groups: ['contact'] })
  @Transform(({ obj }) => obj.phone ?? undefined)
  @IsString()
  @IsOptional()
  phone?: string;

  @Expose()
  @Transform(({ obj }) => obj.image ?? undefined)
  @IsString()
  @IsOptional()
  image?: string;

  @Expose({ groups: ['sensitiveField'] })
  @Transform(({ obj }) => obj.isOtp ?? undefined)
  @IsBoolean()
  @IsOptional()
  isOtp?: boolean;

  @Expose({ groups: ['sensitiveField'] })
  @Transform(({ obj }) => obj.lastActivity ?? undefined)
  @IsDate()
  @IsOptional()
  lastActivity?: Date;

  @Expose({ groups: ['organization'] })
  @Transform(({ obj }) =>
    obj.userOrganizations?.length > 0 && obj.userOrganizations[0].organization
      ? obj.userOrganizations[0].organization.name
      : undefined,
  )
  @IsString()
  @IsOptional()
  organizationName?: string;

  @Expose({ groups: ['userProfile'] })
  @Transform(({ obj }) =>
    obj.userOrganizations?.length > 0
      ? obj.userOrganizations[0].roleId
      : undefined,
  )
  @IsNumber()
  @IsOptional()
  roleId?: number;

  @Expose()
  @Transform(({ obj }) =>
    obj.userOrganizations?.length > 0
      ? (obj.userOrganizations[0].title ?? undefined)
      : undefined,
  )
  @IsString()
  @IsOptional()
  title?: string;

  @Expose()
  @Transform(({ obj }) => {
    if (!obj.userOrganizations) {
      return undefined;
    }
    return obj.userOrganizations.length > 0 ? undefined : true;
  })
  @IsBoolean()
  @IsOptional()
  isFired?: boolean;
}
