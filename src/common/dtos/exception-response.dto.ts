import { Expose, plainToInstance, Transform } from 'class-transformer';
import {
  <PERSON>Array,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { HttpException } from '@nestjs/common';

export class ExceptionResponseDto {
  @Expose({ name: 'response' })
  @Transform(({ value }) =>
    Array.isArray(value.message) ? value.message : [value.message],
  )
  @IsArray()
  message: unknown[];

  @Expose()
  @IsString()
  error: string;

  @Expose()
  @IsNumber()
  statusCode: number;

  @Expose()
  @IsString()
  @IsOptional()
  errorCode?: string;

  @Expose()
  @IsObject()
  @IsOptional()
  options?: object;

  static transformToDto(data: HttpException): ExceptionResponseDto {
    const dto = plainToInstance(ExceptionResponseDto, data, {
      excludeExtraneousValues: true,
    });
    dto.errorCode = data?.cause?.['errorCode'] as string;
    dto.options = data?.cause?.['options'] as object;
    return dto;
  }
}

export class ErrorDto {
  @ApiProperty({ type: String })
  @IsString()
  property: string;

  @ApiProperty({ type: String, isArray: true })
  @IsArray()
  messages: string[];
}
