import {
  IsBoolean,
  IsEnum,
  IsN<PERSON>ber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { Permission, Role } from '@prisma/client';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';

export class UserDto {
  @IsNumber()
  id: number;

  @IsString()
  fullName: string;

  @IsString()
  email: string;

  @IsString()
  @IsOptional()
  image?: string;

  @IsNumber()
  @IsOptional()
  timezone?: number;

  @IsNumber()
  organizationId: number;

  @IsEnum(OrganizationTypeEnum)
  organizationType: OrganizationTypeEnum;

  @IsObject()
  role: Role;

  @IsObject({ each: true })
  permissions: Permission[];

  @IsString()
  jwtId: string;

  @IsString()
  bearerToken: string;

  @IsBoolean()
  isAdmin: boolean;
}
