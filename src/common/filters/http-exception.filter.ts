import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Response } from 'express';
import { GeneralResponseDto } from '../dtos/general-response.dto';
import { ErrorDto, ExceptionResponseDto } from '../dtos/exception-response.dto';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const exceptionResponse = ExceptionResponseDto.transformToDto(exception);

    const errors: ErrorDto[] = [];
    const messages: string[] = [];
    if (exceptionResponse.message?.length > 0) {
      if (typeof exceptionResponse.message[0] === 'string') {
        messages.push(...exceptionResponse.message.map((m) => m as string));
      } else if (typeof exceptionResponse.message[0] === 'object') {
        errors.push(...exceptionResponse.message.map((m) => m as ErrorDto));
      }
    } else if (typeof exceptionResponse === 'string') {
      messages.push(exceptionResponse);
    }

    this.logger.error(exception);
    response
      .status(
        [
          HttpStatus.UNAUTHORIZED,
          HttpStatus.FORBIDDEN,
          HttpStatus.NOT_ACCEPTABLE,
        ].includes(exception.getStatus())
          ? exception.getStatus()
          : HttpStatus.OK,
      )
      .json(
        new GeneralResponseDto(false)
          .setErrors(errors)
          .setMessages(messages)
          .setErrorCode(exceptionResponse.errorCode)
          .setOptions(exceptionResponse.options),
      );
  }
}
