import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Response } from 'express';
import { GeneralResponseDto } from '../dtos/general-response.dto';
import { Prisma } from '@prisma/client';

@Catch(Prisma.PrismaClientKnownRequestError)
export class PrismaExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(PrismaExceptionFilter.name);

  catch(exception: Prisma.PrismaClientKnownRequestError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    if (exception.code == 'P2025') {
      return response
        .status(HttpStatus.OK)
        .json(
          new GeneralResponseDto(false).setMessages([
            `${exception.meta?.modelName ?? 'Data'} not found`,
          ]),
        );
    } else {
      this.logger.error(exception);
      return response
        .status(HttpStatus.OK)
        .json(
          new GeneralResponseDto(false).setMessages([
            'Beklenmedik bir sorun oluştu.',
          ]),
        );
    }
  }
}
