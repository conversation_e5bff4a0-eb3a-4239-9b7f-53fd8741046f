import { Controller, Get } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { GeneralResponseDto } from '../dtos/general-response.dto';
import { CommonService } from '../services/common.service';
import { User } from '../decorators/auth.decorator';
import { UserDto } from '../dtos/user.dto';

@ApiTags('Commons')
@Controller('commons')
export class CommonController {
  constructor(private commonService: CommonService) {}

  @Get('countries')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCountries(@User() user: UserDto): Promise<GeneralResponseDto> {
    const countries = await this.commonService.getCountries();
    return new GeneralResponseDto().setData(countries);
  }

  @Get('currencies')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrencies(@User() user: UserDto): Promise<GeneralResponseDto> {
    const currencies = await this.commonService.getCurrencies();
    return new GeneralResponseDto().setData(currencies);
  }
}
