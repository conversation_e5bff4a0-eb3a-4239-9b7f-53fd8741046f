import { Module } from '@nestjs/common';
import { PrismaService } from './services/prisma.service';
import { UserService } from './services/user.service';
import { UtilService } from './services/util.service';
import { JwtModule } from '@nestjs/jwt';
import { ApiClientService } from './services/api-client.service';
import { HttpModule } from '@nestjs/axios';
import { CommonController } from './controllers/common.controller';
import { CommonService } from './services/common.service';
import { SocketIoGateway } from './gateways/socket-io.gateway';
import { SituationUtilService } from './services/situation.util.service';
import { ActivityUtilService } from './services/activity.util.service';
import { OrganizationUtilService } from './services/organization.util.service';
import { AnalysisUtilService } from './services/analysis.util.service';
import { PermissionUtilService } from './services/permission.util.service';

@Module({
  imports: [HttpModule, JwtModule],
  controllers: [CommonController],
  providers: [
    ActivityUtilService,
    AnalysisUtilService,
    ApiClientService,
    CommonService,
    OrganizationUtilService,
    PermissionUtilService,
    PrismaService,
    SituationUtilService,
    UserService,
    UtilService,
    SocketIoGateway,
  ],
  exports: [
    ActivityUtilService,
    AnalysisUtilService,
    ApiClientService,
    OrganizationUtilService,
    PermissionUtilService,
    PrismaService,
    SituationUtilService,
    UserService,
    UtilService,
    SocketIoGateway,
  ],
})
export class CommonModule {}
