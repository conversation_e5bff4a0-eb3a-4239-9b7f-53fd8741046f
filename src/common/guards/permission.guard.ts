import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Scope,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserDto } from '../dtos/user.dto';
import { PERMISSION_KEY } from '../decorators/permission.decorator';
import { IS_PUBLIC_KEY } from '../decorators/auth.decorator';

@Injectable({ scope: Scope.REQUEST })
export class PermissionGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(ctx: ExecutionContext): boolean {
    const isPublic = this.reflector.get<boolean>(
      IS_PUBLIC_KEY,
      ctx.getHandler(),
    );
    if (isPublic) {
      return true;
    }
    const permissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSION_KEY,
      [ctx.getHandler(), ctx.getClass()],
    );
    if (!permissions) {
      return true;
    }
    const request = ctx.switchToHttp().getRequest();
    const user: UserDto = request.user;
    const isAuth = user.permissions.some((p) => permissions.includes(p.key));
    if (!isAuth) {
      throw new HttpException('Yetkisiz işlem', HttpStatus.FORBIDDEN);
    }
    return true;
  }
}
