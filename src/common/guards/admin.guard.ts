import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  Scope,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { IS_ADMIN_KEY } from '../decorators/admin.decorator';
import { UserDto } from '../dtos/user.dto';

@Injectable({ scope: Scope.REQUEST })
export class AdminGuard implements CanActivate {
  private readonly logger = new Logger(AdminGuard.name);

  constructor(private reflector: Reflector) {}

  async canActivate(ctx: ExecutionContext): Promise<boolean> {
    try {
      const requiresAdmin = this.reflector.getAllAndOverride<boolean>(
        IS_ADMIN_KEY,
        [ctx.getHandler(), ctx.getClass()],
      );

      if (!requiresAdmin) {
        return true;
      }

      const request = ctx.switchToHttp().getRequest();
      const user: UserDto = request.user;
      if (!user || !user.isAdmin) {
        throw new HttpException('unauthorized', HttpStatus.FORBIDDEN);
      }

      return true;
    } catch (error) {
      this.logger.error(`${error.message}`);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('unauthenticated', HttpStatus.FORBIDDEN);
    }
  }
}
