import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Scope,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/role.decorator';
import { UserDto } from '../dtos/user.dto';
import { IS_PUBLIC_KEY } from '../decorators/auth.decorator';

@Injectable({ scope: Scope.REQUEST })
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(ctx: ExecutionContext): boolean {
    const isPublic = this.reflector.get<boolean>(
      IS_PUBLIC_KEY,
      ctx.getHandler(),
    );
    if (isPublic) {
      return true;
    }
    const roles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      ctx.getHandler(),
      ctx.getClass(),
    ]);
    if (!roles) {
      return true;
    }
    const request = ctx.switchToHttp().getRequest();
    const user: UserDto = request.user;
    if (!user.role || !roles.includes(user.role.key)) {
      throw new HttpException('Yetkisiz işlem', HttpStatus.FORBIDDEN);
    }
    return true;
  }
}
