import {
  Injectable,
  CanActivate,
  ExecutionContext,
  HttpException,
  Scope,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../decorators/auth.decorator';
import { UserService } from '../services/user.service';

@Injectable({ scope: Scope.REQUEST })
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private reflector: Reflector,
    private userService: UserService,
  ) {}

  async canActivate(ctx: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.get<boolean>(
      IS_PUBLIC_KEY,
      ctx.getHandler(),
    );
    if (isPublic) {
      return true;
    }
    try {
      const request = ctx.switchToHttp().getRequest();
      const user = await this.userService.checkUser(
        request.headers.authorization,
      );
      request.user = user;
      this.userService
        .updateLastActivity(user.id)
        .catch((err) => this.logger.error(err));
      return true;
    } catch (error) {
      console.error(error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('unauthenticated', HttpStatus.UNAUTHORIZED);
    }
  }
}
