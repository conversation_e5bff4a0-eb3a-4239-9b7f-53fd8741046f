import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Scope,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserDto } from '../dtos/user.dto';
import { ORGANIZATION_TYPES_KEY } from '../decorators/organization-type.decorator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { IS_PUBLIC_KEY } from '../decorators/auth.decorator';

@Injectable({ scope: Scope.REQUEST })
export class OrganizationTypeGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(ctx: ExecutionContext): boolean {
    const isPublic = this.reflector.get<boolean>(
      IS_PUBLIC_KEY,
      ctx.getHandler(),
    );
    if (isPublic) {
      return true;
    }
    const organizationTypes = this.reflector.getAllAndOverride<
      OrganizationTypeEnum[]
    >(ORGANIZATION_TYPES_KEY, [ctx.getHandler(), ctx.getClass()]);
    if (!organizationTypes) {
      return true;
    }
    const request = ctx.switchToHttp().getRequest();
    const user: UserDto = request.user;
    if (!organizationTypes.includes(user.organizationType)) {
      throw new HttpException('Yetkisiz şirket türü', HttpStatus.FORBIDDEN);
    }
    return true;
  }
}
