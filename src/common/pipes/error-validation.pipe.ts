import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
  ValidationPipe,
  ValidationError,
} from '@nestjs/common';
import { ErrorDto } from '../dtos/exception-response.dto';

@Injectable()
export class ErrorValidationPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    const validationPipe = new ValidationPipe({
      transform: true,
      exceptionFactory: (errors) => {
        const errorsDto = this.mappingErrors(errors);
        return new BadRequestException(errorsDto);
      },
    });
    return validationPipe.transform(value, metadata);
  }

  private mappingErrors(
    errors: ValidationError[],
    parentProperty?: string,
  ): ErrorDto[] {
    const errorsDto: ErrorDto[] = [];
    for (const error of errors) {
      const property =
        (parentProperty ? parentProperty + '.' : '') + error.property;
      if (error.constraints) {
        errorsDto.push({
          property: property,
          messages: Object.values(error.constraints),
        });
      }
      if (error.children) {
        errorsDto.push(...this.mappingErrors(error.children, property));
      }
    }
    return errorsDto;
  }
}
