import {
  CapitalReport,
  ChainReport,
  FinancialReport,
  GovernanceReport,
  ImpactReport,
  IndustryReport,
  MetricReport,
  OpinionReport,
  Prisma,
  RiskReport,
  SituationReport,
  StrategyReport,
  TendencyReport,
  TrendReport,
} from '@prisma/client';

export interface ActivityListDataType {
  situationReports: SituationReport[];
  situationHelps: Prisma.SituationHelpGetPayload<{
    include: { helpedUser: true };
  }>[];
  trendReports: TrendReport[];
  trendHelps: Prisma.TrendHelpGetPayload<{
    include: { trend: true; helpedUser: true };
  }>[];
  riskReports: RiskReport[];
  riskHelps: Prisma.RiskHelpGetPayload<{
    include: { risk: true; helpedUser: true };
  }>[];
  chainReports: ChainReport[];
  chainHelps: Prisma.ChainHelpGetPayload<{
    include: { chain: true; helpedUser: true };
  }>[];
  capitalReports: CapitalReport[];
  capitalHelps: Prisma.CapitalHelpGetPayload<{
    include: { capital: true; helpedUser: true };
  }>[];
  financialReports: FinancialReport[];
  financialHelps: Prisma.FinancialHelpGetPayload<{
    include: { report: true; helpedUser: true };
  }>[];
  financialReviews: Prisma.FinancialReviewGetPayload<{
    include: { report: true; reviewerUser: true };
  }>[];
  financialSignificances: Prisma.FinancialSignificanceGetPayload<{
    include: { report: true };
  }>[];
  impactReports: ImpactReport[];
  impactHelps: Prisma.ImpactHelpGetPayload<{
    include: { report: true; helpedUser: true };
  }>[];
  impactReviews: Prisma.ImpactReviewGetPayload<{
    include: { report: true; reviewerUser: true };
  }>[];
  impactSignificances: Prisma.ImpactSignificanceGetPayload<{
    include: { report: true };
  }>[];
  industryReports: IndustryReport[];
  industryHelps: Prisma.IndustryHelpGetPayload<{
    include: { report: true; helpedUser: true };
  }>[];
  opinionReports: OpinionReport[];
  opinionHelps: Prisma.OpinionHelpGetPayload<{
    include: { report: true; stakeholderUser: true };
  }>[];
  tendencyReports: TendencyReport[];
  tendencyHelps: Prisma.TendencyHelpGetPayload<{
    include: { report: true; helpedUser: true };
  }>[];
  strategyReports: StrategyReport[];
  strategyHelps: Prisma.StrategyHelpGetPayload<{
    include: { strategy: true; helpedUser: true };
  }>[];
  governanceReports: GovernanceReport[];
  metricReports: MetricReport[];
  metricHelps: Prisma.MetricHelpGetPayload<{
    include: { metric: true; helpedUser: true };
  }>[];
  metricSupervisors: Prisma.MetricSupervisorGetPayload<{
    include: { metric: true; profile: { include: { user: true } } };
  }>[];
}
