import { Injectable } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { PrismaClient, SituationReport } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';

@Injectable()
export class SituationUtilService {
  constructor(private prismaService: PrismaService) {}

  async getLastReport(
    organizationId: number,
    options?: {
      isCompleted?: boolean;
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<SituationReport> {
    const dbService = options?.tx ?? this.prismaService;
    const report = await dbService.situationReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }
}
