import { Injectable } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { UserDto } from '../dtos/user.dto';
import { PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { SituationUtilService } from './situation.util.service';

@Injectable()
export class AnalysisUtilService {
  constructor(
    private prismaService: PrismaService,
    private situationUtilService: SituationUtilService,
  ) {}

  async getStages(
    user: UserDto,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ) {
    const situationIsComplete =
      !!(await this.situationUtilService.getLastReport(user.organizationId, {
        isCompleted: true,
        tx: options?.tx,
      }));
    let trendIsComplete = false;
    let riskIsComplete = false;
    if (situationIsComplete) {
      trendIsComplete = await this.trendIsComplete(user, { tx: options?.tx });
      if (trendIsComplete) {
        riskIsComplete = await this.riskIsComplete(user, { tx: options?.tx });
      }
    }
    return {
      situationIsComplete: situationIsComplete,
      trendIsComplete: trendIsComplete,
      riskIsComplete: riskIsComplete,
    };
  }

  private async trendIsComplete(
    user: UserDto,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ): Promise<boolean> {
    const dbService = options?.tx ?? this.prismaService;
    const report = await dbService.trendReport.findFirst({
      where: { organizationId: user.organizationId, riskReports: { some: {} } },
    });
    return !!report;
  }

  private async riskIsComplete(
    user: UserDto,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ): Promise<boolean> {
    const dbService = options?.tx ?? this.prismaService;
    const report = await dbService.riskReport.findFirst({
      where: { organizationId: user.organizationId, isCompleted: true },
    });
    return !!report;
  }
}
