import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UserDto } from '../dtos/user.dto';
import { AffiliateTypeEnum } from '../../organization/enums/affiliate-type.enum';

@Injectable()
export class OrganizationUtilService {
  constructor(private prismaService: PrismaService) {}

  async getOrganizationUserIds(
    user: UserDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<number[]> {
    const dbService = options?.tx ?? this.prismaService;
    const userIds = (
      await dbService.user.findMany({
        where: {
          userOrganizations: { some: { organizationId: user.organizationId } },
        },
      })
    ).map((u) => u.id);
    return userIds;
  }

  getAffiliateText(affiliateType: AffiliateTypeEnum): string {
    if (affiliateType == AffiliateTypeEnum.AFFILIATE) {
      return 'İştirak';
    } else if (affiliateType == AffiliateTypeEnum.PARTNER) {
      return 'Bağlı Ortak';
    } else if (affiliateType == AffiliateTypeEnum.INVESTMENT) {
      return 'Yatırım Ortağı';
    } else {
      throw new BadRequestException('Geçeriz alt şirket türü.');
    }
  }
}
