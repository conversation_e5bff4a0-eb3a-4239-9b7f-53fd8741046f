import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { UserDto } from '../dtos/user.dto';

@Injectable()
export class PermissionUtilService {
  constructor(private prismaService: PrismaService) {}

  async whoCanSee({
    user,
    type,
  }: {
    user: UserDto;
    type: 'chain';
  }): Promise<number[]> {
    const userIds = (
      await this.prismaService.user.findMany({
        where: {
          userOrganizations: { some: { organizationId: user.organizationId } },
        },
      })
    ).map((u) => u.id);
    switch (type) {
      case 'chain':
        return userIds;
      default:
        throw new BadRequestException('Invalid type');
    }
  }
}
