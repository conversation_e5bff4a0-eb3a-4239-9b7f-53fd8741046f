import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaService } from './prisma.service';

@Injectable()
export class UtilService {
  constructor(private prismaService: PrismaService) {}

  jsonToNumberList(value: Prisma.JsonValue): number[] {
    if (!value) {
      return [];
    }
    if (!Array.isArray(value)) {
      return [+value];
    }
    return value as number[];
  }

  jsonToStringList(value: Prisma.JsonValue): string[] {
    if (!value) {
      return [];
    }
    if (!Array.isArray(value)) {
      return [value as string];
    }
    return value as string[];
  }

  getRandomInteger(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  getRandomString(length: number): string {
    const characters: string =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let result = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charactersLength);
      result += characters.charAt(randomIndex);
    }
    return result;
  }

  enumToArray(enumObject: any): { key: string; value: any }[] {
    return Object.keys(enumObject)
      .filter((key) => !isNaN(Number(enumObject[key])))
      .map((key) => ({ key, value: enumObject[key] }));
  }

  enumToArrayForString(enumObject: any): { key: string; value: string }[] {
    return Object.keys(enumObject)
      .filter((key) => enumObject[key])
      .map((key) => ({ key, value: enumObject[key] }));
  }

  shuffle(array: string[]) {
    return structuredClone(array).sort(() => Math.random() - 0.5);
  }

  diffUserIds(oldUserIds?: Prisma.JsonValue, newUserIds?: number[]): number[] {
    if (!newUserIds || newUserIds.length == 0) {
      return null;
    }
    if (!oldUserIds && newUserIds && newUserIds.length > 0) {
      return newUserIds;
    }
    const oldUserIdList = this.jsonToNumberList(oldUserIds);
    const diff = newUserIds.filter((id) => !oldUserIdList.includes(id));
    return diff.length > 0 ? diff : null;
  }

  diffDates(date1?: Date, date2?: Date): number {
    return Math.abs(
      (date1 ? date1.getTime() : 0) - (date2 ? date2.getTime() : 0),
    );
  }

  moveToBeginningOfArray<T>(e: T, arr: T[]): void {
    const index = arr.findIndex((item) => item == e);
    if (index < 0) {
      return;
    }
    const element = structuredClone(arr[index]);
    arr.splice(index, 1);
    arr.unshift(element);
  }

  moveToBeginningOfObject(e: any, arr: object[], key: string): void {
    const index = arr.findIndex((item) => item[key] == e);
    if (index < 0) {
      return;
    }
    const element = structuredClone(arr[index]);
    arr.splice(index, 1);
    this.moveToBeginningOfObject(e, arr, key);
    arr.unshift(element);
  }

  getPaginationSkip(page?: number, limit?: number, sub: number = 0): number {
    return page === undefined || limit === undefined
      ? undefined
      : Math.max(0, page * limit - sub);
  }

  getPaginationTake(limit?: number, sub: number = 0): number {
    return limit === undefined ? undefined : Math.max(0, limit - sub);
  }

  dateFilter(
    startDate: Date,
    endDate: Date,
    options?: { startField?: string; endField?: string },
  ) {
    return [
      {
        [options?.startField ?? 'startDate']: { lte: startDate },
        [options?.endField ?? 'endDate']: { gte: startDate },
      },
      {
        [options?.startField ?? 'startDate']: { lte: endDate },
        [options?.endField ?? 'endDate']: { gte: endDate },
      },
      {
        [options?.startField ?? 'startDate']: { gte: startDate },
        [options?.endField ?? 'endDate']: { lte: endDate },
      },
    ];
  }

  removeElementFromArr<T>(e: T, arr: T[]): T[] {
    return arr.filter((item) => item != e);
  }

  slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/^\s+|\s+$/g, '')
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-');
  }

  socketRoom(userId: number, organizationId: number): string {
    return `user-${userId}-organization-${organizationId}`;
  }

  numberFormat(number: number, decimal: number = 4): string {
    return number.toLocaleString('tr-TR', {
      minimumFractionDigits: decimal,
      maximumFractionDigits: decimal,
    });
  }

  groupByUserId<T extends { userId: number }>(arr: T[]): Record<number, T[]> {
    return arr.reduce(
      (groups, item) => {
        const { userId } = item;
        if (!groups[userId]) {
          groups[userId] = [];
        }
        groups[userId].push(item);
        return groups;
      },
      {} as Record<number, T[]>,
    );
  }
}
