import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { AxiosRequestConfig, AxiosResponse } from 'axios';
import { CreateFacilityDto } from '../../carbon/dtos/create-facility.dto';
import { CreateReportDto } from '../../carbon/dtos/create-report.dto';
import { CreateIsoStandardDto } from '../../carbon/dtos/create-iso-standard.dto';
import { Organization } from '@prisma/client';
import { UserDto } from '../dtos/user.dto';
import { CreateReportResType } from '../types/api-client.type';
import { plainToInstance } from 'class-transformer';
import { CreateCarbonReportResDto } from '../dtos/api-client/create-carbon-report.res.dto';

@Injectable()
export class ApiClientService {
  private readonly CARBON_URL: string;
  private readonly CONFIG: AxiosRequestConfig;
  private readonly logger = new Logger(ApiClientService.name);

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
  ) {
    this.CARBON_URL = this.configService.getOrThrow<string>('CARBON_URL');
    this.CONFIG = {
      headers: {
        'SUPER-ADMIN-EMAIL':
          this.configService.getOrThrow<string>('SUPER_ADMIN_EMAIL'),
        'SUPER-ADMIN-PASSWORD': this.configService.getOrThrow<string>(
          'SUPER_ADMIN_PASSWORD',
        ),
      },
    };
  }

  async getReports(
    companyIds: number[],
    year?: number,
  ): Promise<{
    years: number[];
    reports: {
      companyId: number;
      facilityId: number;
      facilityName: string;
      totalGas: string;
      totalGasDouble: number;
    }[];
  }> {
    if (companyIds.length == 0) {
      return { years: [], reports: [] };
    }
    const companyIdsStr = [...new Set(companyIds)].join(',');
    let url =
      this.CARBON_URL + `/super-admin/esg/reports?companyIds=${companyIdsStr}`;
    if (year) {
      url += `&year=${year}`;
    }
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.get(url, this.CONFIG);
      if (res.data?.status && res.data?.data) {
        this.logger.log('GET', url, this.CONFIG, res.data);
        return res.data.data;
      } else {
        this.logger.error('GET', url, this.CONFIG, res.data);
        return { years: [], reports: [] };
      }
    } catch (error) {
      this.logger.error('GET', url, this.CONFIG, String(error), res?.data);
      return { years: [], reports: [] };
    }
  }

  async getSubsidiaryReportFiles(companyIds: number[]): Promise<
    {
      companyId: number;
      formName: string;
      email: string;
      files: object[];
      totalGas: string;
      totalGasDouble: number;
    }[]
  > {
    if (companyIds.length == 0) {
      return [];
    }
    companyIds = [...new Set(companyIds)];
    const url =
      this.CARBON_URL +
      '/super-admin/esg/reports/files?companyIds=' +
      companyIds.join(',');
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.get(url, this.CONFIG);
      if (res.data?.status && res.data?.data) {
        this.logger.log('GET', url, this.CONFIG, res.data);
        return res.data.data;
      } else {
        this.logger.error('GET', url, this.CONFIG, res.data);
        return [];
      }
    } catch (error) {
      this.logger.error('GET', url, this.CONFIG, String(error), res?.data);
      return [];
    }
  }

  async getFacilities(user: UserDto): Promise<
    {
      id: number;
      name: string;
      address: string;
      code: number;
    }[]
  > {
    const url = this.CARBON_URL + '/facilities';
    const config: AxiosRequestConfig = {
      headers: { Authorization: user.bearerToken },
    };
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.get(url, config);
      if (res.data?.status && res.data?.data) {
        this.logger.log('GET', url, config, res.data);
        return res.data.data;
      } else {
        this.logger.error('GET', url, config, res.data);
        return [];
      }
    } catch (error) {
      this.logger.error('GET', url, config, String(error), res?.data);
      return [];
    }
  }

  async getSubsidiaryFacilityCounts(companyIds: number[]): Promise<
    {
      companyId: number;
      facilityCount: number;
    }[]
  > {
    if (companyIds.length == 0) {
      return [];
    }
    const companyIdsStr = [...new Set(companyIds)].join(',');
    const url =
      this.CARBON_URL +
      `/super-admin/esg/facilities/counts?companyIds=${companyIdsStr}`;
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.get(url, this.CONFIG);
      if (res.data?.status && res.data?.data) {
        this.logger.log('GET', url, this.CONFIG, res.data);
        return res.data.data;
      } else {
        this.logger.error('GET', url, this.CONFIG, res.data);
        return [];
      }
    } catch (error) {
      this.logger.error('GET', url, this.CONFIG, String(error), res?.data);
      return [];
    }
  }

  async login(
    jwtId: string,
    expiredAt: Date,
    email: string,
    fullName: string,
    companyName: string,
    companyId?: number,
  ): Promise<{
    carbonUserId: number;
    carbonCompanyId: number;
    carbonAudience: string;
  }> {
    const url = this.CARBON_URL + '/super-admin/esg/login';
    const body = {
      jwtId: jwtId,
      expiredAt: expiredAt,
      email: email,
      fullName: fullName,
      companyName: companyName,
      companyId: companyId,
    };
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.post(url, body, this.CONFIG);
      if (res.data?.status && res.data?.data) {
        this.logger.log('POST', url, body, this.CONFIG, res.data);
        return res.data.data;
      } else {
        throw new InternalServerErrorException();
      }
    } catch (error) {
      this.logger.error(
        'POST',
        url,
        body,
        this.CONFIG,
        String(error),
        res?.data,
      );
    }
  }

  async logout(user: UserDto): Promise<void> {
    const url = this.CARBON_URL + '/users/logout';
    const config: AxiosRequestConfig = {
      headers: { Authorization: user.bearerToken },
    };
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.post(url, {}, config);
      if (res.data?.status) {
        this.logger.log('POST', url, config, res.data);
      } else {
        throw new InternalServerErrorException();
      }
    } catch (error) {
      this.logger.error('POST', url, config, String(error), res?.data);
      throw new InternalServerErrorException();
    }
  }

  async createFacility(
    user: UserDto,
    dto: CreateFacilityDto,
  ): Promise<{
    status: boolean;
    data?: any;
    messages: string[];
    errors?: object;
  }> {
    const url = this.CARBON_URL + '/facilities';
    const config: AxiosRequestConfig = {
      headers: { Authorization: user.bearerToken },
    };
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.post(url, dto, config);
      this.logger.log('POST', url, dto, config, res.data);
      return res.data;
    } catch (error) {
      this.logger.error('POST', url, dto, config, String(error), res?.data);
      throw new BadRequestException('Beklenmedik bir hata oluştu.');
    }
  }

  async createReport(
    user: UserDto,
    dto: CreateReportDto,
  ): Promise<CreateReportResType> {
    const url = this.CARBON_URL + '/reports';
    const config: AxiosRequestConfig = {
      headers: { Authorization: user.bearerToken },
    };
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.post(url, dto, config);
      this.logger.log('POST', url, dto, config, res.data);

      const createReportRes: CreateReportResType = res.data;
      createReportRes.data = plainToInstance(
        CreateCarbonReportResDto,
        createReportRes.data,
        {
          excludeExtraneousValues: true,
        },
      );
      return createReportRes;
    } catch (error) {
      this.logger.error('POST', url, dto, config, String(error), res?.data);
      throw new BadRequestException('Beklenmedik bir hata oluştu.');
    }
  }

  async createIsoStandard(
    user: UserDto,
    dto: CreateIsoStandardDto,
  ): Promise<{
    status: boolean;
    data?: any;
    messages: string[];
    errors?: object;
  }> {
    const url = this.CARBON_URL + '/iso-standards';
    const config: AxiosRequestConfig = {
      headers: { Authorization: user.bearerToken },
    };
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.post(url, dto, config);
      this.logger.log('POST', url, dto, config, res.data);
      return res.data;
    } catch (error) {
      this.logger.error('POST', url, dto, config, String(error), res?.data);
      throw new BadRequestException('Beklenmedik bir hata oluştu.');
    }
  }

  async updateUserProfile(
    user: UserDto,
    fullName: string,
    phone: string,
    image: string,
    options?: { title?: string },
  ): Promise<void> {
    const url = this.CARBON_URL + '/users/profile';
    const config: AxiosRequestConfig = {
      headers: { Authorization: user.bearerToken },
    };
    const body = {
      fullName: fullName,
      phone: phone,
      photo: image,
      title: options?.title,
    };
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.put(url, body, config);
      this.logger.log('PUT', url, body, config, res.data);
      if (res.data?.status) {
        this.logger.log('PUT', url, config, res.data);
      } else {
        throw new InternalServerErrorException();
      }
    } catch (error) {
      this.logger.error('PUT', url, body, config, String(error), res?.data);
      throw new BadRequestException('Beklenmedik bir hata oluştu.');
    }
  }

  async updateOrganization(
    user: UserDto,
    organization: Organization,
  ): Promise<void> {
    const url = this.CARBON_URL + '/companies';
    const config: AxiosRequestConfig = {
      headers: { Authorization: user.bearerToken },
    };
    const body = {
      name: organization.name,
      address: organization.address,
      logo: organization.image,
    };
    let res: AxiosResponse;
    try {
      res = await this.httpService.axiosRef.put(url, body, config);
      this.logger.log('PUT', url, body, config, res.data);
      if (res.data?.status) {
        this.logger.log('PUT', url, config, res.data);
      } else {
        throw new InternalServerErrorException();
      }
    } catch (error) {
      this.logger.error('PUT', url, body, config, String(error), res?.data);
      throw new BadRequestException('Beklenmedik bir hata oluştu.');
    }
  }
}
