import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { createSoftDeleteMiddleware } from 'prisma-soft-delete-middleware';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  constructor() {
    super();

    this.$use(
      createSoftDeleteMiddleware({
        models: {
          User: true,
          UserAgreement: true,
          UserOrganization: true,
          UserPending: true,
          UserActivation: true,
          UserForgot: true,
          UserPermission: true,
          UserSession: true,
          Organization: true,
          OrganizationRelation: true,
          OrganizationCountry: true,
          OrganizationSetting: true,
          OrganizationProcedureCategory: true,
          OrganizationProcedurePolicy: true,
          OrganizationSector: true,
          Sector: true,
          SectorSource: true,
          Role: true,
          Permission: true,
          Country: true,
          Currency: true,
          Procedure: true,
          ProcedureCategory: true,
          ProcedurePolicy: true,
          Stakeholder: true,
          StakeholderCommunication: true,
          StakeholderUser: true,
          StakeholderDefault: true,
          QuickStart: true,
          FileManager: true,
          SituationReport: true,
          SituationReportAnswer: true,
          SituationQuestion: true,
          SituationQuestionCategory: true,
          SituationQuestionType: true,
          SituationHelp: true,
          Trend: true,
          TrendReport: true,
          TrendDefault: true,
          TrendTerm: true,
          TrendCategory: true,
          TrendHelp: true,
          Risk: true,
          RiskReport: true,
          RiskHelp: true,
          Chain: true,
          ChainReport: true,
          ChainDefault: true,
          ChainHelp: true,
          Capital: true,
          CapitalReport: true,
          CapitalDefault: true,
          CapitalHelp: true,
          Opinion: true,
          OpinionReport: true,
          OpinionDefault: true,
          OpinionHelp: true,
          OpinionHelpAnswer: true,
          Industry: true,
          IndustryDetail: true,
          IndustryReport: true,
          IndustryDefault: true,
          IndustryDefaultDetail: true,
          IndustryHelp: true,
          IndustryHelpAnswer: true,
          Tendency: true,
          TendencyReport: true,
          TendencyDefault: true,
          TendencyHelp: true,
          TendencyHelpAnswer: true,
          Financial: true,
          FinancialDetail: true,
          FinancialReport: true,
          FinancialHelp: true,
          FinancialHelpAnswer: true,
          FinancialPriority: true,
          FinancialReview: true,
          FinancialSignificance: true,
          Impact: true,
          ImpactDetail: true,
          ImpactReport: true,
          ImpactHelp: true,
          ImpactHelpAnswer: true,
          ImpactPriority: true,
          ImpactReview: true,
          ImpactSignificance: true,
          Strategy: true,
          StrategyDetail: true,
          StrategyDetailCategory: true,
          StrategyReport: true,
          StrategyHelp: true,
          StrategyTerm: true,
          Governance: true,
          GovernanceReport: true,
          GovernanceMember: true,
          GovernanceMemberProfile: true,
          GovernanceMeeting: true,
          Metric: true,
          MetricReport: true,
          MetricDefault: true,
          MetricPerformance: true,
          MetricHelp: true,
          MetricSupervisor: true,
          MetricSupervisorProfile: true,
          ChartTemplate: true,
          Chart: true,
        },
        defaultConfig: {
          field: 'deletedAt',
          createValue: (deleted) => {
            if (deleted) return new Date();
            return null;
          },
        },
      }),
    );
  }

  async onModuleInit() {
    await this.$connect();
  }
}
