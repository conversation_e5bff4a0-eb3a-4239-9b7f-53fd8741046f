import { Injectable } from '@nestjs/common';
import { Activity, PrismaClient } from '@prisma/client';
import { ActivityActionDto } from '../../activity/dtos/req/activity-action.dto';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { PrismaService } from './prisma.service';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../dtos/user-data.dto';
import { ActivityListDataType } from '../types/activity.util.type';
import { ReviewStatusEnum } from '../../insight/enums/review-status.enum';

@Injectable()
export class ActivityUtilService {
  constructor(private prismaService: PrismaService) {}

  async getDataForActivityList(
    organizationId: number,
    activities: Activity[],
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ): Promise<ActivityListDataType> {
    const dbService = options?.tx ?? this.prismaService;

    // SITUATION
    const situationReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.SITUATION_REPORT_CREATE,
            ActivityEnum.SITUATION_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const situationReports = await dbService.situationReport.findMany({
      where: {
        id: { in: situationReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const situationHelpIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.SITUATION_HELP,
            ActivityEnum.SITUATION_HELP_ANSWER,
          ].includes(a.activityType) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const situationHelps = await dbService.situationHelp.findMany({
      where: {
        id: { in: situationHelpIds },
        report: { organizationId: organizationId },
        deletedAt: {},
      },
      include: { helpedUser: true },
    });
    for (const help of situationHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }

    // TREND
    const trendReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.TREND_REPORT_CREATE,
            ActivityEnum.TREND_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const trendReports = await dbService.trendReport.findMany({
      where: {
        id: { in: trendReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const trendHelpIds: number[] = activities
      .filter(
        (a) =>
          [ActivityEnum.TREND_HELP, ActivityEnum.TREND_HELP_ANSWER].includes(
            a.activityType,
          ) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const trendHelps = await dbService.trendHelp.findMany({
      where: {
        id: { in: trendHelpIds },
        trend: { report: { organizationId: organizationId } },
        deletedAt: {},
      },
      include: { trend: true, helpedUser: true },
    });
    for (const help of trendHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }

    // RISK
    const riskReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.RISK_REPORT_CREATE,
            ActivityEnum.RISK_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const riskReports = await dbService.riskReport.findMany({
      where: {
        id: { in: riskReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const riskHelpIds: number[] = activities
      .filter(
        (a) =>
          [ActivityEnum.RISK_HELP, ActivityEnum.RISK_HELP_ANSWER].includes(
            a.activityType,
          ) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const riskHelps = await dbService.riskHelp.findMany({
      where: {
        id: { in: riskHelpIds },
        risk: { report: { organizationId: organizationId } },
        deletedAt: {},
      },
      include: { risk: true, helpedUser: true },
    });
    for (const help of riskHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }

    // CHAIN
    const chainReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.CHAIN_REPORT_CREATE,
            ActivityEnum.CHAIN_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const chainReports = await dbService.chainReport.findMany({
      where: {
        id: { in: chainReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const chainHelpIds: number[] = activities
      .filter(
        (a) =>
          [ActivityEnum.CHAIN_HELP, ActivityEnum.CHAIN_HELP_ANSWER].includes(
            a.activityType,
          ) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const chainHelps = await dbService.chainHelp.findMany({
      where: {
        id: { in: chainHelpIds },
        chain: { report: { organizationId: organizationId } },
        deletedAt: {},
      },
      include: { chain: true, helpedUser: true },
    });
    for (const help of chainHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }

    // CAPITAL
    const capitalReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.CAPITAL_REPORT_CREATE,
            ActivityEnum.CAPITAL_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const capitalReports = await dbService.capitalReport.findMany({
      where: {
        id: { in: capitalReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const capitalHelpIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.CAPITAL_HELP,
            ActivityEnum.CAPITAL_HELP_ANSWER,
          ].includes(a.activityType) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const capitalHelps = await dbService.capitalHelp.findMany({
      where: {
        id: { in: capitalHelpIds },
        capital: { report: { organizationId: organizationId } },
        deletedAt: {},
      },
      include: { capital: true, helpedUser: true },
    });
    for (const help of capitalHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }

    // FINANCIAL
    const financialReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.FINANCIAL_REPORT_CREATE,
            ActivityEnum.FINANCIAL_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload['reportId']);
    const financialReports = await dbService.financialReport.findMany({
      where: {
        id: { in: financialReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const financialHelpIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.FINANCIAL_HELP,
            ActivityEnum.FINANCIAL_HELP_ANSWER,
          ].includes(a.activityType) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const financialHelps = await dbService.financialHelp.findMany({
      where: {
        id: { in: financialHelpIds },
        report: { organizationId: organizationId },
        deletedAt: {},
      },
      include: { report: true, helpedUser: true },
    });
    for (const help of financialHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }
    const financialReviewIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.FINANCIAL_REVIEW,
            ActivityEnum.FINANCIAL_REVIEW_ANSWER,
          ].includes(a.activityType) && a.payload?.['reviewId'],
      )
      .map((a) => a.payload?.['reviewId']);
    const financialReviews = await dbService.financialReview.findMany({
      where: {
        id: { in: financialReviewIds },
        report: { organizationId: organizationId },
        deletedAt: {},
      },
      include: { report: true, reviewerUser: true },
    });
    for (const review of financialReviews) {
      review['reviewerUserInfo'] = plainToInstance(
        UserDataDto,
        review.reviewerUser,
        {
          excludeExtraneousValues: true,
        },
      );
    }
    const financialSignificancesReportIds: number[] = activities
      .filter(
        (a) =>
          a.activityType == ActivityEnum.FINANCIAL_SIGNIFICANCE_CREATE &&
          a.payload?.['reportId'],
      )
      .map((a) => a.payload['reportId']);
    const financialSignificances =
      await dbService.financialSignificance.findMany({
        where: {
          id: { in: financialSignificancesReportIds },
          report: { organizationId: organizationId, deletedAt: {} },
          deletedAt: {},
        },
        include: { report: true },
      });

    // IMPACT
    const impactReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.IMPACT_REPORT_CREATE,
            ActivityEnum.IMPACT_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload['reportId']);
    const impactReports = await dbService.impactReport.findMany({
      where: {
        id: { in: impactReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const impactHelpIds: number[] = activities
      .filter(
        (a) =>
          [ActivityEnum.IMPACT_HELP, ActivityEnum.IMPACT_HELP_ANSWER].includes(
            a.activityType,
          ) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const impactHelps = await dbService.impactHelp.findMany({
      where: {
        id: { in: impactHelpIds },
        report: { organizationId: organizationId },
        deletedAt: {},
      },
      include: { report: true, helpedUser: true },
    });
    for (const help of impactHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }
    const impactReviewIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.IMPACT_REVIEW,
            ActivityEnum.IMPACT_REVIEW_ANSWER,
          ].includes(a.activityType) && a.payload?.['reviewId'],
      )
      .map((a) => a.payload?.['reviewId']);
    const impactReviews = await dbService.impactReview.findMany({
      where: {
        id: { in: impactReviewIds },
        report: { organizationId: organizationId },
        deletedAt: {},
      },
      include: { report: true, reviewerUser: true },
    });
    for (const review of impactReviews) {
      review['reviewerUserInfo'] = plainToInstance(
        UserDataDto,
        review.reviewerUser,
        {
          excludeExtraneousValues: true,
        },
      );
    }
    const impactSignificancesReportIds: number[] = activities
      .filter(
        (a) =>
          a.activityType == ActivityEnum.IMPACT_SIGNIFICANCE_CREATE &&
          a.payload?.['reportId'],
      )
      .map((a) => a.payload['reportId']);
    const impactSignificances = await dbService.impactSignificance.findMany({
      where: {
        id: { in: impactSignificancesReportIds },
        report: { organizationId: organizationId, deletedAt: {} },
        deletedAt: {},
      },
      include: { report: true },
    });

    // INDUSTRY
    const industryReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.INDUSTRY_REPORT_CREATE,
            ActivityEnum.INDUSTRY_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const industryReports = await dbService.industryReport.findMany({
      where: {
        id: { in: industryReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const industryHelpIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.INDUSTRY_HELP,
            ActivityEnum.INDUSTRY_HELP_ANSWER,
          ].includes(a.activityType) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const industryHelps = await dbService.industryHelp.findMany({
      where: {
        id: { in: industryHelpIds },
        report: { organizationId: organizationId },
        deletedAt: {},
      },
      include: { report: true, helpedUser: true },
    });
    for (const help of industryHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }

    // OPINION
    const opinionReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.OPINION_REPORT_CREATE,
            ActivityEnum.OPINION_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const opinionReports = await dbService.opinionReport.findMany({
      where: {
        id: { in: opinionReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const opinionHelpIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.OPINION_HELP,
            ActivityEnum.OPINION_HELP_ANSWER,
          ].includes(a.activityType) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const opinionHelps = await dbService.opinionHelp.findMany({
      where: {
        id: { in: opinionHelpIds },
        report: { organizationId: organizationId },
        deletedAt: {},
      },
      include: { report: true, stakeholderUser: true },
    });
    for (const help of opinionHelps) {
      help['stakeholderUserInfo'] = plainToInstance(
        UserDataDto,
        help.stakeholderUser,
        {
          excludeExtraneousValues: true,
        },
      );
    }

    // TENDENCY
    const tendencyReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.TENDENCY_REPORT_CREATE,
            ActivityEnum.TENDENCY_REPORT_COMPLETE,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const tendencyReports = await dbService.tendencyReport.findMany({
      where: {
        id: { in: tendencyReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const tendencyHelpIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.TENDENCY_HELP,
            ActivityEnum.TENDENCY_HELP_ANSWER,
          ].includes(a.activityType) && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const tendencyHelps = await dbService.tendencyHelp.findMany({
      where: {
        id: { in: tendencyHelpIds },
        report: { organizationId: organizationId },
        deletedAt: {},
      },
      include: { report: true, helpedUser: true },
    });
    for (const help of tendencyHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }

    // STRATEGY
    const strategyReportIds: number[] = activities
      .filter(
        (a) =>
          [
            ActivityEnum.STRATEGY_REPORT_CREATE,
            ActivityEnum.STRATEGY_REPORT_ROAD_MAP,
            ActivityEnum.STRATEGY_REPORT_COMPLETE,
            ActivityEnum.STRATEGY_HELP_ANSWER,
          ].includes(a.activityType) && a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const strategyReports = await dbService.strategyReport.findMany({
      where: {
        id: { in: strategyReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const strategyHelpIds: number[] = activities
      .filter(
        (a) =>
          a.activityType == ActivityEnum.STRATEGY_HELP && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const strategyHelps = await dbService.strategyHelp.findMany({
      where: {
        id: { in: strategyHelpIds },
        strategy: { report: { organizationId: organizationId } },
        deletedAt: {},
      },
      include: { strategy: true, helpedUser: true },
    });
    for (const help of strategyHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }

    // GOVERNANCE
    const governanceReportIds: number[] = activities
      .filter(
        (a) =>
          a.activityType == ActivityEnum.GOVERNANCE_REPORT_CREATE &&
          a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const governanceReports = await dbService.governanceReport.findMany({
      where: {
        id: { in: governanceReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });

    // METRIC
    const metricReportIds: number[] = activities
      .filter(
        (a) =>
          a.activityType == ActivityEnum.METRIC_REPORT_CREATE &&
          a.payload?.['reportId'],
      )
      .map((a) => a.payload?.['reportId']);
    const metricReports = await dbService.metricReport.findMany({
      where: {
        id: { in: metricReportIds },
        organizationId: organizationId,
        deletedAt: {},
      },
    });
    const metricHelpIds: number[] = activities
      .filter(
        (a) =>
          a.activityType == ActivityEnum.METRIC_HELP && a.payload?.['helpId'],
      )
      .map((a) => a.payload?.['helpId']);
    const metricHelps = await dbService.metricHelp.findMany({
      where: {
        id: { in: metricHelpIds },
        metric: { report: { organizationId: organizationId } },
        deletedAt: {},
      },
      include: { metric: true, helpedUser: true },
    });
    for (const help of metricHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }
    const metricSupervisorIds: number[] = activities
      .filter(
        (a) =>
          a.activityType == ActivityEnum.METRIC_SUPERVISOR &&
          a.payload?.['supervisorId'],
      )
      .map((a) => a.payload?.['supervisorId']);
    const metricSupervisors = await dbService.metricSupervisor.findMany({
      where: {
        id: { in: metricSupervisorIds },
        metric: { report: { organizationId: organizationId } },
        deletedAt: {},
      },
      include: { metric: true, profile: { include: { user: true } } },
    });
    for (const supervisor of metricSupervisors) {
      supervisor['supervisorUserInfo'] = plainToInstance(
        UserDataDto,
        supervisor.profile.user,
        {
          excludeExtraneousValues: true,
        },
      );
    }

    return {
      situationReports: situationReports,
      situationHelps: situationHelps,
      trendReports: trendReports,
      trendHelps: trendHelps,
      riskReports: riskReports,
      riskHelps: riskHelps,
      chainReports: chainReports,
      chainHelps: chainHelps,
      capitalReports: capitalReports,
      capitalHelps: capitalHelps,
      financialReports: financialReports,
      financialHelps: financialHelps,
      financialReviews: financialReviews,
      financialSignificances: financialSignificances,
      impactReports: impactReports,
      impactHelps: impactHelps,
      impactReviews: impactReviews,
      impactSignificances: impactSignificances,
      industryReports: industryReports,
      industryHelps: industryHelps,
      opinionReports: opinionReports,
      opinionHelps: opinionHelps,
      tendencyReports: tendencyReports,
      tendencyHelps: tendencyHelps,
      strategyReports: strategyReports,
      strategyHelps: strategyHelps,
      governanceReports: governanceReports,
      metricReports: metricReports,
      metricHelps: metricHelps,
      metricSupervisors: metricSupervisors,
    };
  }

  async getActivityList(
    userId: number,
    organizationId: number,
    activities: Activity[],
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      data?: ActivityListDataType;
    },
  ): Promise<{
    [key: number]: { actions: ActivityActionDto[]; content: string };
  }> {
    let activityListData: ActivityListDataType;
    if (options?.data) {
      activityListData = options.data;
    } else {
      activityListData = await this.getDataForActivityList(
        organizationId,
        activities,
        {
          tx: options?.tx,
        },
      );
    }
    const {
      situationReports,
      situationHelps,
      trendReports,
      trendHelps,
      riskReports,
      riskHelps,
      chainReports,
      chainHelps,
      capitalReports,
      capitalHelps,
      financialReports,
      financialHelps,
      financialReviews,
      financialSignificances,
      impactReports,
      impactHelps,
      impactReviews,
      impactSignificances,
      industryReports,
      industryHelps,
      opinionReports,
      opinionHelps,
      tendencyReports,
      tendencyHelps,
      strategyReports,
      strategyHelps,
      governanceReports,
      metricReports,
      metricHelps,
      metricSupervisors,
    } = activityListData;
    const activityList: {
      [key: number]: { actions: ActivityActionDto[]; content: string };
    } = {};
    for (const help of capitalHelps) {
      help['helpedUserInfo'] = plainToInstance(UserDataDto, help.helpedUser, {
        excludeExtraneousValues: true,
      });
    }
    for (const activity of activities) {
      // SITUATION
      if (activity.activityType == ActivityEnum.SITUATION_REPORT_CREATE) {
        const report = situationReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir mevcut durum analizi raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (
        activity.activityType == ActivityEnum.SITUATION_REPORT_COMPLETE
      ) {
        const report = situationReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir mevcut durum analizi raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.SITUATION_HELP) {
        const help = situationHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir mevcut durum analizi sorusu atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/analysis/questions/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir soru atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.SITUATION_HELP_ANSWER) {
        const help = situationHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir mevcut durum analizi sorusu cevapladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/analysis/${help.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, sorduğunuz soruyu cevapladı.`,
        };
      }
      // TREND
      else if (activity.activityType == ActivityEnum.TREND_REPORT_CREATE) {
        const report = trendReports.find(
          (h) => h.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir trend raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/trend-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (activity.activityType == ActivityEnum.TREND_REPORT_COMPLETE) {
        const report = trendReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir trend raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/trend-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raprounu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.TREND_HELP) {
        const help = trendHelps.find((h) => h.id == activity.payload['helpId']);
        if (!help?.trend) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir trend analizi atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/trend-analysis/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir trend analizi atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.TREND_HELP_ANSWER) {
        const help = trendHelps.find((h) => h.id == activity.payload['helpId']);
        if (!help?.trend) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir trend analizi verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/trend-analysis/${help.trend.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, trend analizi verilerini girdi.`,
        };
      }
      // RISK
      else if (activity.activityType == ActivityEnum.RISK_REPORT_CREATE) {
        const report = riskReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir risk raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/risk-opportunity-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (activity.activityType == ActivityEnum.RISK_REPORT_COMPLETE) {
        const report = riskReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir risk raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/risk-opportunity-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.RISK_HELP) {
        const help = riskHelps.find((h) => h.id == activity.payload['helpId']);
        if (!help?.risk) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir risk analizi atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/risk-opportunity-analysis/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir risk analizi atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.RISK_HELP_ANSWER) {
        const help = riskHelps.find((h) => h.id == activity.payload['helpId']);
        if (!help?.risk) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir risk analizi verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/risk-opportunity-analysis/${help.risk.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, risk analizi verilerini girdi.`,
        };
      }
      // CHAIN
      else if (activity.activityType == ActivityEnum.CHAIN_REPORT_CREATE) {
        const report = chainReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir değer zinciri raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/value-chain/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (activity.activityType == ActivityEnum.CHAIN_REPORT_COMPLETE) {
        const report = chainReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir değer zinciri raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/value-chain/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.CHAIN_HELP) {
        const help = chainHelps.find((h) => h.id == activity.payload['helpId']);
        if (!help?.chain) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir değer zinciri atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/value-chain/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir değer zinciri analizi atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.CHAIN_HELP_ANSWER) {
        const help = chainHelps.find((h) => h.id == activity.payload['helpId']);
        if (!help?.chain) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir değer zinciri analizi verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/value-chain/${help.chain.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, değer zinciri analizi verilerini girdi.`,
        };
      }
      // CAPITAL
      else if (activity.activityType == ActivityEnum.CAPITAL_REPORT_CREATE) {
        const report = capitalReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir sermaye ögeleri raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/capital-items/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (
        activity.activityType == ActivityEnum.CAPITAL_REPORT_COMPLETE
      ) {
        const report = capitalReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir sermaye ögeleri raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/capital-items/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.CAPITAL_HELP) {
        const help = capitalHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.capital) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir sermaye ögeleri analizi atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/capital-items/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir sermaye ögeleri analizi atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.CAPITAL_HELP_ANSWER) {
        const help = capitalHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.capital) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir sermaye ögeleri analizi verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/capital-items/${help.capital.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, sermaye ögeleri analizi verilerini girdi.`,
        };
      }
      // FINANCIAL
      else if (activity.activityType == ActivityEnum.FINANCIAL_REPORT_CREATE) {
        const report = financialReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir finansal önemlilik raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/financial-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (
        activity.activityType == ActivityEnum.FINANCIAL_REPORT_COMPLETE
      ) {
        const report = financialReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir finansal önemlilik raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/financial-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.FINANCIAL_HELP) {
        const help = financialHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir finansal önemlilik atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/double-analysis/financial-analysis/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir finansal önemlilik atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.FINANCIAL_HELP_ANSWER) {
        const help = financialHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir finansal önemlilik verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/financial-analysis/${help.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, finansal önemlilik verilerini girdi.`,
        };
      } else if (activity.activityType == ActivityEnum.FINANCIAL_REVIEW) {
        const review = financialReviews.find(
          (h) => h.id == activity.payload['reviewId'],
        );
        if (!review?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir finansal önemlilik raporunu yönetici onayına gönderdi.`,
          };
          continue;
        }
        const reviewerUserTxt =
          review.reviewerUserId == userId
            ? 'sizden'
            : `${review['reviewerUserInfo'].fullName} kişisinden`;
        activityList[activity.id] = {
          actions:
            review.reviewerUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/double-analysis/financial-analysis/reviews/${review.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${reviewerUserTxt} finansal önemlilik yönetici onayı istedi.`,
        };
      } else if (
        activity.activityType == ActivityEnum.FINANCIAL_REVIEW_ANSWER
      ) {
        const review = financialReviews.find(
          (h) => h.id == activity.payload['reviewId'],
        );
        if (!review?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir finansal önemlilik yönetici onayına cevap verdi.`,
          };
          continue;
        }
        const status =
          review.status == ReviewStatusEnum.APPROVED ? 'onayladı' : 'reddetti';
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/financial-analysis/${review.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${review.report.name} isimli finansal önemlilik raporunu ${status}.`,
        };
      } else if (
        activity.activityType == ActivityEnum.FINANCIAL_SIGNIFICANCE_CREATE
      ) {
        const significance = financialSignificances.find(
          (s) => s.id == activity.payload['reportId'],
        );
        if (!significance || !significance.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir finansal önemlilik tablosu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/financials-significances/${significance.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${significance.report.name} raporundan ${significance.name} finansal önemlilik tablosu oluşturdu.`,
        };
      }
      // IMPACT
      else if (activity.activityType == ActivityEnum.IMPACT_REPORT_CREATE) {
        const report = impactReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir etki analizi raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/impact-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (activity.activityType == ActivityEnum.IMPACT_REPORT_COMPLETE) {
        const report = impactReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir etki analizi raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/impact-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.IMPACT_HELP) {
        const help = impactHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir etki analiz atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/double-analysis/impact-analysis/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir etki analiz atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.IMPACT_HELP_ANSWER) {
        const help = impactHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir etki analiz verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/impact-analysis/${help.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, etki analiz verilerini girdi.`,
        };
      } else if (activity.activityType == ActivityEnum.IMPACT_REVIEW) {
        const review = impactReviews.find(
          (h) => h.id == activity.payload['reviewId'],
        );
        if (!review?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir etki analizi raporunu yönetici onayına gönderdi.`,
          };
          continue;
        }
        const reviewerUserTxt =
          review.reviewerUserId == userId
            ? 'sizden'
            : `${review['reviewerUserInfo'].fullName} kişisinden`;
        activityList[activity.id] = {
          actions:
            review.reviewerUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/double-analysis/impact-analysis/reviews/${review.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${reviewerUserTxt} etki analizi yönetici onayı istedi.`,
        };
      } else if (activity.activityType == ActivityEnum.IMPACT_REVIEW_ANSWER) {
        const review = impactReviews.find(
          (h) => h.id == activity.payload['reviewId'],
        );
        if (!review?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir etki analizi yönetici onayına cevap verdi.`,
          };
          continue;
        }
        const status =
          review.status == ReviewStatusEnum.APPROVED ? 'onayladı' : 'reddetti';
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/impact-analysis/${review.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${review.report.name} isimli etki analizi raporunu ${status}.`,
        };
      } else if (
        activity.activityType == ActivityEnum.IMPACT_SIGNIFICANCE_CREATE
      ) {
        const significance = impactSignificances.find(
          (s) => s.id == activity.payload['reportId'],
        );
        if (!significance || !significance.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir etki önemlilik tablosu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/impacts-significances/${significance.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${significance.report.name} raporundan ${significance.name} etki önemlilik tablosu oluşturdu.`,
        };
      }
      // INDUSTRY
      else if (activity.activityType == ActivityEnum.INDUSTRY_REPORT_CREATE) {
        const report = industryReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir sektör analizi raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/sector-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (
        activity.activityType == ActivityEnum.INDUSTRY_REPORT_COMPLETE
      ) {
        const report = industryReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir sektör analizi raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/sector-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.INDUSTRY_HELP) {
        const help = industryHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir sektör analiz atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/double-analysis/sector-analysis/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir sektör analiz atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.INDUSTRY_HELP_ANSWER) {
        const help = industryHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir sektör analiz verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/sector-analysis/${help.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, sektör analiz verilerini girdi.`,
        };
      }
      // OPINION
      else if (activity.activityType == ActivityEnum.OPINION_REPORT_CREATE) {
        const report = opinionReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir paydaş görüşleri raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/stakeholder-views/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (
        activity.activityType == ActivityEnum.OPINION_REPORT_COMPLETE
      ) {
        const report = opinionReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir paydaş görüşleri raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/stakeholder-views/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.OPINION_HELP) {
        const report = opinionReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir paydaş görüşü atadı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/stakeholder-views/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, bir paydaş görüşü atadı.`,
        };
      } /*else if (activity.activityType == ActivityEnum.OPINION_HELP_ANSWER) {
        const help = opinionHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir paydaş görüşü verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/stakeholder-views/${help.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, paydaş görüşleri verilerini girdi.`,
        };
      }*/
      // TENDENCY
      else if (activity.activityType == ActivityEnum.TENDENCY_REPORT_CREATE) {
        const report = tendencyReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir trend analizi raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/trend-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (
        activity.activityType == ActivityEnum.TENDENCY_REPORT_COMPLETE
      ) {
        const report = tendencyReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir trend analizi raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/trend-analysis/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.TENDENCY_HELP) {
        const help = tendencyHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir trend analizi atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/double-analysis/trend-analysis/helps/${help.id}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir trend analizi atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.TENDENCY_HELP_ANSWER) {
        const help = tendencyHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir trend analizi verisi girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/double-analysis/trend-analysis/${help.reportId}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, trend analizi verilerini girdi.`,
        };
      }
      // STRATEGY
      else if (activity.activityType == ActivityEnum.STRATEGY_REPORT_CREATE) {
        const report = strategyReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir strateji raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/design/strategies/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (
        activity.activityType == ActivityEnum.STRATEGY_REPORT_ROAD_MAP
      ) {
        const report = strategyReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir strateji raporunun yol haritasını oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/design/strategies/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunun yol haritasını oluşturdu.`,
        };
      } else if (
        activity.activityType == ActivityEnum.STRATEGY_REPORT_COMPLETE
      ) {
        const report = strategyReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir strateji raporu tamamladı.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/design/strategies/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu tamamladı.`,
        };
      } else if (activity.activityType == ActivityEnum.STRATEGY_HELP) {
        const help = strategyHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.strategy) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir strateji atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/design/strategies/helps/${help.strategy.reportId}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} bir strateji atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.STRATEGY_HELP_ANSWER) {
        const report = strategyReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, strateji verilerini girdi.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/design/strategies/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, strateji verilerini girdi.`,
        };
      }
      // GOVERNANCE
      else if (activity.activityType == ActivityEnum.GOVERNANCE_REPORT_CREATE) {
        const report = governanceReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, bir yönetişim raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/design/governances/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      }
      // METRIC
      else if (activity.activityType == ActivityEnum.METRIC_REPORT_CREATE) {
        const report = metricReports.find(
          (r) => r.id == activity.payload['reportId'],
        );
        if (!report) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, metrikler ve hedefler raporu oluşturdu.`,
          };
          continue;
        }
        activityList[activity.id] = {
          actions: [
            {
              text: 'Görüntüle',
              url: `/design/metrics/${report.id}`,
            },
          ],
          content: `${activity['createdUserInfo']['fullName']}, ${report.name} raporunu oluşturdu.`,
        };
      } else if (activity.activityType == ActivityEnum.METRIC_HELP) {
        const help = metricHelps.find(
          (h) => h.id == activity.payload['helpId'],
        );
        if (!help?.metric || help?.deletedAt) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, metrikler ve hedefler atadı.`,
          };
          continue;
        }
        const helpedUserTxt =
          help.helpedUserId == userId
            ? 'size'
            : `${help['helpedUserInfo'].fullName} kişisine`;
        activityList[activity.id] = {
          actions:
            help.helpedUserId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/design/metrics/helps/${help.metric.reportId}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${helpedUserTxt} metrikler ve hedefler atadı.`,
        };
      } else if (activity.activityType == ActivityEnum.METRIC_SUPERVISOR) {
        const supervisor = metricSupervisors.find(
          (h) => h.id == activity.payload['supervisorId'],
        );
        if (!supervisor?.metric || supervisor?.deletedAt) {
          activityList[activity.id] = {
            actions: [{ text: 'Silindi' }],
            content: `${activity['createdUserInfo']['fullName']}, metrikler ve hedefler veri sorumlusu olarak atadı.`,
          };
          continue;
        }
        const supervisorUserTxt =
          supervisor.profile.userId == userId
            ? 'sizi'
            : `${supervisor['supervisorUserInfo'].fullName} kişisini`;
        activityList[activity.id] = {
          actions:
            supervisor.profile.userId == userId
              ? [
                  {
                    text: 'Görüntüle',
                    url: `/design/metrics/supervisors/${supervisor.metric.reportId}`,
                  },
                ]
              : [],
          content: `${activity['createdUserInfo']['fullName']}, ${supervisorUserTxt} metrikler ve hedefler veri sorumlusu olarak atadı.`,
        };
      }
    }
    return activityList;
  }
}
