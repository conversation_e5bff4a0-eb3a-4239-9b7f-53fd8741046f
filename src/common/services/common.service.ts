import { Injectable } from '@nestjs/common';
import { PrismaService } from './prisma.service';

@Injectable()
export class CommonService {
  constructor(private prismaService: PrismaService) {}

  async getCountries() {
    return await this.prismaService.country.findMany({
      select: {
        id: true,
        name: true,
      },
      orderBy: { name: 'asc' },
    });
  }

  async getCurrencies() {
    return await this.prismaService.currency.findMany({
      select: {
        id: true,
        name: true,
      },
      orderBy: { order: 'asc' },
    });
  }
}
