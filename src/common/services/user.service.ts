import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { UserDto } from '../dtos/user.dto';
import { JwtService } from '@nestjs/jwt';
import { AccessTokenPayloadDto } from '../dtos/access-token-payload.dto';
import { fsReadFile } from 'ts-loader/dist/utils';
import { mainDirectory } from '../../main';
import { UtilService } from './util.service';
import { addWeeks, differenceInSeconds, isBefore } from 'date-fns';
import { CreateAccessTokenDto } from '../dtos/create-access-token.dto';
import { ApiClientService } from './api-client.service';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../dtos/user-data.dto';
import { Prisma, PrismaClient } from '@prisma/client';
import { PermissionEnum } from '../enums/permission.enum';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UserService {
  constructor(
    private prismaService: PrismaService,
    private jwtService: JwtService,
    private utilService: UtilService,
    private apiClientService: ApiClientService,
  ) {}

  async isLogout(userId: number, jwtId: string): Promise<boolean> {
    const session = await this.prismaService.userSession.findFirst({
      where: { userId: userId, jwtId: jwtId },
    });
    if (!session) {
      return true;
    }
    if (isBefore(session.expiredAt, new Date())) {
      return true;
    }
    return false;
  }

  async getUser(userId: number, organizationId: number) {
    const user = await this.prismaService.user.findFirstOrThrow({
      where: {
        id: userId,
        userOrganizations: { some: { organizationId: organizationId } },
      },
      include: {
        userOrganizations: {
          where: { organizationId: organizationId },
          include: { organization: true, role: true },
        },
        userPermissions: {
          where: { organizationId: organizationId },
          include: { permission: true },
        },
      },
    });
    return {
      id: user.id,
      fullName: `${user.name} ${user.surname}`,
      email: user.email,
      image: user.image,
      timezone: user.timezone,
      organizationId: user.userOrganizations[0].organization.id,
      organizationType: user.userOrganizations[0].organization.organizationType,
      role: user.userOrganizations[0].role,
      permissions: user.userPermissions.map((up) => up.permission),
      isAdmin: user.isAdmin,
    };
  }

  async checkPassword(user: UserDto, password: string): Promise<void> {
    const profile = await this.prismaService.user.findFirst({
      where: { id: user.id },
    });
    const isMatch = await bcrypt.compare(password, profile.password);
    if (!isMatch) {
      throw new BadRequestException('Şifre yanlış.');
    }
  }

  async checkUser(authorization: string): Promise<UserDto> {
    if (!authorization) {
      throw new HttpException('unauthenticated', HttpStatus.UNAUTHORIZED);
    }
    const tokenArr = authorization.split('Bearer ');
    const token =
      tokenArr.length == 1 ? String(tokenArr[0]) : String(tokenArr[1]);
    let payload: AccessTokenPayloadDto;
    try {
      payload = this.jwtService.verify(token, {
        publicKey: fsReadFile(mainDirectory + '/oauth-keys/public.key'),
      });
    } catch (error) {
      throw new HttpException('unauthenticated', HttpStatus.UNAUTHORIZED);
    }
    if (!payload) {
      throw new HttpException('unauthenticated', HttpStatus.UNAUTHORIZED);
    }
    if (await this.isLogout(payload.userId, payload.jti)) {
      throw new HttpException('unauthenticated', HttpStatus.UNAUTHORIZED);
    }
    const user = await this.getUser(payload.userId, payload.organizationId);
    if (!user) {
      throw new HttpException(
        'please register to system',
        HttpStatus.UNAUTHORIZED,
      );
    }
    return {
      id: user.id,
      fullName: user.fullName,
      email: user.email,
      image: user.image,
      timezone: user.timezone,
      organizationId: user.organizationId,
      organizationType: user.organizationType,
      role: user.role,
      permissions: user.permissions,
      jwtId: payload.jti,
      bearerToken: authorization,
      isAdmin: user.isAdmin,
    };
  }

  async createAccessToken(dto: CreateAccessTokenDto): Promise<string> {
    const dbService = dto.tx ?? this.prismaService;
    const user = await dbService.user.findFirstOrThrow({
      where: { id: dto.userId },
      include: {
        userOrganizations: {
          where: { organizationId: dto.organizationId },
          include: { organization: { include: { carbonSession: true } } },
        },
      },
    });
    const userDataDto = plainToInstance(UserDataDto, user, {
      excludeExtraneousValues: true,
    });
    const organization = user.userOrganizations[0].organization;
    const jwtId = this.utilService.getRandomString(80);
    const expiredAt = addWeeks(new Date(), 1);
    const { carbonUserId, carbonAudience } = await this.createCarbonSession(
      jwtId,
      expiredAt,
      userDataDto,
      organization,
      dto.tx,
    );
    const accessToken = this.jwtService.sign(
      {
        userId: dto.userId,
        organizationId: organization.id,
      } as AccessTokenPayloadDto,
      {
        privateKey: fsReadFile(mainDirectory + '/oauth-keys/private.key'),
        algorithm: 'RS256',
        expiresIn: differenceInSeconds(expiredAt, new Date()),
        audience: carbonAudience,
        jwtid: jwtId,
        subject: carbonUserId.toString(),
      },
    );
    const payload = {
      ipAddress: dto.request.ip,
      userAgent: dto.request.headers['user-agent'],
    };
    await dbService.userSession.create({
      data: {
        userId: dto.userId,
        organizationId: organization.id,
        jwtId: jwtId,
        payload: payload,
        expiredAt: expiredAt,
      },
    });
    return accessToken;
  }

  private async createCarbonSession(
    jwtId: string,
    expiredAt: Date,
    userDataDto: UserDataDto,
    organization: Prisma.OrganizationGetPayload<{
      include: { carbonSession: true };
    }>,
    tx?: Omit<PrismaClient, ITXClientDenyList>,
  ): Promise<{
    carbonUserId: number;
    carbonAudience: string;
  }> {
    const dbService = tx ?? this.prismaService;
    const carbonOrMetricSupervisorPermission =
      await dbService.userPermission.findFirst({
        where: {
          userId: userDataDto.id,
          organizationId: organization.id,
          permission: {
            key: {
              in: [PermissionEnum.CARBON, PermissionEnum.METRIC_SUPERVISOR],
            },
          },
        },
      });
    if (!carbonOrMetricSupervisorPermission) {
      return { carbonUserId: 0, carbonAudience: 'audience' };
    }
    const result = await this.apiClientService.login(
      jwtId,
      expiredAt,
      userDataDto.email,
      userDataDto.fullName,
      organization.name,
      organization.carbonSession?.carbonOrganizationId,
    );
    if (!result) {
      return { carbonUserId: 0, carbonAudience: 'audience' };
    }
    const carbonSession = await dbService.organizationCarbonSession.findFirst({
      where: {
        carbonOrganizationId: result.carbonCompanyId,
      },
    });
    if (!organization.carbonSession && !carbonSession) {
      await dbService.organizationCarbonSession.create({
        data: {
          organizationId: organization.id,
          carbonOrganizationId: result.carbonCompanyId,
        },
      });
    }
    return result;
  }

  async updateLastActivity(userId: number): Promise<void> {
    await this.prismaService.user.update({
      where: { id: userId },
      data: { lastActivity: new Date() },
    });
  }
}
