import { UserDto } from '../common/dtos/user.dto';
import { UtilService } from '../common/services/util.service';
import { UserQuickStart } from '@prisma/client';
import { SocketIoGateway } from '../common/gateways/socket-io.gateway';
import { Injectable } from '@nestjs/common';

@Injectable()
export class QuickStartGateway {
  constructor(
    private socketIoGateway: SocketIoGateway,
    private utilService: UtilService,
  ) {}

  completeQuickStart(user: UserDto, userQuickStart: UserQuickStart) {
    this.socketIoGateway.sendEmit(
      this.utilService.socketRoom(user.id, user.organizationId),
      'completeQuickStart',
      {
        quickType: userQuickStart.quickType,
        createdAt: userQuickStart.createdAt,
      },
    );
  }
}
