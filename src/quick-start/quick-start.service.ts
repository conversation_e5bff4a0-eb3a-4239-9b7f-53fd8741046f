import { Injectable } from '@nestjs/common';
import { PrismaService } from '../common/services/prisma.service';
import { UserDto } from '../common/dtos/user.dto';
import { GetQuickStartDto } from './dtos/get-quick-start.dto';
import { UtilService } from '../common/services/util.service';
import { QuickTypeEnum } from './enums/quick-type.enum';
import { PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { QuickStartGateway } from './quick-start.gateway';
import { OrganizationTypeEnum } from '../organization/enums/organization-type.enum';

@Injectable()
export class QuickStartService {
  constructor(
    private prismaService: PrismaService,
    private quickStartGateway: QuickStartGateway,
    private utilService: UtilService,
  ) {}

  async getQuickStarts(user: UserDto, dto: GetQuickStartDto) {
    const result: {
      quickType: QuickTypeEnum;
      createdAt?: Date;
    }[] = [];
    const showQuickStart = (
      await this.prismaService.user.findFirstOrThrow({
        where: { id: user.id },
      })
    ).showQuickStart;
    if (!showQuickStart) {
      return [];
    }
    const organization = await this.prismaService.organization.findFirstOrThrow(
      {
        where: { id: user.organizationId },
      },
    );
    const notInQuickTypes: QuickTypeEnum[] = [];
    if (organization.organizationType != OrganizationTypeEnum.COMPANY) {
      notInQuickTypes.push(QuickTypeEnum.AFFILIATE_CREATE);
    }
    if (
      ![OrganizationTypeEnum.COMPANY, OrganizationTypeEnum.AFFILIATE].includes(
        organization.organizationType,
      )
    ) {
      notInQuickTypes.push(QuickTypeEnum.SUPPLIER_CREATE);
    }
    const userQuickStarts = await this.prismaService.userQuickStart.findMany({
      where: { userId: user.id },
    });
    const quickStarts = await this.prismaService.quickStart.findMany({
      where: {
        frontSlug: dto.frontSlug,
        quickType: { notIn: notInQuickTypes },
      },
      orderBy: { order: 'asc' },
    });
    for (const qs of quickStarts) {
      const userQuickStart = userQuickStarts.find(
        (uqs) => uqs.quickType == qs.quickType,
      );
      result.push({
        quickType: qs.quickType,
        createdAt: userQuickStart?.createdAt,
      });
    }
    return result;
  }

  async getQuickTypes() {
    return this.utilService.enumToArray(QuickTypeEnum);
  }

  async completeQuickStart(
    user: UserDto,
    quickType: QuickTypeEnum,
    tx?: Omit<PrismaClient, ITXClientDenyList>,
  ): Promise<void> {
    const dbService = tx ?? this.prismaService;
    let userQuickStart = await dbService.userQuickStart.findFirst({
      where: { userId: user.id, quickType: quickType },
    });
    if (userQuickStart) {
      return;
    }
    userQuickStart = await dbService.userQuickStart.create({
      data: { userId: user.id, quickType: quickType },
    });
    this.quickStartGateway.completeQuickStart(user, userQuickStart);
  }
}
