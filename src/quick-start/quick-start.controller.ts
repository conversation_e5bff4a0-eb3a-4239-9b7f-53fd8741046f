import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { QuickStartService } from './quick-start.service';
import { User } from '../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../common/dtos/general-response.dto';
import { UserDto } from '../common/dtos/user.dto';
import { GetQuickStartDto } from './dtos/get-quick-start.dto';

@ApiTags('Quick Starts')
@Controller('quick-starts')
export class QuickStartController {
  constructor(private quickStartService: QuickStartService) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getQuickStarts(
    @User() user: UserDto,
    @Query() dto: GetQuickStartDto,
  ): Promise<GeneralResponseDto> {
    const quickStarts = await this.quickStartService.getQuickStarts(user, dto);
    return new GeneralResponseDto().setData(quickStarts);
  }

  @Get('types')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getQuickTypes(): Promise<GeneralResponseDto> {
    const quickTypes = await this.quickStartService.getQuickTypes();
    return new GeneralResponseDto().setData(quickTypes);
  }
}
