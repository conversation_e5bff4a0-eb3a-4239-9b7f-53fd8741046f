import { Module } from '@nestjs/common';
import { QuickStartController } from './quick-start.controller';
import { QuickStartService } from './quick-start.service';
import { CommonModule } from '../common/common.module';
import { QuickStartGateway } from './quick-start.gateway';

@Module({
  imports: [CommonModule],
  controllers: [QuickStartController],
  providers: [QuickStartService, QuickStartGateway],
  exports: [QuickStartService],
})
export class QuickStartModule {}
