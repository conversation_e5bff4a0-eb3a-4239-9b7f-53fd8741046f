import { Body, Controller, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { CarbonService } from './carbon.service';
import { User } from '../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../common/dtos/general-response.dto';
import { UserDto } from '../common/dtos/user.dto';
import { CreateFacilityDto } from './dtos/create-facility.dto';
import { CreateReportDto } from './dtos/create-report.dto';
import { CreateIsoStandardDto } from './dtos/create-iso-standard.dto';

@ApiTags('Carbon')
@Controller('carbon')
export class CarbonController {
  constructor(private carbonService: CarbonService) {}

  @Post('facilities')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createFacility(
    @User() user: UserDto,
    @Body() dto: CreateFacilityDto,
  ): Promise<GeneralResponseDto> {
    const { status, data, messages, errors } =
      await this.carbonService.createFacility(user, dto);
    return new GeneralResponseDto(status)
      .setData(data)
      .setMessages(messages)
      .setErrors(errors);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(
    @User() user: UserDto,
    @Body() dto: CreateReportDto,
  ): Promise<GeneralResponseDto> {
    const { status, data, messages, errors } =
      await this.carbonService.createReport(user, dto);
    return new GeneralResponseDto(status)
      .setData(data)
      .setMessages(messages)
      .setErrors(errors);
  }

  @Post('iso-standards')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createIsoStandard(
    @User() user: UserDto,
    @Body() dto: CreateIsoStandardDto,
  ): Promise<GeneralResponseDto> {
    const { status, data, messages, errors } =
      await this.carbonService.createIsoStandard(user, dto);
    return new GeneralResponseDto(status)
      .setData(data)
      .setMessages(messages)
      .setErrors(errors);
  }
}
