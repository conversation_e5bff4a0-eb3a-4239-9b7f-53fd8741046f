import { Injectable } from '@nestjs/common';
import { PrismaService } from '../common/services/prisma.service';
import { ApiClientService } from '../common/services/api-client.service';
import { UserDto } from '../common/dtos/user.dto';
import { CreateFacilityDto } from './dtos/create-facility.dto';
import { ErrorDto } from '../common/dtos/exception-response.dto';
import { QuickTypeEnum } from '../quick-start/enums/quick-type.enum';
import { QuickStartService } from '../quick-start/quick-start.service';
import { CreateReportDto } from './dtos/create-report.dto';
import { CreateIsoStandardDto } from './dtos/create-iso-standard.dto';
import { MetricService } from '../design/services/metric.service';
import { MetricCategoryTypeEnum } from '../design/enums/metric-category-type.enum';

@Injectable()
export class CarbonService {
  constructor(
    private prismaService: PrismaService,
    private apiClientService: ApiClientService,
    private quickStartService: QuickStartService,
    private metricService: MetricService,
  ) {}

  private getErrors(result: { status: boolean; errors?: object }): ErrorDto[] {
    const errors: ErrorDto[] = [];
    if (result.errors) {
      Object.keys(result.errors).forEach((key) => {
        errors.push({ property: key, messages: result.errors[key] });
      });
    }
    return errors;
  }

  async createFacility(user: UserDto, dto: CreateFacilityDto) {
    const result = await this.apiClientService.createFacility(user, dto);
    const errors = this.getErrors(result);
    if (errors.length == 0) {
      await this.quickStartService.completeQuickStart(
        user,
        QuickTypeEnum.CARBON_FACILITY_CREATE,
      );
    }
    return {
      status: result.status,
      data: result.data,
      messages: result.messages,
      errors: errors,
    };
  }

  async createReport(user: UserDto, dto: CreateReportDto) {
    const result = await this.apiClientService.createReport(user, dto);
    const errors = this.getErrors(result);
    if (errors.length > 0) {
      return {
        status: result.status,
        data: result.data,
        messages: result.messages,
        errors: errors,
      };
    }

    console.log(result.data); // TODO: remove this line

    await this.prismaService.$transaction(async (tx) => {
      // await this.metricService.createMetric(
      //   user,
      //   {
      //     isActivity: true,
      //     facilityIds: [],
      //     metricName: '',
      //     categoryType: MetricCategoryTypeEnum.QUANTITATIVE,
      //     unitType: '',
      //   },
      //   { tx: tx },
      // );

      await this.quickStartService.completeQuickStart(
        user,
        QuickTypeEnum.CARBON_REPORT_CREATE,
        tx,
      );
    });
    return {
      status: result.status,
      data: result.data,
      messages: result.messages,
      errors: errors,
    };
  }

  async createIsoStandard(user: UserDto, dto: CreateIsoStandardDto) {
    const result = await this.apiClientService.createIsoStandard(user, dto);
    const errors = this.getErrors(result);
    if (errors.length == 0) {
      await this.quickStartService.completeQuickStart(
        user,
        QuickTypeEnum.CARBON_START_ISO_STANDARD,
      );
    }
    return {
      status: result.status,
      data: result.data,
      messages: result.messages,
      errors: errors,
    };
  }
}
