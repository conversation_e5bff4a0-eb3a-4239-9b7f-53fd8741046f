import { <PERSON>du<PERSON> } from '@nestjs/common';
import { CarbonController } from './carbon.controller';
import { CarbonService } from './carbon.service';
import { CommonModule } from '../common/common.module';
import { QuickStartModule } from '../quick-start/quick-start.module';
import { DesignModule } from '../design/design.module';

@Module({
  imports: [QuickStartModule, DesignModule, CommonModule],
  controllers: [CarbonController],
  providers: [CarbonService],
  exports: [CarbonService],
})
export class CarbonModule {}
