import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class CreateIsoStandardValueDto {
  @ApiProperty({ type: String })
  @IsString()
  key: string;

  @ApiProperty({ type: String })
  value: any;
}

export class CreateIsoStandardDto {
  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  id?: number;

  @ApiProperty({ type: CreateIsoStandardValueDto, isArray: true })
  @ValidateNested({ each: true })
  @Type(() => CreateIsoStandardValueDto)
  @IsArray()
  values: CreateIsoStandardValueDto[];
}
