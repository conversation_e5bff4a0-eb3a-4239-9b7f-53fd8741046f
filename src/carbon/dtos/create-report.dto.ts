import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Array,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class CreateReportPayloadDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  inputId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  groupValueId?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  numericValue?: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  textValue?: string;
}

export class CreateReportDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  formId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  facilityId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  resultId?: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  year: number;

  @ApiProperty({ type: Number, isArray: true })
  @IsArray()
  @IsNumber({}, { each: true })
  monthIds: number[];

  @ApiProperty({ type: Number })
  @IsNumber()
  amount: number;

  @ApiProperty({ type: Number, isArray: true, required: false })
  @IsArray()
  @IsNumber({ maxDecimalPlaces: 0 }, { each: true })
  @IsOptional()
  fileIds?: number[];

  @ApiProperty({ type: CreateReportPayloadDto, isArray: true, required: false })
  @ValidateNested({ each: true })
  @Type(() => CreateReportPayloadDto)
  @IsArray()
  @IsOptional()
  payload?: CreateReportPayloadDto[];

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  description?: string;
}
