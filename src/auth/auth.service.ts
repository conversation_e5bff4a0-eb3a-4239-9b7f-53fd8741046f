import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../common/services/prisma.service';
import { UserDto } from '../common/dtos/user.dto';
import { UpdateUserProfileDto } from './dtos/update-user-profile.dto';
import { UpdatePasswordDto } from './dtos/update-password.dto';
import {
  Prisma,
  User,
  User as UserModel,
  UserActivation,
  UserForgot,
  UserPending,
} from '@prisma/client';
import { LoginDto } from './dtos/login.dto';
import * as bcrypt from 'bcrypt';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../common/dtos/user-data.dto';
import { UserService } from '../common/services/user.service';
import { AcceptInvitationDto } from './dtos/accept-invitation.dto';
import { RoleEnum } from '../common/enums/role.enum';
import { CreateAccessTokenDto } from './dtos/create-access-token.dto';
import { SignUpDto } from './dtos/sign-up.dto';
import { UtilService } from '../common/services/util.service';
import { ActivateUserDto } from './dtos/activate-user.dto';
import { OrganizationTypeEnum } from '../organization/enums/organization-type.enum';
import { MailService } from '../mail/mail.service';
import { ApiClientService } from '../common/services/api-client.service';
import { GetPermissionDto } from './dtos/get-permission.dto';
import { PermissionEnum } from '../common/enums/permission.enum';
import { Request } from 'express';
import { UpdateForgotPasswordDto } from './dtos/update-forgot-password.dto';
import { SendForgotMailDto } from './dtos/send-forgot-mail.dto';
import { addHours, addMinutes } from 'date-fns';
import { NotificationService } from '../notification/notification.service';
import { MaterialTypeEnum } from '../organization/enums/material-type.enum';
import { CreateCodeDto } from './dtos/create-code.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private prismaService: PrismaService,
    private userService: UserService,
    private utilService: UtilService,
    private mailService: MailService,
    private apiClientService: ApiClientService,
    private notificationService: NotificationService,
  ) {}

  async getUserProfile(user: UserDto) {
    const profile = await this.prismaService.user.findFirstOrThrow({
      where: { id: user.id },
      include: {
        userOrganizations: {
          where: { organizationId: user.organizationId },
          include: { organization: true },
        },
        userPermissions: {
          where: { organizationId: user.organizationId },
          include: { permission: { select: { id: true, name: true } } },
        },
      },
    });
    const userOrganization = profile.userOrganizations[0];
    const firstAffiliate = await this.prismaService.organization.findFirst({
      where: {
        mainOrganizations: {
          some: {
            mainOrganizationId: userOrganization.organization.id,
            relationType: OrganizationTypeEnum.AFFILIATE,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });
    return {
      id: profile.id,
      name: profile.name,
      surname: profile.surname,
      fullName: `${profile.name} ${profile.surname}`,
      email: profile.email,
      phone: profile.phone,
      image: profile.image,
      description: profile.description,
      timezone: profile.timezone,
      isOtp: profile.isOtp,
      showQuickStart: profile.showQuickStart,
      roleId: userOrganization.roleId,
      title: userOrganization.title,
      organizationId: userOrganization.organization.id,
      organizationName: userOrganization.organization.name,
      organizationImage: userOrganization.organization.image,
      organizationMaterialType: userOrganization.organization.materialType,
      organizationType: userOrganization.organization.organizationType,
      organizationAffiliateType: userOrganization.organization.affiliateType,
      firstAffiliateId: firstAffiliate ? firstAffiliate.id : undefined,
      permissions: profile.userPermissions.map((p) => p.permission),
    };
  }

  async getUserProfileSummary(user: UserDto) {
    const profile = await this.prismaService.user.findFirstOrThrow({
      where: { id: user.id },
    });
    return {
      id: profile.id,
      fullName: `${profile.name} ${profile.surname}`,
      email: profile.email,
      image: profile.image,
    };
  }

  async getUserUnreadCounts(user: UserDto) {
    const unreadNotificationCount =
      await this.notificationService.getUnreadCountsByUser(user);
    return {
      unreadNotificationCount: unreadNotificationCount,
    };
  }

  async getRoles() {
    const roles = await this.prismaService.role.findMany({
      select: { id: true, name: true, color: true },
    });
    return roles;
  }

  async getPermissions(dto: GetPermissionDto) {
    const keys: PermissionEnum[] = [];
    if (dto.organizationType == OrganizationTypeEnum.AFFILIATE) {
      keys.push(
        PermissionEnum.CARBON,
        PermissionEnum.ANALYSIS,
        PermissionEnum.PROCEDURE,
      );
    } else if (dto.organizationType == OrganizationTypeEnum.SUPPLIER) {
      keys.push(PermissionEnum.CARBON, PermissionEnum.PROCEDURE);
    }
    const permissions = await this.prismaService.permission.findMany({
      select: { id: true, name: true },
      where: { key: { in: keys }, isVisible: true },
      orderBy: { id: 'asc' },
    });
    return permissions;
  }

  async login(dto: LoginDto, request: Request): Promise<UserDataDto> {
    const user = await this.prismaService.user.findFirst({
      where: { email: dto.email },
      include: { userOrganizations: true },
    });
    if (!user) {
      throw new BadRequestException('E-mail veya şifre yanlış.');
    }
    const isMatch = await bcrypt.compare(dto.password, user.password);
    if (!isMatch) {
      throw new BadRequestException('E-mail veya şifre yanlış.');
    }
    if (!user.isActive) {
      throw new ForbiddenException(
        'Hesabınıza gelen aktivasyon mailini onaylayınız.',
      );
    }
    if (user.userOrganizations.length == 0) {
      throw new BadRequestException('Mevcut şirketiniz bulunmamaktadır.');
    }
    const permissions = await this.prismaService.permission.findMany({
      select: { id: true, name: true },
      where: {
        userPermissions: {
          some: {
            userId: user.id,
            organizationId: user.userOrganizations[0].organizationId,
          },
        },
      },
    });

    const result = plainToInstance(UserDataDto, user, {
      excludeExtraneousValues: true,
      groups: ['sensitiveField', 'organization'],
    });
    result['token'] = await this.userService.createAccessToken({
      userId: user.id,
      request: request,
    });
    result['permissions'] = permissions;
    return result;
  }

  async logout(user: UserDto): Promise<void> {
    await this.prismaService.userSession.delete({
      where: { jwtId: user.jwtId },
    });
    this.apiClientService.logout(user).catch((err) => this.logger.error(err));
  }

  async signUp(dto: SignUpDto, request: Request): Promise<void> {
    const hash = await bcrypt.hash(dto.password, 10);
    const token = this.utilService.getRandomString(
      this.utilService.getRandomInteger(100, 150),
    );
    const user = await this.prismaService.$transaction(async (tx) => {
      const user = await tx.user.create({
        data: {
          name: dto.name,
          surname: dto.surname,
          email: dto.email,
          password: hash,
        },
      });
      await tx.userActivation.create({
        data: {
          userId: user.id,
          token: token,
        },
      });
      return user;
    });
    this.mailService
      .sendActivation({
        request: request,
        token: token,
        email: user.email,
        fullName: `${user.name} ${user.surname}`,
      })
      .catch((err) => this.logger.error(err));
  }

  async activateUser(
    dto: ActivateUserDto,
    activation: UserActivation,
    request: Request,
  ) {
    const role = await this.prismaService.role.findFirstOrThrow({
      where: { key: RoleEnum.MANAGER },
    });
    const accessToken = await this.prismaService.$transaction(async (tx) => {
      const user = await tx.user.update({
        where: { id: activation.userId },
        data: { isActive: true },
      });
      const organizationName = `${user.name} ${user.surname} Workspace`;
      const organization = await tx.organization.create({
        data: {
          name: organizationName,
          materialType: MaterialTypeEnum.SINGLE,
          organizationType: OrganizationTypeEnum.COMPANY,
          userOrganizations: { create: { userId: user.id, roleId: role.id } },
        },
      });
      await tx.userActivation.delete({ where: { id: activation.id } });
      return await this.userService.createAccessToken({
        userId: user.id,
        organizationId: organization.id,
        request: request,
        tx: tx,
      });
    });
    return { accessToken: accessToken };
  }

  async createAccessToken(
    user: UserDto,
    dto: CreateAccessTokenDto,
    request: Request,
  ): Promise<string> {
    const accessToken = await this.userService.createAccessToken({
      userId: user.id,
      organizationId: dto.organizationId,
      request: request,
    });
    return accessToken;
  }

  async acceptInvitation(
    dto: AcceptInvitationDto,
    request: Request,
    pending: Prisma.UserPendingGetPayload<{ include: { role: true } }>,
    user?: UserModel,
  ) {
    const permissionIds = this.utilService.jsonToNumberList(
      pending.permissionIds,
    );
    const result = await this.prismaService.$transaction(async (tx) => {
      if (!user) {
        const hash = await bcrypt.hash(dto.password, 10);
        user = await tx.user.create({
          data: {
            name: dto.name,
            surname: dto.surname,
            email: pending.email,
            password: hash,
            phone: pending.phone,
            isActive: true,
          },
        });
      }
      const agreement = await tx.userAgreement.findFirst({
        where: { userId: user.id },
      });
      await tx.userAgreement.upsert({
        where: { id: agreement?.id ?? 0 },
        create: {
          userId: user.id,
          personalData: dto.personalData,
          kvkk: dto.kvkk,
          userAgreement: dto.userAgreement,
          privacyPolicy: dto.privacyPolicy,
        },
        update: {
          personalData: dto.personalData,
          kvkk: dto.kvkk,
          userAgreement: dto.userAgreement,
          privacyPolicy: dto.privacyPolicy,
        },
      });
      if (pending.role.key == RoleEnum.MANAGER) {
        const managerUserOrg = await tx.userOrganization.findFirst({
          where: {
            organizationId: pending.organizationId,
            role: { key: RoleEnum.MANAGER },
          },
        });
        if (managerUserOrg) {
          await tx.userOrganization.update({
            where: { id: managerUserOrg.id },
            data: {
              role: { connect: { key: RoleEnum.ASSISTANT } },
            },
          });
        }
      }
      await tx.userOrganization.create({
        data: {
          userId: user.id,
          organizationId: pending.organizationId,
          roleId: pending.roleId,
        },
      });
      if (permissionIds.length > 0) {
        await tx.userPermission.createMany({
          data: permissionIds.map((pId) => {
            return {
              userId: user.id,
              organizationId: pending.organizationId,
              permissionId: pId,
            };
          }),
        });
      }
      await tx.userPending.delete({ where: { id: pending.id } });
      const accessToken = await this.userService.createAccessToken({
        userId: user.id,
        organizationId: pending.organizationId,
        request: request,
        tx: tx,
      });
      return {
        id: user.id,
        name: user.name,
        surname: user.surname,
        email: user.email,
        organizationId: pending.organizationId,
        token: accessToken,
      };
    });

    const permissions = await this.prismaService.permission.findMany({
      select: { id: true, name: true },
      where: { id: { in: permissionIds } },
    });
    result['permissions'] = permissions;
    return result;
  }

  async createCode(dto: CreateCodeDto, pending: UserPending) {
    const code = this.utilService.getRandomString(6).toUpperCase();
    const updatedPending = await this.prismaService.userPending.update({
      where: { id: pending.id },
      data: { code: code, codeExpiredAt: addMinutes(new Date(), 3) },
    });
    this.mailService
      .sendOrganizationInviteCode({ pending: updatedPending })
      .catch((err) => this.logger.error(err));
    return code;
  }

  async sendForgotMail(
    dto: SendForgotMailDto,
    user: User,
    request: Request,
  ): Promise<void> {
    const token = this.utilService.getRandomString(
      this.utilService.getRandomInteger(100, 150),
    );
    await this.prismaService.userForgot.create({
      data: {
        userId: user.id,
        token: token,
        expiredAt: addHours(new Date(), 3),
      },
    });
    this.mailService
      .sendForgotPassword({
        request: request,
        token: token,
        email: user.email,
        fullName: `${user.name} ${user.surname}`,
      })
      .catch((err) => this.logger.error(err));
  }

  async updateForgotPassword(
    dto: UpdateForgotPasswordDto,
    forgot: UserForgot,
  ): Promise<void> {
    const hash = await bcrypt.hash(dto.password, 10);
    await this.prismaService.$transaction(async (tx) => {
      await tx.user.update({
        where: { id: forgot.userId },
        data: { password: hash },
      });
      await tx.userForgot.deleteMany({ where: { userId: forgot.userId } });
    });
  }

  async updateUserProfile(
    user: UserDto,
    dto: UpdateUserProfileDto,
  ): Promise<void> {
    await this.prismaService.$transaction(async (tx) => {
      const updatedUser = await tx.user.update({
        where: { id: user.id },
        data: {
          name: dto.name,
          surname: dto.surname,
          phone: dto.phone,
          image: dto.image,
          description: dto.description,
          timezone: dto.timezone,
          showQuickStart: dto.showQuickStart,
        },
      });
      await tx.userOrganization.updateMany({
        where: { userId: user.id, organizationId: user.organizationId },
        data: { title: dto.title },
      });
      const isCarbonAccess = user.permissions.some(
        (p) => p.key == PermissionEnum.CARBON,
      );
      if (isCarbonAccess) {
        await this.apiClientService.updateUserProfile(
          user,
          `${updatedUser.name} ${updatedUser.surname}`,
          updatedUser.phone,
          updatedUser.image,
          { title: dto.title },
        );
      }
    });
  }

  async updatePassword(
    user: UserDto,
    dto: UpdatePasswordDto,
    isOtp: boolean,
  ): Promise<void> {
    const hash = await bcrypt.hash(dto.newPassword, 10);
    await this.prismaService.$transaction(async (tx) => {
      await tx.user.update({
        where: { id: user.id },
        data: { password: hash, isOtp: false },
      });
      if (isOtp) {
        const agreement = await tx.userAgreement.findFirst({
          where: { userId: user.id },
        });
        await tx.userAgreement.upsert({
          where: { id: agreement?.id ?? 0 },
          create: {
            userId: user.id,
            personalData: dto.personalData,
            kvkk: dto.kvkk,
            userAgreement: dto.userAgreement,
            privacyPolicy: dto.privacyPolicy,
          },
          update: {
            personalData: dto.personalData,
            kvkk: dto.kvkk,
            userAgreement: dto.userAgreement,
            privacyPolicy: dto.privacyPolicy,
          },
        });
      }
    });
  }

  async isAdmin(user: UserDto): Promise<{ isAdmin: boolean }> {
    return { isAdmin: user.isAdmin };
  }
}
