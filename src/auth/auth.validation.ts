import {
  BadRequestException,
  Injectable,
  NotAcceptableException,
} from '@nestjs/common';
import { PrismaService } from '../common/services/prisma.service';
import { UserDto } from '../common/dtos/user.dto';
import { AcceptInvitationDto } from './dtos/accept-invitation.dto';
import { CreateAccessTokenDto } from './dtos/create-access-token.dto';
import { SignUpDto } from './dtos/sign-up.dto';
import { ActivateUserDto } from './dtos/activate-user.dto';
import { User, UserActivation, UserForgot } from '@prisma/client';
import { RoleEnum } from '../common/enums/role.enum';
import { GetPermissionDto } from './dtos/get-permission.dto';
import { OrganizationTypeEnum } from '../organization/enums/organization-type.enum';
import { UpdatePasswordDto } from './dtos/update-password.dto';
import { UpdateForgotPasswordDto } from './dtos/update-forgot-password.dto';
import { SendForgotMailDto } from './dtos/send-forgot-mail.dto';
import * as bcrypt from 'bcrypt';
import { CreateCodeDto } from './dtos/create-code.dto';
import { differenceInSeconds } from 'date-fns';

@Injectable()
export class AuthValidation {
  constructor(private prismaService: PrismaService) {}

  async getPermissions(dto: GetPermissionDto) {
    if (dto.organizationType == OrganizationTypeEnum.COMPANY) {
      throw new BadRequestException('Geçersiz şirket tipi.');
    }
  }

  async signUp(dto: SignUpDto): Promise<void> {
    const emailExist = await this.prismaService.user.findFirst({
      where: { email: dto.email },
    });
    if (emailExist) {
      throw new BadRequestException('E-mail adresi kullanılmaktadır.');
    }
  }

  async activateUser(dto: ActivateUserDto): Promise<UserActivation> {
    const activation = await this.prismaService.userActivation.findFirst({
      where: { token: dto.token },
    });
    if (!activation) {
      throw new BadRequestException('Geçersiz işlem');
    }
    return activation;
  }

  async createAccessToken(
    user: UserDto,
    dto: CreateAccessTokenDto,
  ): Promise<void> {
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: {
        userId: user.id,
        organizationId: dto.organizationId,
      },
    });
  }

  async acceptInvitation(dto: AcceptInvitationDto) {
    const pending = await this.prismaService.userPending.findFirst({
      where: {
        token: dto.token,
        //code: dto.code,
        OR: [{ tokenExpiredAt: null }, { tokenExpiredAt: { gt: new Date() } }],
        //codeExpiredAt: { gt: new Date() },
      },
      include: { role: true },
    });
    if (!pending) {
      throw new NotAcceptableException('Geçersiz işlem.');
    }
    const user = await this.prismaService.user.findFirst({
      where: { email: pending.email },
      include: { userOrganizations: true, agreements: true },
    });
    if (user) {
      const userInOrg = user.userOrganizations.some(
        (uo) => uo.organizationId == pending.organizationId,
      );
      if (userInOrg) {
        throw new NotAcceptableException('Zaten bu şirkette bulunuyorsunuz.');
      }
      if (user.userOrganizations.length > 0) {
        throw new NotAcceptableException(
          'Zaten mevcut bir şirketiniz bulunmaktadır.',
        );
      }
      delete user.userOrganizations;
    } else {
      if (!dto.name || !dto.surname || !dto.password) {
        throw new BadRequestException('Eksik alanları doldurunuz.');
      }
    }
    if (!pending.roleId) {
      const role = await this.prismaService.role.findFirstOrThrow({
        where: { key: RoleEnum.ASSISTANT },
      });
      pending.roleId = role.id;
      pending.role = role;
    }
    return { pending: pending, user: user };
  }

  async createCode(dto: CreateCodeDto) {
    const pending = await this.prismaService.userPending.findFirst({
      where: {
        token: dto.token,
        OR: [{ tokenExpiredAt: null }, { tokenExpiredAt: { gt: new Date() } }],
      },
    });
    if (!pending) {
      throw new NotAcceptableException('Geçersiz işlem.');
    }
    if (pending.codeExpiredAt) {
      const diffSeconds = differenceInSeconds(
        pending.codeExpiredAt,
        new Date(),
      );
      if (diffSeconds > 0) {
        throw new BadRequestException(
          `Maile gelen kodu kullanınız. ${diffSeconds} saniye sonra tekrar kod üretebilirsiniz.`,
        );
      }
    }
    return pending;
  }

  async sendForgotMail(dto: SendForgotMailDto): Promise<User> {
    const user = await this.prismaService.user.findFirst({
      where: { email: dto.email },
    });
    if (!user) {
      throw new BadRequestException('Kullanıcı bulunamadı.');
    }
    const forgotCount = await this.prismaService.userForgot.count({
      where: { user: { email: dto.email }, expiredAt: { gte: new Date() } },
    });
    if (forgotCount >= 5) {
      throw new BadRequestException(
        "5'ten fazla işlem yapamazsınız. İşleminize kısıt getirildi.",
      );
    }
    return user;
  }

  async updateForgotPassword(
    dto: UpdateForgotPasswordDto,
  ): Promise<UserForgot> {
    const forgot = await this.prismaService.userForgot.findFirst({
      where: { token: dto.token },
    });
    if (!forgot) {
      throw new NotAcceptableException('Geçersiz işlem.');
    }
    if (forgot.expiredAt < new Date()) {
      throw new NotAcceptableException('Bağlantının süresi dolmuştur.');
    }
    return forgot;
  }

  async updatePassword(
    user: UserDto,
    dto: UpdatePasswordDto,
  ): Promise<boolean> {
    const userProfile = await this.prismaService.user.findFirstOrThrow({
      where: { id: user.id },
    });
    if (userProfile.isOtp) {
      if (
        dto.personalData === undefined ||
        dto.kvkk === undefined ||
        dto.userAgreement === undefined ||
        dto.privacyPolicy === undefined
      ) {
        throw new BadRequestException('Sözleşmelerin onay durumunu giriniz.');
      }
    } else {
      if (!dto.oldPassword) {
        throw new BadRequestException('Eski şifrenizi giriniz.');
      }
      const isMatch = await bcrypt.compare(
        dto.oldPassword,
        userProfile.password,
      );
      if (!isMatch) {
        throw new BadRequestException('Eski şifre yanlış.');
      }
    }
    return userProfile.isOtp;
  }
}
