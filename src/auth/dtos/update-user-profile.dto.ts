import {
  IsBoolean,
  IsInt,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUserProfileDto {
  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  surname?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  phone?: string;

  @ApiProperty({ type: String, required: false })
  @IsUrl()
  @IsOptional()
  image?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  title?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;

  @ApiProperty({
    type: Number,
    required: false,
    description: 'timezone seconds',
  })
  @IsInt()
  @Min(-12 * 3600)
  @Max(14 * 3600)
  @IsOptional()
  timezone?: number;

  @ApiProperty({ type: Boolean, required: false })
  @IsBoolean()
  @IsOptional()
  showQuickStart?: boolean;
}
