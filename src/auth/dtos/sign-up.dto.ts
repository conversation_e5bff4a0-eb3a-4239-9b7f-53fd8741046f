import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class SignUpDto {
  @ApiProperty({ type: String })
  @IsEmail()
  @MinLength(1)
  @Transform(({ value }) => value.toLowerCase())
  email: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  surname: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(6)
  @MaxLength(32)
  password: string;
}
