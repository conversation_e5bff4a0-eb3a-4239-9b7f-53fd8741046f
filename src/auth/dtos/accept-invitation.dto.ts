import {
  IsBoolean,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AcceptInvitationDto {
  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  token: string;

  // @ApiProperty({ type: String })
  // @IsString()
  // @MinLength(1)
  // code: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  surname?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  @MinLength(6)
  @MaxLength(32)
  password?: string;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  personalData: boolean;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  kvkk: boolean;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  userAgreement: boolean;

  @ApiProperty({ type: Boolean })
  @IsBoolean()
  privacyPolicy: boolean;
}
