import { ApiProperty } from '@nestjs/swagger';
import { IsEnum } from 'class-validator';
import { OrganizationTypeEnum } from '../../organization/enums/organization-type.enum';
import { Transform } from 'class-transformer';

export class GetPermissionDto {
  @ApiProperty({ type: Number, enum: OrganizationTypeEnum })
  @IsEnum(OrganizationTypeEnum)
  @Transform(({ value }) => +value)
  organizationType: OrganizationTypeEnum;
}
