import { Body, Controller, Get, Post, Put, Query, Req } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { User, Public } from '../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../common/dtos/general-response.dto';
import { UserDto } from '../common/dtos/user.dto';
import { UpdateUserProfileDto } from './dtos/update-user-profile.dto';
import { UpdatePasswordDto } from './dtos/update-password.dto';
import { AuthValidation } from './auth.validation';
import { LoginDto } from './dtos/login.dto';
import { AcceptInvitationDto } from './dtos/accept-invitation.dto';
import { CreateAccessTokenDto } from './dtos/create-access-token.dto';
import { SignUpDto } from './dtos/sign-up.dto';
import { UserDataDto } from '../common/dtos/user-data.dto';
import { ActivateUserDto } from './dtos/activate-user.dto';
import { GetPermissionDto } from './dtos/get-permission.dto';
import { Request } from 'express';
import { UpdateForgotPasswordDto } from './dtos/update-forgot-password.dto';
import { SendForgotMailDto } from './dtos/send-forgot-mail.dto';
import { CreateCodeDto } from './dtos/create-code.dto';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private authValidation: AuthValidation,
  ) {}

  @Get('check')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async authCheck() {
    return new GeneralResponseDto();
  }

  @Get('profile')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getUserProfile(@User() user: UserDto) {
    const profile = await this.authService.getUserProfile(user);
    return new GeneralResponseDto().setData(profile);
  }

  @Get('profile/summary')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getUserProfileSummary(@User() user: UserDto) {
    const profile = await this.authService.getUserProfileSummary(user);
    return new GeneralResponseDto().setData(profile);
  }

  @Get('unread-counts')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getUserUnreadCounts(@User() user: UserDto) {
    const profile = await this.authService.getUserUnreadCounts(user);
    return new GeneralResponseDto().setData(profile);
  }

  @Get('roles')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getRoles(): Promise<GeneralResponseDto> {
    const roles = await this.authService.getRoles();
    return new GeneralResponseDto().setData(roles);
  }

  @Get('permissions')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getPermissions(
    @Query() dto: GetPermissionDto,
  ): Promise<GeneralResponseDto> {
    await this.authValidation.getPermissions(dto);
    const permissions = await this.authService.getPermissions(dto);
    return new GeneralResponseDto().setData(permissions);
  }

  @Post('login')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async login(
    @Body() dto: LoginDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto<UserDataDto>> {
    const profile = await this.authService.login(dto, request);
    return new GeneralResponseDto<UserDataDto>().setData(profile);
  }

  @Post('logout')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async logout(@User() user: UserDto): Promise<GeneralResponseDto> {
    await this.authService.logout(user);
    return new GeneralResponseDto();
  }

  @Post('sign-up')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async signUp(
    @Body() dto: SignUpDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    await this.authValidation.signUp(dto);
    await this.authService.signUp(dto, request);
    return new GeneralResponseDto();
  }

  @Post('activation')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async activateUser(
    @Body() dto: ActivateUserDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    const activation = await this.authValidation.activateUser(dto);
    const result = await this.authService.activateUser(
      dto,
      activation,
      request,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('access-token')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto<string> })
  async createAccessToken(
    @User() user: UserDto,
    @Body() dto: CreateAccessTokenDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    await this.authValidation.createAccessToken(user, dto);
    const accessToken = await this.authService.createAccessToken(
      user,
      dto,
      request,
    );
    return new GeneralResponseDto<string>().setData(accessToken);
  }

  @Post('accept-invitation')
  @Public()
  @ApiOperation({
    summary:
      'First, a request is made with only the token value. If status returns false, the request is sent to this endpoint again. (In the 2nd request, the request is sent with all data)',
  })
  @ApiOkResponse({ type: GeneralResponseDto })
  async acceptInvitation(
    @Body() dto: AcceptInvitationDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    const { pending, user } = await this.authValidation.acceptInvitation(dto);
    const result = await this.authService.acceptInvitation(
      dto,
      request,
      pending,
      user,
    );
    return new GeneralResponseDto().setData(result);
  }

  @Post('accept-invitation/code')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createCode(@Body() dto: CreateCodeDto): Promise<GeneralResponseDto> {
    const pending = await this.authValidation.createCode(dto);
    await this.authService.createCode(dto, pending);
    return new GeneralResponseDto();
  }

  @Post('password/forgot/mail')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async sendForgotMail(
    @Body() dto: SendForgotMailDto,
    @Req() request: Request,
  ): Promise<GeneralResponseDto> {
    const user = await this.authValidation.sendForgotMail(dto);
    await this.authService.sendForgotMail(dto, user, request);
    return new GeneralResponseDto();
  }

  @Put('password/forgot')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateForgotPassword(
    @Body() dto: UpdateForgotPasswordDto,
  ): Promise<GeneralResponseDto> {
    const forgot = await this.authValidation.updateForgotPassword(dto);
    await this.authService.updateForgotPassword(dto, forgot);
    return new GeneralResponseDto();
  }

  @Put('profile')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateUserProfile(
    @User() user: UserDto,
    @Body() dto: UpdateUserProfileDto,
  ): Promise<GeneralResponseDto> {
    await this.authService.updateUserProfile(user, dto);
    return new GeneralResponseDto();
  }

  @Put('password')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updatePassword(
    @User() user: UserDto,
    @Body() dto: UpdatePasswordDto,
  ): Promise<GeneralResponseDto> {
    const isOtp = await this.authValidation.updatePassword(user, dto);
    await this.authService.updatePassword(user, dto, isOtp);
    return new GeneralResponseDto();
  }

  @Get('is-admin')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto<{ isAdmin: boolean }> })
  async isAdmin(@User() user: UserDto): Promise<GeneralResponseDto> {
    const isAdmin = await this.authService.isAdmin(user);
    return new GeneralResponseDto().setData(isAdmin);
  }
}
