import { Module } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { CommonModule } from '../common/common.module';
import { AuthValidation } from './auth.validation';
import { MailModule } from '../mail/mail.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [MailModule, NotificationModule, CommonModule],
  controllers: [AuthController],
  providers: [AuthService, AuthValidation],
  exports: [],
})
export class AuthModule {}
