import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { useContainer } from 'class-validator';

export const mainDirectory = __dirname + '/..';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix('api');
  useContainer(app.select(AppModule), { fallbackOnErrors: true });

  const configService = app.get(ConfigService);
  app.enableCors({
    origin: configService.getOrThrow('CORS_ORIGINS').split('; '),
  });

  const config = new DocumentBuilder()
    .addBearerAuth()
    .setTitle(configService.getOrThrow('APP_NAME'))
    .setDescription(configService.getOrThrow('APP_NAME') + ' API')
    .setVersion('1.0')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      defaultModelsExpandDepth: -1,
    },
    customCssUrl: '/public/css/dark-swagger.css',
  });

  await app.listen(
    configService.getOrThrow('APP_PORT'),
    configService.getOrThrow('APP_HOST'),
  );
}

bootstrap();
