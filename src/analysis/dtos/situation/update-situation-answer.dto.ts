import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SituationAnswerTypeEnum } from '../../enums/situation-answer-type.enum';

class AnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  questionId: number;

  @ApiProperty({ type: Number, enum: SituationAnswerTypeEnum, required: false })
  @IsEnum(SituationAnswerTypeEnum)
  @IsOptional()
  answerType?: SituationAnswerTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  description?: string;
}

export class UpdateSituationAnswerDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({
    type: AnswerDto,
    isArray: true,
  })
  @ValidateNested({ each: true })
  @Type(() => AnswerDto)
  @IsArray()
  answers: AnswerDto[];
}
