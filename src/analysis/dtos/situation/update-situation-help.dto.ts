import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { SituationAnswerTypeEnum } from '../../enums/situation-answer-type.enum';

export class UpdateSituationHelpDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  helpId: number;

  @ApiProperty({ type: Number, enum: SituationAnswerTypeEnum })
  @IsEnum(SituationAnswerTypeEnum)
  answerType: SituationAnswerTypeEnum;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  description?: string;
}
