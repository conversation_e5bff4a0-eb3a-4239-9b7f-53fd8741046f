import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsString, MinLength } from 'class-validator';

export class UpdateCapitalDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  capitalId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  input?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  output?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  value?: string;
}
