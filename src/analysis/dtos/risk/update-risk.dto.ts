import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class UpdateRiskDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  riskId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  riskText?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @IsOptional()
  opportunity?: string;
}
