import { ApiProperty } from '@nestjs/swagger';
import {
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Max,
  Min,
  MinLength,
} from 'class-validator';

export class UpdateTrendDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  trendId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  source?: string;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  termId?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @IsOptional()
  categoryId?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  severity?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  possibility?: number;
}
