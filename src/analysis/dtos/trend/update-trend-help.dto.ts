import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class UpdateTrendHelpDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  helpId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  @Min(1)
  @Max(5)
  severity: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  @Min(1)
  @Max(5)
  possibility: number;
}
