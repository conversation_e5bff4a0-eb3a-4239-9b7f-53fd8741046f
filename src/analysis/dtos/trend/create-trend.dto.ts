import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>N<PERSON>ber,
  <PERSON>Optional,
  IsString,
  Max,
  Min,
  MinLength,
} from 'class-validator';

export class CreateTrendDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  source: string;

  @ApiProperty({ type: Number })
  @IsNumber()
  termId: number;

  @ApiProperty({ type: Number })
  @IsNumber()
  categoryId: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  severity?: number;

  @ApiProperty({ type: Number, required: false })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  possibility?: number;
}
