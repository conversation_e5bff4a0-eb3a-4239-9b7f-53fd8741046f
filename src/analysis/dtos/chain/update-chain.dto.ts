import { ApiProperty } from '@nestjs/swagger';
import {
  IsNumber,
  IsOptional,
  IsString,
  Min,
  MinLength,
} from 'class-validator';

export class UpdateChainDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  chainId: number;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  name?: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;

  @ApiProperty({ type: Number, required: false })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  @IsOptional()
  order?: number;
}
