import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Min,
  MinLength,
} from 'class-validator';
import { ChainTypeEnum } from '../../enums/chain-type.enum';

export class CreateChainDto {
  @ApiProperty({ type: Number })
  @IsNumber()
  reportId: number;

  @ApiProperty({ type: String })
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ type: String, required: false })
  @IsString()
  @MinLength(1)
  @IsOptional()
  description?: string;

  @ApiProperty({ type: Number, enum: ChainTypeEnum })
  @IsEnum(ChainTypeEnum)
  type: ChainTypeEnum;

  @ApiProperty({ type: Number })
  @IsNumber({ maxDecimalPlaces: 0 })
  @Min(1)
  order: number;
}
