import { UtilService } from '../../common/services/util.service';
import { UserDto } from '../../common/dtos/user.dto';
import { PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { AnalysisUtilService } from '../../common/services/analysis.util.service';
import { SocketIoGateway } from '../../common/gateways/socket-io.gateway';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AnalysisGateway {
  constructor(
    private socketIoGateway: SocketIoGateway,
    private utilService: UtilService,
    private analysisUtilService: AnalysisUtilService,
  ) {}

  async sendStages(
    user: UserDto,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ): Promise<void> {
    const stages = await this.analysisUtilService.getStages(user, {
      tx: options?.tx,
    });
    this.socketIoGateway.sendEmit(
      this.utilService.socketRoom(user.id, user.organizationId),
      'analysisStages',
      stages,
    );
  }
}
