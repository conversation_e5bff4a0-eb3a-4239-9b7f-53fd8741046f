import { UtilService } from '../../common/services/util.service';
import { UserDto } from '../../common/dtos/user.dto';
import { SocketIoGateway } from '../../common/gateways/socket-io.gateway';
import { Injectable } from '@nestjs/common';
import { PermissionUtilService } from '../../common/services/permission.util.service';

@Injectable()
export class ChainGateway {
  constructor(
    private socketIoGateway: SocketIoGateway,
    private utilService: UtilService,
    private permissionUtilService: PermissionUtilService,
  ) {}

  async sendReports(user: UserDto, reports: any[]): Promise<void> {
    const userIds = await this.permissionUtilService.whoCanSee({
      user,
      type: 'chain',
    });
    userIds.forEach((userId) => {
      this.socketIoGateway.sendEmit(
        this.utilService.socketRoom(userId, user.organizationId),
        'chain',
        {
          type: 'reports',
          data: reports,
        },
      );
    });
  }

  async sendChains(
    user: UserDto,
    reportId: number,
    chains: any,
  ): Promise<void> {
    const userIds = await this.permissionUtilService.whoCanSee({
      user,
      type: 'chain',
    });
    userIds.forEach((userId) => {
      this.socketIoGateway.sendEmit(
        this.utilService.socketRoom(userId, user.organizationId),
        `chain-${reportId}`,
        {
          type: 'chains',
          data: chains,
        },
      );
    });
  }
}
