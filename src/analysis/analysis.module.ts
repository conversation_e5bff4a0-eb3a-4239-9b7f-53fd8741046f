import { Module } from '@nestjs/common';
import { SituationController } from './controllers/situation.controller';
import { SituationService } from './services/situation.service';
import { CommonModule } from '../common/common.module';
import { SituationValidation } from './validations/situation.validation';
import { TrendController } from './controllers/trend.controller';
import { TrendService } from './services/trend.service';
import { TrendValidation } from './validations/trend.validation';
import { RiskController } from './controllers/risk.controller';
import { RiskService } from './services/risk.service';
import { RiskValidation } from './validations/risk.validation';
import { ChainController } from './controllers/chain.controller';
import { ChainService } from './services/chain.service';
import { ChainValidation } from './validations/chain.validation';
import { CapitalService } from './services/capital.service';
import { CapitalValidation } from './validations/capital.validation';
import { CapitalController } from './controllers/capital.controller';
import { OrganizationModule } from '../organization/organization.module';
import { ActivityModule } from '../activity/activity.module';
import { AnalysisController } from './controllers/analysis.controller';
import { AnalysisService } from './services/analysis.service';
import { AnalysisGateway } from './gateways/analysis.gateway';
import { AnalysisValidation } from './validations/analysis.validation';
import { ChainGateway } from './gateways/chain.gateway';

@Module({
  imports: [ActivityModule, OrganizationModule, CommonModule],
  controllers: [
    AnalysisController,
    CapitalController,
    ChainController,
    RiskController,
    SituationController,
    TrendController,
  ],
  providers: [
    AnalysisService,
    AnalysisValidation,
    AnalysisGateway,
    CapitalService,
    CapitalValidation,
    ChainService,
    ChainValidation,
    ChainGateway,
    RiskService,
    RiskValidation,
    SituationService,
    SituationValidation,
    TrendService,
    TrendValidation,
  ],
  exports: [ChainService],
})
export class AnalysisModule {}
