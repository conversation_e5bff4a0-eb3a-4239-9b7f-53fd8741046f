import { BadRequestException, Injectable } from '@nestjs/common';
import { UserDto } from '../../common/dtos/user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetCurrentAnalysisDto } from '../dtos/analysis/get-current-analysis.dto';

@Injectable()
export class AnalysisValidation {
  constructor(private organizationService: OrganizationService) {}

  async getCurrents(user: UserDto, dto: GetCurrentAnalysisDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }
}
