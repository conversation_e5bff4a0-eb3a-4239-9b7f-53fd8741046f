import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteTrendReportDto } from '../dtos/trend/delete-trend-report.dto';
import { CreateTrendDto } from '../dtos/trend/create-trend.dto';
import { UpdateTrendDto } from '../dtos/trend/update-trend.dto';
import { DeleteTrendDto } from '../dtos/trend/delete-trend.dto';
import { Prisma, TrendHelp } from '@prisma/client';
import { UpdateTrendReportDto } from '../dtos/trend/update-trend-report.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { CreateTrendHelpDto } from '../dtos/trend/create-trend-help.dto';
import { UpdateTrendHelpDto } from '../dtos/trend/update-trend-help.dto';
import { DeleteTrendHelpDto } from '../dtos/trend/delete-trend-help.dto';

@Injectable()
export class TrendValidation {
  constructor(private prismaService: PrismaService) {}

  private reportCompleteCheck(
    report: Prisma.TrendReportGetPayload<{ include: { riskReports: true } }>,
  ): void {
    if (report.riskReports.length > 0) {
      throw new BadRequestException(
        'Trend raporu tamamlandığı için bu raporda işlem yapılamaz.',
      );
    }
  }

  async createTrend(user: UserDto, dto: CreateTrendDto): Promise<void> {
    const report = await this.prismaService.trendReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { riskReports: true },
    });
    this.reportCompleteCheck(report);
  }

  async createTrendHelp(user: UserDto, dto: CreateTrendHelpDto): Promise<void> {
    if (user.id == dto.helpedUserId) {
      throw new BadRequestException('Kendinizi ekleyemezsiniz.');
    }
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: { userId: dto.helpedUserId, organizationId: user.organizationId },
    });
    const trend = await this.prismaService.trend.findFirstOrThrow({
      where: {
        id: dto.trendId,
        report: { organizationId: user.organizationId },
      },
      include: { report: { include: { riskReports: true } } },
    });
    this.reportCompleteCheck(trend.report);
    const help = await this.prismaService.trendHelp.findFirst({
      where: {
        trendId: dto.trendId,
        status: HelpStatusEnum.PENDING,
      },
    });
    if (help) {
      throw new BadRequestException(`Şu an aktif bir atamanız var.`);
    }
  }

  async updateTrend(user: UserDto, dto: UpdateTrendDto): Promise<void> {
    const trend = await this.prismaService.trend.findFirstOrThrow({
      where: {
        id: dto.trendId,
        report: { organizationId: user.organizationId },
      },
      include: { report: { include: { riskReports: true } } },
    });
    this.reportCompleteCheck(trend.report);
  }

  async updateReport(user: UserDto, dto: UpdateTrendReportDto): Promise<void> {
    const report = await this.prismaService.trendReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { riskReports: true },
    });
    this.reportCompleteCheck(report);
  }

  async updateTrendHelp(
    user: UserDto,
    dto: UpdateTrendHelpDto,
  ): Promise<TrendHelp> {
    const help = await this.prismaService.trendHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        trend: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: {
        trend: { include: { report: { include: { riskReports: true } } } },
      },
    });
    this.reportCompleteCheck(help.trend.report);
    return help;
  }

  async deleteTrend(user: UserDto, dto: DeleteTrendDto): Promise<void> {
    const trend = await this.prismaService.trend.findFirstOrThrow({
      where: {
        id: dto.trendId,
        report: { organizationId: user.organizationId },
      },
      include: { report: { include: { riskReports: true } } },
    });
    this.reportCompleteCheck(trend.report);
  }

  async deleteReport(user: UserDto, dto: DeleteTrendReportDto): Promise<void> {
    const report = await this.prismaService.trendReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { riskReports: true },
    });
    this.reportCompleteCheck(report);
  }

  async deleteTrendHelp(user: UserDto, dto: DeleteTrendHelpDto): Promise<void> {
    const help = await this.prismaService.trendHelp.findFirst({
      where: {
        id: dto.helpId,
        trend: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: {
        trend: { include: { report: { include: { riskReports: true } } } },
      },
    });
    if (!help) {
      throw new BadRequestException('Bu atamayı silemezsiniz.');
    }
    this.reportCompleteCheck(help.trend.report);
  }
}
