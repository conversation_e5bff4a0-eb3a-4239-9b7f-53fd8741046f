import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { UpdateSituationAnswerDto } from '../dtos/situation/update-situation-answer.dto';
import { CreateSituationHelpDto } from '../dtos/situation/create-situation-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { UpdateSituationHelpDto } from '../dtos/situation/update-situation-help.dto';
import { DeleteSituationReportDto } from '../dtos/situation/delete-situation-report.dto';
import { GetSituationSuggestionDto } from '../dtos/situation/get-situation-suggestion.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetSituationReportDto } from '../dtos/situation/get-situation-report.dto';
import { GetSituationReportUserDto } from '../dtos/situation/get-situation-report-user.dto';
import { SituationHelp } from '@prisma/client';
import { DeleteSituationHelpDto } from '../dtos/situation/delete-situation-help.dto';
import { UpdateSituationReportDto } from '../dtos/situation/update-situation-report.dto';

@Injectable()
export class SituationValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
  ) {}

  async getSuggestions(
    user: UserDto,
    dto: GetSituationSuggestionDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetSituationReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetSituationReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async createSituationHelp(
    user: UserDto,
    dto: CreateSituationHelpDto,
  ): Promise<void> {
    if (user.id == dto.helpedUserId) {
      throw new BadRequestException('Kendinizi ekleyemezsiniz.');
    }
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: { userId: dto.helpedUserId, organizationId: user.organizationId },
    });
    const report = await this.prismaService.situationReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    if (report.isCompleted) {
      throw new BadRequestException(
        'Mevcut durum analizi tamamlandığı için işlem yapılamaz.',
      );
    }
    await this.prismaService.situationQuestion.findFirstOrThrow({
      where: { id: dto.questionId },
    });
    const help = await this.prismaService.situationHelp.findFirst({
      where: {
        reportId: dto.reportId,
        questionId: dto.questionId,
        status: HelpStatusEnum.PENDING,
      },
    });
    if (help) {
      throw new BadRequestException(`Şu an aktif bir atamanız var.`);
    }
  }

  async updateAnswer(
    user: UserDto,
    dto: UpdateSituationAnswerDto,
  ): Promise<void> {
    const report = await this.prismaService.situationReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    if (report.isCompleted) {
      throw new BadRequestException(
        'Mevcut durum analizi tamamlandığı için işlem yapılamaz.',
      );
    }
    const questionIds = dto.answers.map((a) => a.questionId);
    const questionCount = await this.prismaService.situationQuestion.count({
      where: { id: { in: questionIds } },
    });
    if (questionCount !== questionIds.length) {
      throw new BadRequestException('Geçersiz sorular.');
    }
  }

  async updateReport(
    user: UserDto,
    dto: UpdateSituationReportDto,
  ): Promise<void> {
    const report = await this.prismaService.situationReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
      include: {
        _count: { select: { answers: { where: { deletedAt: null } } } },
      },
    });
    if (report.isCompleted) {
      throw new BadRequestException(
        'Mevcut durum analizi tamamlandığı için işlem yapılamaz.',
      );
    }
    if (dto.isCompleted) {
      const questionCount = await this.prismaService.situationQuestion.count();
      if (report._count.answers != questionCount) {
        throw new BadRequestException(
          'Mevcut durum analizinin tamamlanabilmesi için tüm sorular cevaplanmalıdır.',
        );
      }
    }
  }

  async updateSituationHelp(
    user: UserDto,
    dto: UpdateSituationHelpDto,
  ): Promise<SituationHelp> {
    const help = await this.prismaService.situationHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        report: { organizationId: user.organizationId },
        status: HelpStatusEnum.PENDING,
      },
      include: { report: true },
    });
    if (help.report.isCompleted) {
      throw new BadRequestException(
        'Mevcut durum analizi tamamlandığı için işlem yapılamaz.',
      );
    }
    return help;
  }

  async deleteReport(
    user: UserDto,
    dto: DeleteSituationReportDto,
  ): Promise<void> {
    await this.prismaService.situationReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteSituationHelp(
    user: UserDto,
    dto: DeleteSituationHelpDto,
  ): Promise<void> {
    const help = await this.prismaService.situationHelp.findFirst({
      where: {
        id: dto.helpId,
        report: { organizationId: user.organizationId },
        status: HelpStatusEnum.PENDING,
      },
      include: { report: true },
    });
    if (!help) {
      throw new BadRequestException('Bu atamayı silemezsiniz.');
    }
    if (help.report.isCompleted) {
      throw new BadRequestException(
        'Mevcut durum analizi tamamlandığı için işlem yapılamaz.',
      );
    }
  }
}
