import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteChainReportDto } from '../dtos/chain/delete-chain-report.dto';
import { CreateChainDto } from '../dtos/chain/create-chain.dto';
import { UpdateChainDto } from '../dtos/chain/update-chain.dto';
import { DeleteChainDto } from '../dtos/chain/delete-chain.dto';
import { UpdateChainReportDto } from '../dtos/chain/update-chain-report.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { Chain, ChainHelp } from '@prisma/client';
import { CreateChainHelpDto } from '../dtos/chain/create-chain-help.dto';
import { UpdateChainHelpDto } from '../dtos/chain/update-chain-help.dto';
import { DeleteChainHelpDto } from '../dtos/chain/delete-chain-help.dto';
import { GetCurrentChainDto } from '../dtos/chain/get-current-chain.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { GetChainReportDto } from '../dtos/chain/get-chain-report.dto';
import { GetChainReportUserDto } from '../dtos/chain/get-chain-report-user.dto';

@Injectable()
export class ChainValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
  ) {}

  async getCurrentChains(
    user: UserDto,
    dto: GetCurrentChainDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetChainReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetChainReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async createChain(user: UserDto, dto: CreateChainDto): Promise<void> {
    const report = await this.prismaService.chainReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    if (report.isCompleted) {
      throw new BadRequestException(
        'Değer zinciri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    const maxChain = await this.prismaService.chain.aggregate({
      _max: { order: true },
      where: { reportId: report.id, type: dto.type },
    });

    if (maxChain._max.order && dto.order > maxChain._max.order + 1) {
      throw new BadRequestException('Geçersiz sıra numarası.');
    }
    if (!maxChain._max.order) {
      dto.order = 1;
    }
  }

  async createChainHelp(user: UserDto, dto: CreateChainHelpDto): Promise<void> {
    if (user.id == dto.helpedUserId) {
      throw new BadRequestException('Kendinizi ekleyemezsiniz.');
    }
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: { userId: dto.helpedUserId, organizationId: user.organizationId },
    });
    const chain = await this.prismaService.chain.findFirstOrThrow({
      where: {
        id: dto.chainId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (chain.report.isCompleted) {
      throw new BadRequestException(
        'Değer zinciri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    const help = await this.prismaService.chainHelp.findFirst({
      where: {
        helpedUserId: dto.helpedUserId,
        chainId: dto.chainId,
        status: HelpStatusEnum.PENDING,
      },
    });
    if (help) {
      throw new BadRequestException(
        `Bu kullanıcıya zaten şu an aktif bir atama var.`,
      );
    }
  }

  async updateChain(user: UserDto, dto: UpdateChainDto): Promise<Chain> {
    const chain = await this.prismaService.chain.findFirstOrThrow({
      where: {
        id: dto.chainId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (chain.report.isCompleted) {
      throw new BadRequestException(
        'Değer zinciri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    if (dto.order) {
      const maxChain = await this.prismaService.chain.aggregate({
        _max: { order: true },
        where: { reportId: chain.reportId, type: chain.type },
      });
      if (maxChain._max.order && dto.order > maxChain._max.order) {
        throw new BadRequestException('Geçersiz sıra numarası.');
      }
      if (!maxChain._max.order) {
        dto.order = 1;
      }
    }
    return chain;
  }

  async updateReport(user: UserDto, dto: UpdateChainReportDto): Promise<void> {
    const report = await this.prismaService.chainReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { chains: true },
    });
    if (report.isCompleted) {
      throw new BadRequestException(
        'Değer zinciri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    if (dto.isCompleted) {
      const uncompletedChains = report.chains.filter((c) => !c.description);
      if (uncompletedChains.length > 0) {
        throw new BadRequestException(
          'Değer zinciri raporunun tamamlanabilmesi için tüm eksik veriler girilmelidir.',
        );
      }
    }
  }

  async updateChainHelp(
    user: UserDto,
    dto: UpdateChainHelpDto,
  ): Promise<ChainHelp> {
    const help = await this.prismaService.chainHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        chain: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: { chain: { include: { report: true } } },
    });
    if (help.chain.report.isCompleted) {
      throw new BadRequestException(
        'Değer zinciri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    return help;
  }

  async deleteChain(user: UserDto, dto: DeleteChainDto): Promise<void> {
    const chain = await this.prismaService.chain.findFirstOrThrow({
      where: {
        id: dto.chainId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (chain.report.isCompleted) {
      throw new BadRequestException(
        'Değer zinciri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async deleteReport(user: UserDto, dto: DeleteChainReportDto): Promise<void> {
    const report = await this.prismaService.chainReport.findFirstOrThrow({
      select: {
        _count: { select: { financialReports: true, impactReports: true } },
      },
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    if (report._count.financialReports > 0 || report._count.impactReports > 0) {
      throw new BadRequestException(
        'Bu rapor, Önceliklendirme Analizinde kullanıldığı için silinemez.',
      );
    }
  }

  async deleteChainHelp(user: UserDto, dto: DeleteChainHelpDto): Promise<void> {
    const help = await this.prismaService.chainHelp.findFirst({
      where: {
        id: dto.helpId,
        chain: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: { chain: { include: { report: true } } },
    });
    if (!help) {
      throw new BadRequestException('Bu atamayı silemezsiniz.');
    }
    if (help.chain.report.isCompleted) {
      throw new BadRequestException(
        'Değer zinciri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }
}
