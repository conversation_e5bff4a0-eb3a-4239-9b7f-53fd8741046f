import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteRiskReportDto } from '../dtos/risk/delete-risk-report.dto';
import { UpdateRiskDto } from '../dtos/risk/update-risk.dto';
import { CreateRiskReportDto } from '../dtos/risk/create-risk-report.dto';
import { RiskHelp, Trend } from '@prisma/client';
import { UpdateRiskReportDto } from '../dtos/risk/update-risk-report.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { CreateRiskHelpDto } from '../dtos/risk/create-risk-help.dto';
import { UpdateRiskHelpDto } from '../dtos/risk/update-risk-help.dto';
import { DeleteRiskHelpDto } from '../dtos/risk/delete-risk-help.dto';

@Injectable()
export class RiskValidation {
  constructor(private prismaService: PrismaService) {}

  async createReport(
    user: UserDto,
    dto: CreateRiskReportDto,
  ): Promise<Trend[]> {
    const report = await this.prismaService.riskReport.findFirst({
      where: {
        organizationId: user.organizationId,
        trendReportId: dto.trendReportId,
      },
    });
    if (report) {
      throw new BadRequestException(
        'Trend analizi raporu zaten tamamlanmıştır.',
      );
    }
    const trendReport = await this.prismaService.trendReport.findFirstOrThrow({
      where: {
        id: dto.trendReportId,
        organizationId: user.organizationId,
      },
    });
    if (!trendReport.threshold) {
      throw new BadRequestException(
        'Trend analizi raporu için threshold belirlenmemiştir.',
      );
    }
    const trends = (
      await this.prismaService.trend.findMany({
        where: {
          reportId: dto.trendReportId,
          severity: { not: null },
          possibility: { not: null },
        },
      })
    ).filter((t) => t.severity * t.possibility >= trendReport.threshold);
    if (trends.length == 0) {
      throw new BadRequestException(
        'Risk raporu oluşturabilmek için eşik değerini aşan trendlerin olması gerekmektedir.',
      );
    }
    return trends;
  }

  async createRiskHelp(user: UserDto, dto: CreateRiskHelpDto): Promise<void> {
    if (user.id == dto.helpedUserId) {
      throw new BadRequestException('Kendinizi ekleyemezsiniz.');
    }
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: { userId: dto.helpedUserId, organizationId: user.organizationId },
    });
    const risk = await this.prismaService.risk.findFirstOrThrow({
      where: {
        id: dto.riskId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (risk.report.isCompleted) {
      throw new BadRequestException(
        'Risk raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    const help = await this.prismaService.riskHelp.findFirst({
      where: {
        riskId: dto.riskId,
        status: HelpStatusEnum.PENDING,
      },
    });
    if (help) {
      throw new BadRequestException(`Şu an aktif bir atamanız var.`);
    }
  }

  async updateRisk(user: UserDto, dto: UpdateRiskDto): Promise<void> {
    const risk = await this.prismaService.risk.findFirstOrThrow({
      where: {
        id: dto.riskId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (risk.report.isCompleted) {
      throw new BadRequestException(
        'Risk raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async updateReport(user: UserDto, dto: UpdateRiskReportDto): Promise<void> {
    const report = await this.prismaService.riskReport.findFirstOrThrow({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
      include: { risks: { include: { trend: true } } },
    });
    if (report.isCompleted) {
      throw new BadRequestException(
        'Risk raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    if (dto.isCompleted) {
      const uncompletedRisks = report.risks.filter(
        (r) => !r.riskText || !r.opportunity,
      );
      if (uncompletedRisks.length > 0) {
        throw new BadRequestException(
          'Risk raporunun tamamlanabilmesi için tüm risk ve fırsatlar girilmelidir.',
        );
      }
    }
  }

  async updateRiskHelp(
    user: UserDto,
    dto: UpdateRiskHelpDto,
  ): Promise<RiskHelp> {
    const help = await this.prismaService.riskHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        risk: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: { risk: { include: { report: true } } },
    });
    if (help.risk.report.isCompleted) {
      throw new BadRequestException(
        'Risk raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    return help;
  }

  async deleteReport(user: UserDto, dto: DeleteRiskReportDto): Promise<void> {
    await this.prismaService.riskReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteRiskHelp(user: UserDto, dto: DeleteRiskHelpDto): Promise<void> {
    const help = await this.prismaService.riskHelp.findFirst({
      where: {
        id: dto.helpId,
        risk: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: { risk: { include: { report: true } } },
    });
    if (!help) {
      throw new BadRequestException('Bu atamayı silemezsiniz.');
    }
    if (help.risk.report.isCompleted) {
      throw new BadRequestException(
        'Risk raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }
}
