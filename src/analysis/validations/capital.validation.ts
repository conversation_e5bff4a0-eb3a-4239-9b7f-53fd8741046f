import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { DeleteCapitalReportDto } from '../dtos/capital/delete-capital-report.dto';
import { UpdateCapitalDto } from '../dtos/capital/update-capital.dto';
import { DeleteCapitalDto } from '../dtos/capital/delete-capital.dto';
import { UpdateCapitalReportDto } from '../dtos/capital/update-capital-report.dto';
import { CreateCapitalHelpDto } from '../dtos/capital/create-capital-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { UpdateCapitalHelpDto } from '../dtos/capital/update-capital-help.dto';
import { CapitalHelp } from '@prisma/client';
import { DeleteCapitalHelpDto } from '../dtos/capital/delete-capital-help.dto';
import { GetCurrentCapitalDto } from '../dtos/capital/get-current-capital.dto';
import { GetCapitalReportDto } from '../dtos/capital/get-capital-report.dto';
import { GetCapitalReportUserDto } from '../dtos/capital/get-capital-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';

@Injectable()
export class CapitalValidation {
  constructor(
    private prismaService: PrismaService,
    private organizationService: OrganizationService,
  ) {}

  async getCurrentCapitals(
    user: UserDto,
    dto: GetCurrentCapitalDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReports(user: UserDto, dto: GetCapitalReportDto): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async getReportUsers(
    user: UserDto,
    dto: GetCapitalReportUserDto,
  ): Promise<void> {
    if (dto.subsidiaryId) {
      const organizationIds =
        await this.organizationService.getOrganizationIds(user);
      if (!organizationIds.includes(dto.subsidiaryId)) {
        throw new BadRequestException('Geçersiz şirket filtresi.');
      }
    }
  }

  async createCapitalHelp(
    user: UserDto,
    dto: CreateCapitalHelpDto,
  ): Promise<void> {
    if (user.id == dto.helpedUserId) {
      throw new BadRequestException('Kendinizi ekleyemezsiniz.');
    }
    await this.prismaService.userOrganization.findFirstOrThrow({
      where: { userId: dto.helpedUserId, organizationId: user.organizationId },
    });
    const capital = await this.prismaService.capital.findFirstOrThrow({
      where: {
        id: dto.capitalId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (capital.report.isCompleted) {
      throw new BadRequestException(
        'Sermaye ögeleri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    const help = await this.prismaService.capitalHelp.findFirst({
      where: {
        helpedUserId: dto.helpedUserId,
        capitalId: dto.capitalId,
        status: HelpStatusEnum.PENDING,
      },
    });
    if (help) {
      throw new BadRequestException(
        `Bu kullanıcıya zaten şu an aktif bir atama var.`,
      );
    }
  }

  async updateCapital(user: UserDto, dto: UpdateCapitalDto): Promise<void> {
    const capital = await this.prismaService.capital.findFirstOrThrow({
      where: {
        id: dto.capitalId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (capital.report.isCompleted) {
      throw new BadRequestException(
        'Sermaye ögeleri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async updateReport(
    user: UserDto,
    dto: UpdateCapitalReportDto,
  ): Promise<void> {
    const report = await this.prismaService.capitalReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
      include: { capitals: true },
    });
    if (report.isCompleted) {
      throw new BadRequestException(
        'Sermaye ögeleri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    if (dto.isCompleted) {
      const uncompletedCapitals = report.capitals.filter(
        (c) => !c.input || !c.output || !c.value,
      );
      if (uncompletedCapitals.length > 0) {
        throw new BadRequestException(
          'Sermaye ögeleri raporunun tamamlanabilmesi için tüm eksik veriler girilmelidir.',
        );
      }
    }
  }

  async updateCapitalHelp(
    user: UserDto,
    dto: UpdateCapitalHelpDto,
  ): Promise<CapitalHelp> {
    const help = await this.prismaService.capitalHelp.findFirstOrThrow({
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        capital: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: { capital: { include: { report: true } } },
    });
    if (help.capital.report.isCompleted) {
      throw new BadRequestException(
        'Sermaye ögeleri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
    return help;
  }

  async deleteCapital(user: UserDto, dto: DeleteCapitalDto): Promise<void> {
    const capital = await this.prismaService.capital.findFirstOrThrow({
      where: {
        id: dto.capitalId,
        report: { organizationId: user.organizationId },
      },
      include: { report: true },
    });
    if (capital.report.isCompleted) {
      throw new BadRequestException(
        'Sermaye ögeleri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }

  async deleteReport(
    user: UserDto,
    dto: DeleteCapitalReportDto,
  ): Promise<void> {
    await this.prismaService.capitalReport.findFirstOrThrow({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteCapitalHelp(
    user: UserDto,
    dto: DeleteCapitalHelpDto,
  ): Promise<void> {
    const help = await this.prismaService.capitalHelp.findFirst({
      where: {
        id: dto.helpId,
        capital: { report: { organizationId: user.organizationId } },
        status: HelpStatusEnum.PENDING,
      },
      include: { capital: { include: { report: true } } },
    });
    if (!help) {
      throw new BadRequestException('Bu atamayı silemezsiniz.');
    }
    if (help.capital.report.isCompleted) {
      throw new BadRequestException(
        'Sermaye ögeleri raporu tamamlandığı için işlem yapılamaz.',
      );
    }
  }
}
