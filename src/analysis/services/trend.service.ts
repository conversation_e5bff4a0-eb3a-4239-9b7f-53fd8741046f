import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetTrendDto } from '../dtos/trend/get-trend.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteTrendReportDto } from '../dtos/trend/delete-trend-report.dto';
import { PrismaClient, TrendHelp } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { CreateTrendDto } from '../dtos/trend/create-trend.dto';
import { UpdateTrendDto } from '../dtos/trend/update-trend.dto';
import { DeleteTrendDto } from '../dtos/trend/delete-trend.dto';
import { UpdateTrendReportDto } from '../dtos/trend/update-trend-report.dto';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateTrendHelpDto } from '../dtos/trend/create-trend-help.dto';
import { ActivityService } from '../../activity/activity.service';
import { UpdateTrendHelpDto } from '../dtos/trend/update-trend-help.dto';
import { GetTrendHelpDto } from '../dtos/trend/get-trend-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { DeleteTrendHelpDto } from '../dtos/trend/delete-trend-help.dto';
import { OrganizationUtilService } from '../../common/services/organization.util.service';

@Injectable()
export class TrendService {
  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getTrends(
    user: UserDto,
    dto: GetTrendDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      threshold?: number;
    },
  ) {
    const dbService = options?.tx ?? this.prismaService;
    const report = await dbService.trendReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        threshold: true,
        createdAt: true,
        updatedAt: true,
        riskReports: { select: { id: true, name: true, createdAt: true } },
      },
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    let trends = await dbService.trend.findMany({
      select: {
        id: true,
        name: true,
        source: true,
        term: { select: { id: true, name: true } },
        category: { select: { id: true, name: true } },
        severity: true,
        possibility: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          take: 1,
          select: {
            id: true,
            status: true,
            severity: true,
            possibility: true,
            createdAt: true,
            updatedAt: true,
            createdUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                email: true,
                userOrganizations: true,
              },
            },
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                image: true,
                email: true,
                userOrganizations: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: user.organizationId },
      },
    });
    if (options?.threshold) {
      trends = trends.filter(
        (t) =>
          t.severity &&
          t.possibility &&
          t.severity * t.possibility >= options?.threshold,
      );
    }
    for (const t of trends) {
      t['createdUserInfo'] = plainToInstance(UserDataDto, t.createdUser, {
        excludeExtraneousValues: true,
      });
      t['help'] = t.helps.length > 0 ? t.helps[0] : undefined;
      if (t['help']) {
        t['help']['createdUserInfo'] = plainToInstance(
          UserDataDto,
          t['help'].createdUser,
          {
            excludeExtraneousValues: true,
          },
        );
        t['help']['helpedUserInfo'] = plainToInstance(
          UserDataDto,
          t['help'].helpedUser,
          {
            excludeExtraneousValues: true,
          },
        );
        delete t['help'].createdUser;
        delete t['help'].helpedUser;
      }
      delete t.createdUser;
      delete t.helps;
    }
    if (report.riskReports.length > 0) {
      report['riskReport'] = report.riskReports[0];
    }
    delete report.riskReports;
    return { report: report, trends: trends };
  }

  async getTrendRelations() {
    return {
      terms: await this.prismaService.trendTerm.findMany({
        select: { id: true, name: true },
      }),
      categories: await this.prismaService.trendCategory.findMany({
        select: { id: true, name: true },
      }),
    };
  }

  async getCurrentTrends(user: UserDto) {
    const lastFinishedReport = await this.getLastReport(user.organizationId, {
      isCompleted: true,
    });
    if (!lastFinishedReport) {
      return { report: null, trends: [] };
    }
    const { trends } = await this.getTrends(
      user,
      { reportId: lastFinishedReport.id },
      { threshold: lastFinishedReport.threshold },
    );
    return {
      report: lastFinishedReport,
      trends: trends,
    };
  }

  async getReports(user: UserDto) {
    const reports = await this.prismaService.trendReport.findMany({
      select: {
        id: true,
        name: true,
        threshold: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        riskReports: { select: { id: true, name: true, createdAt: true } },
      },
      where: { organizationId: user.organizationId },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      r['riskReport'] = r.riskReports.length > 0 ? r.riskReports[0] : undefined;
      delete r.createdUser;
      delete r.riskReports;
    }
    return reports;
  }

  async getReportUsers(user: UserDto) {
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: user.organizationId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: user.organizationId } },
        OR: [
          {
            createdTrendReports: {
              some: { organizationId: user.organizationId },
            },
          },
          {
            helpedTrendHelps: {
              some: {
                trend: { report: { organizationId: user.organizationId } },
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getLastReport(
    organizationId: number,
    options?: { isCompleted?: boolean; hasThreshold?: boolean },
  ) {
    const report = await this.prismaService.trendReport.findFirst({
      select: {
        id: true,
        name: true,
        threshold: true,
        createdAt: true,
        updatedAt: true,
        riskReports: { select: { id: true, name: true, createdAt: true } },
      },
      where: {
        organizationId: organizationId,
        riskReports:
          options?.isCompleted === undefined
            ? undefined
            : options?.isCompleted
              ? { some: {} }
              : { none: {} },
        threshold:
          options?.hasThreshold === undefined
            ? undefined
            : options?.hasThreshold
              ? { not: null }
              : null,
      },
      orderBy: { createdAt: 'desc' },
    });
    if (report) {
      if (report.riskReports.length > 0) {
        report['riskReport'] = report.riskReports[0];
      }
      delete report.riskReports;
    }
    return report;
  }

  async getTrendHelps(user: UserDto, dto: GetTrendHelpDto) {
    const help = await this.prismaService.trendHelp.findFirstOrThrow({
      select: {
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        trend: {
          select: {
            id: true,
            name: true,
            source: true,
            term: { select: { id: true, name: true } },
            category: { select: { id: true, name: true } },
            severity: true,
            possibility: true,
            report: {
              select: {
                id: true,
                name: true,
                organization: { select: { id: true, name: true } },
              },
            },
          },
        },
        status: true,
        severity: true,
        possibility: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
      },
      where: { id: dto.helpId, helpedUserId: user.id },
    });
    help['createdUserInfo'] = plainToInstance(UserDataDto, help.createdUser, {
      excludeExtraneousValues: true,
    });
    delete help.createdUser;
    return help;
  }

  async createTrend(user: UserDto, dto: CreateTrendDto) {
    await this.prismaService.trend.create({
      data: {
        createdUserId: user.id,
        reportId: dto.reportId,
        name: dto.name,
        source: dto.source,
        termId: dto.termId,
        categoryId: dto.categoryId,
        severity: dto.severity,
        possibility: dto.possibility,
      },
    });
  }

  async createReport(user: UserDto): Promise<{ report: any; trends: any[] }> {
    const reportCount = await this.prismaService.trendReport.count({
      where: { organizationId: user.organizationId },
    });
    const defaults = await this.prismaService.trendDefault.findMany();
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const report = await tx.trendReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          name: `TA_${reportCount + 1}_${dateFormat}`,
        },
        select: {
          id: true,
          name: true,
          threshold: true,
          createdAt: true,
          updatedAt: true,
        },
      });
      await tx.trend.createMany({
        data: defaults.map((d) => {
          return {
            createdUserId: user.id,
            reportId: report.id,
            name: d.name,
            source: d.source,
            termId: d.termId,
            categoryId: d.categoryId,
          };
        }),
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.TREND_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: report.id },
        },
      ]);
      const { trends } = await this.getTrends(
        user,
        { reportId: report.id },
        { tx: tx },
      );
      return {
        report: report,
        trends: trends,
      };
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: any; trends: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      isCompleted: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        isCompleted: false,
      });
      if (!lastReport) {
        return await this.createReport(user);
      }
    }
    const { trends } = await this.getTrends(user, {
      reportId: lastReport.id,
    });
    return {
      report: lastReport,
      trends: trends,
    };
  }

  async createTrendHelp(user: UserDto, dto: CreateTrendHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      const help = await tx.trendHelp.create({
        data: {
          createdUserId: user.id,
          helpedUserId: dto.helpedUserId,
          trendId: dto.trendId,
        },
      });
      await this.updateTrend(
        user,
        { trendId: dto.trendId, severity: null, possibility: null },
        { tx: tx },
      );
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.TREND_HELP,
          effectedUserIds: [dto.helpedUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async updateTrend(
    user: UserDto,
    dto: UpdateTrendDto,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ) {
    const dbService = options?.tx ?? this.prismaService;
    await dbService.trend.update({
      where: {
        id: dto.trendId,
        report: { organizationId: user.organizationId },
      },
      data: {
        name: dto.name,
        source: dto.source,
        termId: dto.termId,
        categoryId: dto.categoryId,
        severity: dto.severity,
        possibility: dto.possibility,
      },
    });
  }

  async updateReport(user: UserDto, dto: UpdateTrendReportDto) {
    await this.prismaService.trendReport.update({
      where: {
        id: dto.reportId,
        organizationId: user.organizationId,
      },
      data: { threshold: dto.threshold },
    });
  }

  async updateTrendHelp(
    user: UserDto,
    dto: UpdateTrendHelpDto,
    help: TrendHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await this.updateTrend(
        user,
        {
          trendId: help.trendId,
          severity: dto.severity,
          possibility: dto.possibility,
        },
        { tx: tx },
      );
      await tx.trendHelp.update({
        where: { id: help.id },
        data: {
          status: HelpStatusEnum.APPROVED,
          severity: dto.severity,
          possibility: dto.possibility,
        },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.TREND_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async deleteTrend(user: UserDto, dto: DeleteTrendDto) {
    await this.prismaService.trend.delete({
      where: {
        id: dto.trendId,
        report: { organizationId: user.organizationId },
      },
    });
  }

  async deleteReport(user: UserDto, dto: DeleteTrendReportDto) {
    await this.prismaService.trendReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteTrendHelp(user: UserDto, dto: DeleteTrendHelpDto) {
    await this.prismaService.trendHelp.delete({
      where: { id: dto.helpId },
    });
  }
}
