import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetCapitalDto } from '../dtos/capital/get-capital.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteCapitalReportDto } from '../dtos/capital/delete-capital-report.dto';
import { CapitalReport, CapitalHelp, PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateCapitalDto } from '../dtos/capital/update-capital.dto';
import { DeleteCapitalDto } from '../dtos/capital/delete-capital.dto';
import { UpdateCapitalReportDto } from '../dtos/capital/update-capital-report.dto';
import { GetCapitalHelpDto } from '../dtos/capital/get-capital-help.dto';
import { CreateCapitalHelpDto } from '../dtos/capital/create-capital-help.dto';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { UpdateCapitalHelpDto } from '../dtos/capital/update-capital-help.dto';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { DeleteCapitalHelpDto } from '../dtos/capital/delete-capital-help.dto';
import { ActivityService } from '../../activity/activity.service';
import { GetCurrentCapitalDto } from '../dtos/capital/get-current-capital.dto';
import { GetCapitalReportDto } from '../dtos/capital/get-capital-report.dto';
import { GetCapitalReportUserDto } from '../dtos/capital/get-capital-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';

@Injectable()
export class CapitalService {
  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
  ) {}

  async getCapitals(
    user: UserDto,
    dto: GetCapitalDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ) {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.capitalReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        isCompleted: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const capitals = await dbService.capital.findMany({
      select: {
        id: true,
        name: true,
        input: true,
        output: true,
        value: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        updatedUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          take: 1,
          select: {
            id: true,
            status: true,
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
      },
    });
    for (const c of capitals) {
      const createdUser = plainToInstance(UserDataDto, c.createdUser, {
        excludeExtraneousValues: true,
      });
      const updatedUser = plainToInstance(UserDataDto, c.updatedUser, {
        excludeExtraneousValues: true,
      });
      let helpedUser: UserDataDto;
      if (c.helps.length > 0) {
        helpedUser = plainToInstance(UserDataDto, c.helps[0].helpedUser, {
          excludeExtraneousValues: true,
        });
        helpedUser['status'] = c.helps[0].status;
      }
      c['responsibleUserInfo'] = createdUser;
      if (helpedUser) {
        c['responsibleUserInfo'] = helpedUser;
      } else if (updatedUser) {
        c['responsibleUserInfo'] = updatedUser;
      }

      if (c['responsibleUserInfo']) {
        c['responsibleUserInfo']['isMe'] =
          c['responsibleUserInfo'].id == user.id;
      }
      delete c.createdUser;
      delete c.updatedUser;
      delete c.helps;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    delete report.createdUser;
    return { report: report, capitals: capitals };
  }

  async getCurrentCapitals(user: UserDto, dto: GetCurrentCapitalDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let report: CapitalReport;
    if (dto.reportId) {
      report = await this.prismaService.capitalReport.findFirstOrThrow({
        where: {
          id: dto.reportId,
          organizationId: dto.subsidiaryId,
          isCompleted: true,
        },
      });
    } else {
      report = await this.getLastReport(dto.subsidiaryId, {
        isCompleted: true,
      });
      if (!report) {
        return { report: null, capitals: [] };
      }
    }
    const { capitals } = await this.getCapitals(user, {
      reportId: report.id,
    });
    return {
      report: report,
      capitals: capitals,
    };
  }

  async getReports(user: UserDto, dto: GetCapitalReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.capitalReport.findMany({
      select: {
        id: true,
        name: true,
        isCompleted: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
      },
      where: {
        organizationId: dto.subsidiaryId,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      delete r.createdUser;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetCapitalReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        OR: [
          {
            createdCapitalReports: {
              some: { id: dto.reportId, organizationId: dto.subsidiaryId },
            },
          },
          {
            helpedCapitalHelps: {
              some: {
                capital: {
                  report: {
                    id: dto.reportId,
                    organizationId: dto.subsidiaryId,
                  },
                },
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  private async getLastReport(
    organizationId: number,
    options?: { isCompleted?: boolean },
  ): Promise<CapitalReport> {
    const report = await this.prismaService.capitalReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async getCapitalHelps(user: UserDto, dto: GetCapitalHelpDto) {
    const help = await this.prismaService.capitalHelp.findFirstOrThrow({
      select: {
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        capital: {
          select: {
            id: true,
            name: true,
            input: true,
            output: true,
            value: true,
            report: {
              select: {
                id: true,
                name: true,
                organization: { select: { id: true, name: true } },
              },
            },
          },
        },
        status: true,
        input: true,
        output: true,
        value: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
      },
      where: { id: dto.helpId, helpedUserId: user.id },
    });
    help['createdUserInfo'] = plainToInstance(UserDataDto, help.createdUser, {
      excludeExtraneousValues: true,
    });
    delete help.createdUser;
    return help;
  }

  async createReport(user: UserDto): Promise<{ report: any; capitals: any[] }> {
    const reportCount = await this.prismaService.capitalReport.count({
      where: { organizationId: user.organizationId },
    });
    const defaults = await this.prismaService.capitalDefault.findMany();
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const report = await tx.capitalReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          name: `SE_${reportCount + 1}_${dateFormat}`,
        },
      });
      await tx.capital.createMany({
        data: defaults.map((d) => {
          return {
            createdUserId: user.id,
            reportId: report.id,
            name: d.name,
            input: d.input,
            output: d.output,
            value: d.value,
          };
        }),
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.CAPITAL_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: report.id },
        },
      ]);
      const { capitals } = await this.getCapitals(
        user,
        { reportId: report.id },
        { tx: tx },
      );
      return {
        report: report,
        capitals: capitals,
      };
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: CapitalReport; capitals: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      isCompleted: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        isCompleted: false,
      });
      if (!lastReport) {
        return await this.createReport(user);
      }
    }
    const { capitals } = await this.getCapitals(user, {
      reportId: lastReport.id,
    });
    return {
      report: lastReport,
      capitals: capitals,
    };
  }

  async createCapitalHelp(user: UserDto, dto: CreateCapitalHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.capitalHelp.deleteMany({
        where: {
          capitalId: dto.capitalId,
          status: HelpStatusEnum.PENDING,
        },
      });
      const help = await tx.capitalHelp.create({
        data: {
          createdUserId: user.id,
          helpedUserId: dto.helpedUserId,
          capitalId: dto.capitalId,
        },
      });
      await this.updateCapital(
        user,
        { capitalId: dto.capitalId, input: null, output: null, value: null },
        { tx: tx, removeUpdatedUserId: true },
      );
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.CAPITAL_HELP,
          effectedUserIds: [dto.helpedUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async updateCapital(
    user: UserDto,
    dto: UpdateCapitalDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      removeUpdatedUserId?: boolean;
      removeHelps?: boolean;
    },
  ) {
    await this.prismaService.$transaction(async (tx) => {
      tx = options?.tx ?? tx;
      await tx.capital.update({
        where: {
          id: dto.capitalId,
          report: { organizationId: user.organizationId },
        },
        data: {
          updatedUserId: options?.removeUpdatedUserId ? null : user.id,
          name: dto.name,
          input: dto.input,
          output: dto.output,
          value: dto.value,
        },
      });
      if (options?.removeHelps) {
        await tx.capitalHelp.deleteMany({
          where: { capitalId: dto.capitalId },
        });
      }
    });
  }

  async updateReport(user: UserDto, dto: UpdateCapitalReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.capitalReport.update({
        where: { id: dto.reportId },
        data: { isCompleted: dto.isCompleted ? true : undefined },
      });
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.CAPITAL_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
    });
  }

  async updateCapitalHelp(
    user: UserDto,
    dto: UpdateCapitalHelpDto,
    help: CapitalHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await this.updateCapital(
        user,
        {
          capitalId: help.capitalId,
          input: dto.input,
          output: dto.output,
          value: dto.value,
        },
        { tx: tx, removeUpdatedUserId: true },
      );
      await tx.capitalHelp.update({
        where: { id: help.id },
        data: {
          status: HelpStatusEnum.APPROVED,
          input: dto.input,
          output: dto.output,
          value: dto.value,
        },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.CAPITAL_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async deleteCapital(user: UserDto, dto: DeleteCapitalDto) {
    await this.prismaService.capital.delete({
      where: {
        id: dto.capitalId,
        report: { organizationId: user.organizationId },
      },
    });
  }

  async deleteReport(user: UserDto, dto: DeleteCapitalReportDto) {
    await this.prismaService.capitalReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteCapitalHelp(user: UserDto, dto: DeleteCapitalHelpDto) {
    await this.prismaService.capitalHelp.delete({
      where: { id: dto.helpId },
    });
  }
}
