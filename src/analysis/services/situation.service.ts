import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { UpdateSituationAnswerDto } from '../dtos/situation/update-situation-answer.dto';
import { format } from 'date-fns';
import { GetSituationQuestionDto } from '../dtos/situation/get-situation-question.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { CreateSituationHelpDto } from '../dtos/situation/create-situation-help.dto';
import { UpdateSituationHelpDto } from '../dtos/situation/update-situation-help.dto';
import { DeleteSituationReportDto } from '../dtos/situation/delete-situation-report.dto';
import { GetSituationSuggestionDto } from '../dtos/situation/get-situation-suggestion.dto';
import { GetSituationReportDto } from '../dtos/situation/get-situation-report.dto';
import { GetSituationReportUserDto } from '../dtos/situation/get-situation-report-user.dto';
import { PrismaClient, SituationHelp, SituationReport } from '@prisma/client';
import { OrganizationService } from '../../organization/services/organization.service';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { ActivityService } from '../../activity/activity.service';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { GetSituationHelpDto } from '../dtos/situation/get-situation-help.dto';
import { AnalysisGateway } from '../gateways/analysis.gateway';
import { SituationUtilService } from '../../common/services/situation.util.service';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { DeleteSituationHelpDto } from '../dtos/situation/delete-situation-help.dto';
import { UpdateSituationReportDto } from '../dtos/situation/update-situation-report.dto';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { SituationAnswerTypeEnum } from '../enums/situation-answer-type.enum';

@Injectable()
export class SituationService {
  constructor(
    private prismaService: PrismaService,
    private analysisGateway: AnalysisGateway,
    private activityService: ActivityService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
    private situationUtilService: SituationUtilService,
  ) {}

  async getSuggestions(user: UserDto, dto: GetSituationSuggestionDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let report: SituationReport;
    if (dto.reportId) {
      report = await this.prismaService.situationReport.findFirstOrThrow({
        where: {
          id: dto.reportId,
          organizationId: dto.subsidiaryId,
          isCompleted: true,
        },
      });
    } else {
      report = await this.situationUtilService.getLastReport(dto.subsidiaryId, {
        isCompleted: true,
      });
      if (!report) {
        return [];
      }
    }
    const suggestions = await this.prismaService.situationQuestion.findMany({
      select: {
        id: true,
        suggestion: true,
        category: { select: { id: true, name: true } },
      },
      where: {
        reportAnswers: {
          some: {
            report: {
              id: report.id,
              organizationId: dto.subsidiaryId,
            },
            answerType: SituationAnswerTypeEnum.NO,
          },
        },
      },
    });
    return suggestions;
  }

  async getQuestions(
    user: UserDto,
    dto: GetSituationQuestionDto,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ) {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.situationReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        isCompleted: true,
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const questionCategories =
      await dbService.situationQuestionCategory.findMany({
        select: {
          id: true,
          name: true,
          situationQuestions: {
            select: {
              id: true,
              type: { select: { id: true, name: true } },
              name: true,
              info: true,
              reportAnswers: {
                where: {
                  report: {
                    id: dto.reportId,
                    organizationId: { in: organizationIds },
                  },
                },
              },
              helps: {
                take: 1,
                select: {
                  id: true,
                  status: true,
                  answerType: true,
                  createdAt: true,
                  updatedAt: true,
                  createdUser: {
                    select: {
                      id: true,
                      name: true,
                      surname: true,
                      email: true,
                      image: true,
                      userOrganizations: true,
                    },
                  },
                  helpedUser: {
                    select: {
                      id: true,
                      name: true,
                      surname: true,
                      email: true,
                      image: true,
                      userOrganizations: true,
                    },
                  },
                },
                where: {
                  report: {
                    id: dto.reportId,
                    organizationId: { in: organizationIds },
                  },
                },
                orderBy: { createdAt: 'desc' },
              },
            },
          },
        },
      });
    for (const qc of questionCategories) {
      for (const q of qc.situationQuestions) {
        q['answerType'] =
          q.reportAnswers.length > 0
            ? q.reportAnswers[0].answerType
            : undefined;
        q['description'] =
          q.reportAnswers.length > 0
            ? q.reportAnswers[0].description
            : undefined;
        q['help'] = q.helps.length > 0 ? q.helps[0] : undefined;
        if (q['help']) {
          q['help']['createdUserInfo'] = plainToInstance(
            UserDataDto,
            q['help'].createdUser,
            {
              excludeExtraneousValues: true,
            },
          );
          q['help']['helpedUserInfo'] = plainToInstance(
            UserDataDto,
            q['help'].helpedUser,
            {
              excludeExtraneousValues: true,
            },
          );
          delete q['help'].createdUser;
          delete q['help'].helpedUser;
        }
        delete q.reportAnswers;
        delete q.helps;
      }
    }
    return { report: report, questions: questionCategories };
  }

  async getReports(user: UserDto, dto: GetSituationReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.situationReport.findMany({
      select: {
        id: true,
        name: true,
        isCompleted: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        answers: true,
      },
      where: {
        organizationId: dto.subsidiaryId,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      if (r.isCompleted && r.answers.length > 0) {
        const rate =
          (r.answers.filter((a) => a.answerType == SituationAnswerTypeEnum.YES)
            .length /
            r.answers.length) *
          100;
        r['rate'] = Math.round(rate);
      }
      delete r.createdUser;
      delete r.answers;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetSituationReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        OR: [
          {
            createdSituationReports: {
              some: { id: dto.reportId, organizationId: dto.subsidiaryId },
            },
          },
          {
            helpedSituationHelps: {
              some: {
                report: { id: dto.reportId, organizationId: dto.subsidiaryId },
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getSituationHelps(user: UserDto, dto: GetSituationHelpDto) {
    const help = await this.prismaService.situationHelp.findFirstOrThrow({
      select: {
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        report: {
          select: {
            id: true,
            name: true,
            organization: { select: { id: true, name: true } },
          },
        },
        question: {
          select: {
            id: true,
            name: true,
            category: { select: { id: true, name: true } },
            type: { select: { id: true, name: true } },
          },
        },
        status: true,
        answerType: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
      },
      where: { id: dto.helpId, helpedUserId: user.id },
    });
    help['createdUserInfo'] = plainToInstance(UserDataDto, help.createdUser, {
      excludeExtraneousValues: true,
    });
    delete help.createdUser;
    return help;
  }

  async createReport(
    user: UserDto,
  ): Promise<{ report: SituationReport; questions: any[] }> {
    const reportCount = await this.prismaService.situationReport.count({
      where: { organizationId: user.organizationId },
    });
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const report = await this.prismaService.situationReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          name: `MDA_${reportCount + 1}_${dateFormat}`,
        },
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.SITUATION_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: report.id },
        },
      ]);
      const { questions } = await this.getQuestions(
        user,
        {
          reportId: report.id,
        },
        { tx: tx },
      );
      return {
        report: report,
        questions: questions,
      };
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: SituationReport; questions: any[] }> {
    let lastReport = await this.situationUtilService.getLastReport(
      user.organizationId,
      {
        isCompleted: true,
      },
    );
    if (!lastReport) {
      lastReport = await this.situationUtilService.getLastReport(
        user.organizationId,
        {
          isCompleted: false,
        },
      );
      if (!lastReport) {
        return await this.createReport(user);
      }
    }
    const { questions } = await this.getQuestions(user, {
      reportId: lastReport.id,
    });
    return {
      report: lastReport,
      questions: questions,
    };
  }

  async createSituationHelp(user: UserDto, dto: CreateSituationHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      const help = await tx.situationHelp.create({
        data: {
          createdUserId: user.id,
          helpedUserId: dto.helpedUserId,
          reportId: dto.reportId,
          questionId: dto.questionId,
        },
      });
      await this.updateAnswer(
        user,
        {
          reportId: dto.reportId,
          answers: [
            { questionId: dto.questionId, answerType: null, description: null },
          ],
        },
        { tx: tx },
      );
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.SITUATION_HELP,
          effectedUserIds: [dto.helpedUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async updateAnswer(
    user: UserDto,
    dto: UpdateSituationAnswerDto,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ) {
    await this.prismaService.$transaction(async (tx) => {
      tx = options?.tx ?? tx;
      for (const a of dto.answers) {
        const answer = await tx.situationReportAnswer.findFirst({
          where: { reportId: dto.reportId, questionId: a.questionId },
        });
        await tx.situationReportAnswer.upsert({
          where: { id: answer?.id ?? 0 },
          create: {
            reportId: dto.reportId,
            questionId: a.questionId,
            answerType: a.answerType,
            description: a.description,
          },
          update: {
            answerType: a.answerType,
            description: a.description,
          },
        });
      }
    });
  }

  async updateReport(user: UserDto, dto: UpdateSituationReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.situationReport.update({
        where: { id: dto.reportId },
        data: { isCompleted: dto.isCompleted ? true : undefined },
      });
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.SITUATION_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
      await this.analysisGateway.sendStages(user, { tx: tx });
    });
  }

  async updateSituationHelp(
    user: UserDto,
    dto: UpdateSituationHelpDto,
    help: SituationHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await this.updateAnswer(
        user,
        {
          reportId: help.reportId,
          answers: [
            {
              questionId: help.questionId,
              answerType: dto.answerType,
              description: dto.description,
            },
          ],
        },
        { tx: tx },
      );
      await tx.situationHelp.update({
        where: { id: help.id },
        data: {
          status: HelpStatusEnum.APPROVED,
          answerType: dto.answerType,
          description: dto.description,
        },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.SITUATION_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async deleteReport(user: UserDto, dto: DeleteSituationReportDto) {
    await this.prismaService.situationReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteSituationHelp(user: UserDto, dto: DeleteSituationHelpDto) {
    await this.prismaService.situationHelp.delete({
      where: { id: dto.helpId },
    });
  }
}
