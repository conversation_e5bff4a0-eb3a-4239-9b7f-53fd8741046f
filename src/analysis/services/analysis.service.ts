import { Injectable } from '@nestjs/common';
import { UserDto } from '../../common/dtos/user.dto';
import { GetCurrentAnalysisDto } from '../dtos/analysis/get-current-analysis.dto';
import { SituationService } from './situation.service';
import { ChainService } from './chain.service';
import { CapitalService } from './capital.service';
import { AnalysisUtilService } from '../../common/services/analysis.util.service';
import { CurrentTabEnum } from '../enums/current-tab.enum';

@Injectable()
export class AnalysisService {
  constructor(
    private situationService: SituationService,
    private chainService: ChainService,
    private capitalService: CapitalService,
    private analysisUtilService: AnalysisUtilService,
  ) {}

  async getStages(user: UserDto) {
    return await this.analysisUtilService.getStages(user);
  }

  async getCurrents(user: UserDto, dto: GetCurrentAnalysisDto) {
    let reports: any[];
    let currents: any[];
    if (!dto.currentTab || dto.currentTab == CurrentTabEnum.SITUATION) {
      reports = await this.situationService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        isCompleted: true,
      });
      currents = await this.situationService.getSuggestions(user, {
        subsidiaryId: dto.subsidiaryId,
        reportId: dto.reportId,
      });
    } else if (dto.currentTab == CurrentTabEnum.CHAIN) {
      reports = await this.chainService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        isCompleted: true,
      });
      const { chains } = await this.chainService.getCurrentChains(user, {
        subsidiaryId: dto.subsidiaryId,
        reportId: dto.reportId,
      });
      currents = chains;
    } else if (dto.currentTab == CurrentTabEnum.CAPITAL) {
      reports = await this.capitalService.getReports(user, {
        subsidiaryId: dto.subsidiaryId,
        isCompleted: true,
      });
      const { capitals } = await this.capitalService.getCurrentCapitals(user, {
        subsidiaryId: dto.subsidiaryId,
        reportId: dto.reportId,
      });
      currents = capitals;
    }
    reports = reports
      .sort((r1, r2) => r2.createdAt - r1.createdAt)
      .map((r) => {
        return { id: r.id, name: r.name };
      });
    return { reports: reports, currents: currents };
  }
}
