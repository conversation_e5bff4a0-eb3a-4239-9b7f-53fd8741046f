import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetChainDto } from '../dtos/chain/get-chain.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteChainReportDto } from '../dtos/chain/delete-chain-report.dto';
import { Chain, ChainHelp, ChainReport, PrismaClient } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { CreateChainDto } from '../dtos/chain/create-chain.dto';
import { UpdateChainDto } from '../dtos/chain/update-chain.dto';
import { DeleteChainDto } from '../dtos/chain/delete-chain.dto';
import { UpdateChainReportDto } from '../dtos/chain/update-chain-report.dto';
import { GetChainHelpDto } from '../dtos/chain/get-chain-help.dto';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateChainHelpDto } from '../dtos/chain/create-chain-help.dto';
import { ActivityService } from '../../activity/activity.service';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { UpdateChainHelpDto } from '../dtos/chain/update-chain-help.dto';
import { DeleteChainHelpDto } from '../dtos/chain/delete-chain-help.dto';
import { GetCurrentChainDto } from '../dtos/chain/get-current-chain.dto';
import { GetChainReportDto } from '../dtos/chain/get-chain-report.dto';
import { GetChainReportUserDto } from '../dtos/chain/get-chain-report-user.dto';
import { OrganizationService } from '../../organization/services/organization.service';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { ChainGateway } from '../gateways/chain.gateway';

@Injectable()
export class ChainService {
  private readonly logger = new Logger(ChainService.name);

  constructor(
    private prismaService: PrismaService,
    private activityService: ActivityService,
    private organizationService: OrganizationService,
    private organizationUtilService: OrganizationUtilService,
    private chainGateway: ChainGateway,
  ) {}

  async getChains(
    user: UserDto,
    dto: GetChainDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
    },
  ) {
    const dbService = options?.tx ?? this.prismaService;
    const organizationIds =
      await this.organizationService.getOrganizationIds(user);
    const report = await dbService.chainReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        isCompleted: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
      },
      where: { id: dto.reportId, organizationId: { in: organizationIds } },
    });
    const chains = await dbService.chain.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        type: true,
        order: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        updatedUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          take: 1,
          select: {
            id: true,
            status: true,
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: { in: organizationIds } },
      },
      orderBy: { order: 'asc' },
    });
    for (const c of chains) {
      const createdUser = plainToInstance(UserDataDto, c.createdUser, {
        excludeExtraneousValues: true,
      });
      const updatedUser = plainToInstance(UserDataDto, c.updatedUser, {
        excludeExtraneousValues: true,
      });
      let helpedUser: UserDataDto;
      if (c.helps.length > 0) {
        helpedUser = plainToInstance(UserDataDto, c.helps[0].helpedUser, {
          excludeExtraneousValues: true,
        });
        helpedUser['status'] = c.helps[0].status;
      }
      c['responsibleUserInfo'] = createdUser;
      if (helpedUser) {
        c['responsibleUserInfo'] = helpedUser;
      } else if (updatedUser) {
        c['responsibleUserInfo'] = updatedUser;
      }

      if (c['responsibleUserInfo']) {
        c['responsibleUserInfo']['isMe'] =
          c['responsibleUserInfo'].id == user.id;
      }
      delete c.createdUser;
      delete c.updatedUser;
      delete c.helps;
    }
    report['createdUserInfo'] = plainToInstance(
      UserDataDto,
      report.createdUser,
      {
        excludeExtraneousValues: true,
      },
    );
    delete report.createdUser;
    return { report: report, chains: chains };
  }

  async getCurrentChains(user: UserDto, dto: GetCurrentChainDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    let report: ChainReport;
    if (dto.reportId) {
      report = await this.prismaService.chainReport.findFirstOrThrow({
        where: {
          id: dto.reportId,
          organizationId: dto.subsidiaryId,
          isCompleted: true,
        },
      });
    } else {
      report = await this.getLastReport(dto.subsidiaryId, {
        isCompleted: true,
      });
      if (!report) {
        return { report: null, chains: [] };
      }
    }
    const { chains } = await this.getChains(user, {
      reportId: report.id,
    });
    return {
      report: report,
      chains: chains,
    };
  }

  async getReports(user: UserDto, dto: GetChainReportDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const reports = await this.prismaService.chainReport.findMany({
      select: {
        id: true,
        name: true,
        isCompleted: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
      },
      where: {
        organizationId: dto.subsidiaryId,
        isCompleted:
          dto.isCompleted === undefined ? undefined : dto.isCompleted,
      },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      delete r.createdUser;
    }
    return reports;
  }

  async getReportUsers(user: UserDto, dto: GetChainReportUserDto) {
    dto.subsidiaryId = dto.subsidiaryId ?? user.organizationId;
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: dto.subsidiaryId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: dto.subsidiaryId } },
        OR: [
          {
            createdChainReports: {
              some: { id: dto.reportId, organizationId: dto.subsidiaryId },
            },
          },
          {
            helpedChainHelps: {
              some: {
                chain: {
                  report: {
                    id: dto.reportId,
                    organizationId: dto.subsidiaryId,
                  },
                },
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  async getLastReport(
    organizationId: number,
    options?: { isCompleted?: boolean },
  ): Promise<ChainReport> {
    const report = await this.prismaService.chainReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async getChainHelps(user: UserDto, dto: GetChainHelpDto) {
    const help = await this.prismaService.chainHelp.findFirstOrThrow({
      select: {
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        chain: {
          select: {
            id: true,
            name: true,
            description: true,
            type: true,
            report: {
              select: {
                id: true,
                name: true,
                organization: { select: { id: true, name: true } },
              },
            },
          },
        },
        status: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
      },
      where: {
        id: dto.helpId,
        helpedUserId: user.id,
        chain: { report: { organizationId: user.organizationId } },
      },
    });
    help['createdUserInfo'] = plainToInstance(UserDataDto, help.createdUser, {
      excludeExtraneousValues: true,
    });
    delete help.createdUser;
    return help;
  }

  async createChain(user: UserDto, dto: CreateChainDto) {
    await this.prismaService.$transaction(async (tx) => {
      const chain = await tx.chain.create({
        data: {
          createdUserId: user.id,
          reportId: dto.reportId,
          name: dto.name,
          description: dto.description,
          type: dto.type,
          order: dto.order,
        },
      });
      await tx.chain.updateMany({
        where: {
          id: { not: chain.id },
          reportId: chain.reportId,
          type: chain.type,
          order: { gte: chain.order },
        },
        data: { order: { increment: 1 } },
      });
    });

    const result = await this.getChains(user, { reportId: dto.reportId });
    this.chainGateway
      .sendChains(user, dto.reportId, result.chains)
      .catch((error) => {
        this.logger.error(error);
      });
  }

  async createReport(user: UserDto): Promise<{ report: any; chains: any[] }> {
    const reportCount = await this.prismaService.chainReport.count({
      where: { organizationId: user.organizationId },
    });
    const defaults = await this.prismaService.chainDefault.findMany();
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const report = await tx.chainReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          name: `DZ_${reportCount + 1}_${dateFormat}`,
        },
      });
      await tx.chain.createMany({
        data: defaults.map((d) => {
          return {
            createdUserId: user.id,
            reportId: report.id,
            name: d.name,
            description: d.description,
            type: d.type,
            order: d.order,
          };
        }),
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.CHAIN_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: report.id },
        },
      ]);
      const { chains } = await this.getChains(
        user,
        { reportId: report.id },
        { tx: tx },
      );
      return {
        report: report,
        chains: chains,
      };
    });

    const chainReports = await this.getReports(user, {});
    this.chainGateway.sendReports(user, chainReports).catch((error) => {
      this.logger.error(error);
    });

    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: ChainReport; chains: any[] }> {
    let lastReport = await this.getLastReport(user.organizationId, {
      isCompleted: true,
    });
    if (!lastReport) {
      lastReport = await this.getLastReport(user.organizationId, {
        isCompleted: false,
      });
      if (!lastReport) {
        return await this.createReport(user);
      }
    }
    const { chains } = await this.getChains(user, {
      reportId: lastReport.id,
    });

    const chainReports = await this.getReports(user, {});
    this.chainGateway.sendReports(user, chainReports).catch((error) => {
      this.logger.error(error);
    });

    return {
      report: lastReport,
      chains: chains,
    };
  }

  async createChainHelp(user: UserDto, dto: CreateChainHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.chainHelp.deleteMany({
        where: {
          chainId: dto.chainId,
          status: HelpStatusEnum.PENDING,
        },
      });
      const help = await tx.chainHelp.create({
        data: {
          createdUserId: user.id,
          helpedUserId: dto.helpedUserId,
          chainId: dto.chainId,
        },
      });
      await this.updateChain(
        user,
        { chainId: dto.chainId, description: null },
        { tx: tx, removeUpdatedUserId: true },
      );
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.CHAIN_HELP,
          effectedUserIds: [dto.helpedUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async updateChain(
    user: UserDto,
    dto: UpdateChainDto,
    options?: {
      tx?: Omit<PrismaClient, ITXClientDenyList>;
      chain?: Chain;
      removeUpdatedUserId?: boolean;
      removeHelps?: boolean;
    },
  ) {
    const updatedUserId = dto.name || dto.description ? user.id : undefined;
    await this.prismaService.$transaction(async (tx) => {
      tx = options?.tx ?? tx;
      await tx.chain.update({
        where: {
          id: dto.chainId,
          report: { organizationId: user.organizationId },
        },
        data: {
          updatedUserId: options?.removeUpdatedUserId ? null : updatedUserId,
          name: dto.name,
          description: dto.description,
          order: dto.order,
        },
      });

      if (options?.chain && dto.order) {
        const chain = options.chain;
        if (chain.order > dto.order) {
          await tx.chain.updateMany({
            where: {
              id: { not: chain.id },
              reportId: chain.reportId,
              type: chain.type,
              AND: [
                { order: { gte: dto.order } },
                { order: { lt: chain.order } },
              ],
            },
            data: { order: { increment: 1 } },
          });
        } else if (chain.order < dto.order) {
          await tx.chain.updateMany({
            where: {
              id: { not: chain.id },
              reportId: chain.reportId,
              type: chain.type,
              AND: [
                { order: { gt: chain.order } },
                { order: { lte: dto.order } },
              ],
            },
            data: { order: { decrement: 1 } },
          });
        }
      }

      if (options?.removeHelps && updatedUserId) {
        await tx.chainHelp.deleteMany({
          where: { chainId: dto.chainId },
        });
      }

      const reportId = options?.chain?.reportId
        ? options.chain.reportId
        : (
            await this.prismaService.chain.findFirstOrThrow({
              select: { reportId: true },
              where: {
                id: dto.chainId,
                report: { organizationId: user.organizationId },
              },
            })
          ).reportId;
      const result = await this.getChains(
        user,
        {
          reportId: reportId,
        },
        { tx: tx },
      );
      this.chainGateway
        .sendChains(user, reportId, result.chains)
        .catch((error) => {
          this.logger.error(error);
        });
    });
  }

  async updateReport(user: UserDto, dto: UpdateChainReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.chainReport.update({
        where: { id: dto.reportId },
        data: { isCompleted: dto.isCompleted ? true : undefined },
      });
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.CHAIN_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
    });

    const chainReports = await this.getReports(user, {});
    this.chainGateway.sendReports(user, chainReports).catch((error) => {
      this.logger.error(error);
    });
  }

  async updateChainHelp(
    user: UserDto,
    dto: UpdateChainHelpDto,
    help: ChainHelp,
  ) {
    await this.prismaService.$transaction(async (tx) => {
      await this.updateChain(
        user,
        {
          chainId: help.chainId,
          description: dto.description,
        },
        { tx: tx, removeUpdatedUserId: true },
      );
      await tx.chainHelp.update({
        where: { id: help.id },
        data: {
          status: HelpStatusEnum.APPROVED,
          description: dto.description,
        },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.CHAIN_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async deleteChain(user: UserDto, dto: DeleteChainDto) {
    let reportId: number;
    await this.prismaService.$transaction(async (tx) => {
      const chain = await tx.chain.delete({
        where: {
          id: dto.chainId,
          report: { organizationId: user.organizationId },
        },
      });
      reportId = chain.reportId;
      await tx.chain.updateMany({
        where: {
          reportId: chain.reportId,
          order: { gt: chain.order },
        },
        data: { order: { decrement: 1 } },
      });
    });

    if (reportId) {
      const result = await this.getChains(user, {
        reportId: reportId,
      });
      this.chainGateway
        .sendChains(user, reportId, result.chains)
        .catch((error) => {
          this.logger.error(error);
        });
    }
  }

  async deleteReport(user: UserDto, dto: DeleteChainReportDto) {
    await this.prismaService.chainReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });

    const chainReports = await this.getReports(user, {});
    this.chainGateway.sendReports(user, chainReports).catch((error) => {
      this.logger.error(error);
    });
  }

  async deleteChainHelp(user: UserDto, dto: DeleteChainHelpDto) {
    await this.prismaService.chainHelp.delete({
      where: { id: dto.helpId },
    });
  }
}
