import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { UserDto } from '../../common/dtos/user.dto';
import { GetRiskDto } from '../dtos/risk/get-risk.dto';
import { plainToInstance } from 'class-transformer';
import { UserDataDto } from '../../common/dtos/user-data.dto';
import { format } from 'date-fns';
import { DeleteRiskReportDto } from '../dtos/risk/delete-risk-report.dto';
import { PrismaClient, RiskHelp, RiskReport, Trend } from '@prisma/client';
import { ITXClientDenyList } from '@prisma/client/runtime/library';
import { UpdateRiskDto } from '../dtos/risk/update-risk.dto';
import { CreateRiskReportDto } from '../dtos/risk/create-risk-report.dto';
import { UpdateRiskReportDto } from '../dtos/risk/update-risk-report.dto';
import { AnalysisGateway } from '../gateways/analysis.gateway';
import { GetRiskHelpDto } from '../dtos/risk/get-risk-help.dto';
import { ActivityEnum } from '../../activity/enums/activity.enum';
import { CreateRiskHelpDto } from '../dtos/risk/create-risk-help.dto';
import { ActivityService } from '../../activity/activity.service';
import { HelpStatusEnum } from '../enums/help-status.enum';
import { UpdateRiskHelpDto } from '../dtos/risk/update-risk-help.dto';
import { DeleteRiskHelpDto } from '../dtos/risk/delete-risk-help.dto';
import { OrganizationUtilService } from '../../common/services/organization.util.service';
import { RiskValidation } from '../validations/risk.validation';
import { TrendService } from './trend.service';

@Injectable()
export class RiskService {
  constructor(
    private prismaService: PrismaService,
    private analysisGateway: AnalysisGateway,
    private activityService: ActivityService,
    private organizationUtilService: OrganizationUtilService,
    private trendService: TrendService,
    private riskValidation: RiskValidation,
  ) {}

  async getRisks(
    user: UserDto,
    dto: GetRiskDto,
    tx?: Omit<PrismaClient, ITXClientDenyList>,
  ) {
    const dbService = tx ?? this.prismaService;
    const report = await dbService.riskReport.findFirstOrThrow({
      select: {
        id: true,
        name: true,
        isCompleted: true,
        trendReport: { select: { id: true, name: true, threshold: true } },
      },
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
    if (!report.trendReport.threshold) {
      return { report: report, risks: [] };
    }
    let risks = await dbService.risk.findMany({
      select: {
        id: true,
        trend: {
          select: {
            id: true,
            name: true,
            source: true,
            severity: true,
            possibility: true,
            term: { select: { id: true, name: true } },
            category: { select: { id: true, name: true } },
          },
        },
        riskText: true,
        opportunity: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        helps: {
          take: 1,
          select: {
            id: true,
            status: true,
            riskText: true,
            opportunity: true,
            createdAt: true,
            updatedAt: true,
            createdUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                image: true,
                userOrganizations: true,
              },
            },
            helpedUser: {
              select: {
                id: true,
                name: true,
                surname: true,
                email: true,
                image: true,
                userOrganizations: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
      },
      where: {
        report: { id: dto.reportId, organizationId: user.organizationId },
      },
    });
    risks = risks.filter(
      (r) =>
        r.trend.severity &&
        r.trend.possibility &&
        r.trend.severity * r.trend.possibility >= report.trendReport.threshold,
    );
    for (const r of risks) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      r['help'] = r.helps.length > 0 ? r.helps[0] : undefined;
      if (r['help']) {
        r['help']['createdUserInfo'] = plainToInstance(
          UserDataDto,
          r['help'].createdUser,
          {
            excludeExtraneousValues: true,
          },
        );
        r['help']['helpedUserInfo'] = plainToInstance(
          UserDataDto,
          r['help'].helpedUser,
          {
            excludeExtraneousValues: true,
          },
        );
        delete r['help'].createdUser;
        delete r['help'].helpedUser;
      }
      delete r.createdUser;
      delete r.helps;
    }
    return { report: report, risks: risks };
  }

  async getCurrentRisks(user: UserDto) {
    const lastFinishedReport = await this.getLastReport(user.organizationId, {
      isCompleted: true,
    });
    if (!lastFinishedReport) {
      return { report: null, risks: [] };
    }
    lastFinishedReport['trendReport'] =
      await this.prismaService.trendReport.findFirst({
        select: { id: true, name: true, threshold: true },
        where: { id: lastFinishedReport.trendReportId },
      });
    const { risks } = await this.getRisks(user, {
      reportId: lastFinishedReport.id,
    });
    return {
      report: lastFinishedReport,
      risks: risks,
    };
  }

  async getReports(user: UserDto) {
    const reports = await this.prismaService.riskReport.findMany({
      select: {
        id: true,
        name: true,
        isCompleted: true,
        createdAt: true,
        updatedAt: true,
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        organization: { select: { id: true, name: true } },
        trendReport: { select: { id: true, name: true, threshold: true } },
      },
      where: { organizationId: user.organizationId },
    });
    for (const r of reports) {
      r['createdUserInfo'] = plainToInstance(UserDataDto, r.createdUser, {
        excludeExtraneousValues: true,
      });
      delete r.createdUser;
    }
    return reports;
  }

  async getReportUsers(user: UserDto) {
    const users = await this.prismaService.user.findMany({
      select: {
        id: true,
        name: true,
        surname: true,
        image: true,
        userOrganizations: {
          select: { roleId: true, title: true, organization: true },
          where: { organizationId: user.organizationId },
        },
      },
      where: {
        userOrganizations: { some: { organizationId: user.organizationId } },
        OR: [
          {
            createdRiskReports: {
              some: { organizationId: user.organizationId },
            },
          },
          {
            helpedRiskHelps: {
              some: {
                risk: { report: { organizationId: user.organizationId } },
              },
            },
          },
        ],
      },
    });
    const usersList: UserDataDto[] = [];
    for (const u of users) {
      usersList.push(
        plainToInstance(UserDataDto, u, {
          excludeExtraneousValues: true,
          groups: ['organization'],
        }),
      );
    }
    return usersList;
  }

  private async getLastReport(
    organizationId: number,
    options?: { isCompleted?: boolean },
  ): Promise<RiskReport> {
    const report = await this.prismaService.riskReport.findFirst({
      where: {
        organizationId: organizationId,
        isCompleted: options?.isCompleted,
      },
      orderBy: { createdAt: 'desc' },
    });
    return report;
  }

  async getRiskHelps(user: UserDto, dto: GetRiskHelpDto) {
    const help = await this.prismaService.riskHelp.findFirstOrThrow({
      select: {
        createdUser: {
          select: {
            id: true,
            name: true,
            surname: true,
            image: true,
            userOrganizations: true,
          },
        },
        risk: {
          select: {
            id: true,
            trend: {
              select: {
                name: true,
                source: true,
                term: { select: { id: true, name: true } },
                category: { select: { id: true, name: true } },
              },
            },
            riskText: true,
            opportunity: true,
            report: {
              select: {
                id: true,
                name: true,
                organization: { select: { id: true, name: true } },
              },
            },
          },
        },
        status: true,
        riskText: true,
        opportunity: true,
        createdAt: true,
        updatedAt: true,
        deletedAt: true,
      },
      where: { id: dto.helpId, helpedUserId: user.id },
    });
    help['createdUserInfo'] = plainToInstance(UserDataDto, help.createdUser, {
      excludeExtraneousValues: true,
    });
    delete help.createdUser;
    return help;
  }

  async createReport(
    user: UserDto,
    dto: CreateRiskReportDto,
    trends: Trend[],
  ): Promise<{ report: any; risks: any[] }> {
    const reportCount = await this.prismaService.riskReport.count({
      where: { organizationId: user.organizationId },
    });
    const dateFormat = format(new Date(), 'ddMMyyyy');
    const result = await this.prismaService.$transaction(async (tx) => {
      const report = await tx.riskReport.create({
        data: {
          createdUserId: user.id,
          organizationId: user.organizationId,
          trendReportId: dto.trendReportId,
          name: `RvF_${reportCount + 1}_${dateFormat}`,
        },
      });
      await tx.risk.createMany({
        data: trends.map((t) => {
          return {
            createdUserId: user.id,
            reportId: report.id,
            trendId: t.id,
          };
        }),
      });
      const effectedUserIds =
        await this.organizationUtilService.getOrganizationUserIds(user, {
          tx: tx,
        });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.TREND_REPORT_COMPLETE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: report.trendReportId },
        },
        {
          activityType: ActivityEnum.RISK_REPORT_CREATE,
          effectedUserIds: effectedUserIds,
          payload: { reportId: report.id },
        },
      ]);
      const { risks } = await this.getRisks(user, { reportId: report.id }, tx);
      await this.analysisGateway.sendStages(user, { tx: tx });
      return {
        report: report,
        risks: risks,
      };
    });
    return result;
  }

  async createReportForSidebar(
    user: UserDto,
  ): Promise<{ report: any; risks: any[] }> {
    const reportCount = await this.prismaService.riskReport.count({
      where: { organizationId: user.organizationId },
    });
    if (reportCount == 0) {
      const lastUnfinishedTrendReport = await this.trendService.getLastReport(
        user.organizationId,
        {
          isCompleted: false,
          hasThreshold: true,
        },
      );
      if (!lastUnfinishedTrendReport) {
        throw new BadRequestException(
          'Trend analizi raporunuza minimum değer giriniz.',
        );
      }
      const trends = await this.riskValidation.createReport(user, {
        trendReportId: lastUnfinishedTrendReport.id,
      });
      return await this.createReport(
        user,
        { trendReportId: lastUnfinishedTrendReport.id },
        trends,
      );
    } else {
      let lastReport = await this.getLastReport(user.organizationId, {
        isCompleted: true,
      });
      if (!lastReport) {
        lastReport = await this.getLastReport(user.organizationId, {
          isCompleted: false,
        });
      }
      const { risks } = await this.getRisks(user, {
        reportId: lastReport.id,
      });
      return {
        report: lastReport,
        risks: risks,
      };
    }
  }

  async createRiskHelp(user: UserDto, dto: CreateRiskHelpDto) {
    await this.prismaService.$transaction(async (tx) => {
      const help = await tx.riskHelp.create({
        data: {
          createdUserId: user.id,
          helpedUserId: dto.helpedUserId,
          riskId: dto.riskId,
        },
      });
      await this.updateRisk(
        user,
        { riskId: dto.riskId, riskText: null, opportunity: null },
        { tx: tx },
      );
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.RISK_HELP,
          effectedUserIds: [dto.helpedUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async updateRisk(
    user: UserDto,
    dto: UpdateRiskDto,
    options?: { tx?: Omit<PrismaClient, ITXClientDenyList> },
  ) {
    const dbService = options?.tx ?? this.prismaService;
    await dbService.risk.update({
      where: {
        id: dto.riskId,
        report: { organizationId: user.organizationId },
      },
      data: {
        riskText: dto.riskText,
        opportunity: dto.opportunity,
      },
    });
  }

  async updateReport(user: UserDto, dto: UpdateRiskReportDto) {
    await this.prismaService.$transaction(async (tx) => {
      await tx.riskReport.update({
        where: { id: dto.reportId },
        data: { isCompleted: dto.isCompleted ? true : undefined },
      });
      if (dto.isCompleted) {
        const effectedUserIds =
          await this.organizationUtilService.getOrganizationUserIds(user, {
            tx: tx,
          });
        await this.activityService.createActivity(tx, user, [
          {
            activityType: ActivityEnum.RISK_REPORT_COMPLETE,
            effectedUserIds: effectedUserIds,
            payload: { reportId: dto.reportId },
          },
        ]);
      }
      await this.analysisGateway.sendStages(user, { tx: tx });
    });
  }

  async updateRiskHelp(user: UserDto, dto: UpdateRiskHelpDto, help: RiskHelp) {
    await this.prismaService.$transaction(async (tx) => {
      await this.updateRisk(
        user,
        {
          riskId: help.riskId,
          riskText: dto.riskText,
          opportunity: dto.opportunity,
        },
        { tx: tx },
      );
      await tx.riskHelp.update({
        where: { id: help.id },
        data: {
          status: HelpStatusEnum.APPROVED,
          riskText: dto.riskText,
          opportunity: dto.opportunity,
        },
      });
      await this.activityService.createActivity(tx, user, [
        {
          activityType: ActivityEnum.RISK_HELP_ANSWER,
          effectedUserIds: [help.createdUserId],
          payload: { helpId: help.id },
        },
      ]);
    });
  }

  async deleteReport(user: UserDto, dto: DeleteRiskReportDto) {
    await this.prismaService.riskReport.delete({
      where: { id: dto.reportId, organizationId: user.organizationId },
    });
  }

  async deleteRiskHelp(user: UserDto, dto: DeleteRiskHelpDto) {
    await this.prismaService.riskHelp.delete({
      where: { id: dto.helpId },
    });
  }
}
