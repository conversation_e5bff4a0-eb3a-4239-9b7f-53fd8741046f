import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { TrendValidation } from '../validations/trend.validation';
import { TrendService } from '../services/trend.service';
import { GetTrendDto } from '../dtos/trend/get-trend.dto';
import { DeleteTrendReportDto } from '../dtos/trend/delete-trend-report.dto';
import { CreateTrendDto } from '../dtos/trend/create-trend.dto';
import { UpdateTrendDto } from '../dtos/trend/update-trend.dto';
import { DeleteTrendDto } from '../dtos/trend/delete-trend.dto';
import { UpdateTrendReportDto } from '../dtos/trend/update-trend-report.dto';
import { GetTrendHelpDto } from '../dtos/trend/get-trend-help.dto';
import { CreateTrendHelpDto } from '../dtos/trend/create-trend-help.dto';
import { UpdateTrendHelpDto } from '../dtos/trend/update-trend-help.dto';
import { DeleteTrendHelpDto } from '../dtos/trend/delete-trend-help.dto';

@ApiTags('Analysis/Trends')
@Controller('analysis/trends')
export class TrendController {
  constructor(
    private trendValidation: TrendValidation,
    private trendService: TrendService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getTrends(
    @User() user: UserDto,
    @Query() dto: GetTrendDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.trendService.getTrends(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('relations')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getTrendRelations(): Promise<GeneralResponseDto> {
    const relations = await this.trendService.getTrendRelations();
    return new GeneralResponseDto().setData(relations);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentTrends(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.trendService.getCurrentTrends(user);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(@User() user: UserDto): Promise<GeneralResponseDto> {
    const reports = await this.trendService.getReports(user);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(@User() user: UserDto): Promise<GeneralResponseDto> {
    const users = await this.trendService.getReportUsers(user);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getTrendHelps(
    @User() user: UserDto,
    @Query() dto: GetTrendHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.trendService.getTrendHelps(user, dto);
    return new GeneralResponseDto().setData(help);
  }

  @Post()
  @ApiBearerAuth()
  @ApiBody({ type: CreateTrendDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createTrend(
    @User() user: UserDto,
    @Body() dto: CreateTrendDto,
  ): Promise<GeneralResponseDto> {
    await this.trendValidation.createTrend(user, dto);
    await this.trendService.createTrend(user, dto);
    return new GeneralResponseDto();
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.trendService.createReport(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.trendService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createTrendHelp(
    @User() user: UserDto,
    @Body() dto: CreateTrendHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.trendValidation.createTrendHelp(user, dto);
    await this.trendService.createTrendHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateTrendDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateTrend(
    @User() user: UserDto,
    @Body() dto: UpdateTrendDto,
  ): Promise<GeneralResponseDto> {
    await this.trendValidation.updateTrend(user, dto);
    await this.trendService.updateTrend(user, dto);
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateTrendReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateTrendReportDto,
  ): Promise<GeneralResponseDto> {
    await this.trendValidation.updateReport(user, dto);
    await this.trendService.updateReport(user, dto);
    return new GeneralResponseDto();
  }

  @Put('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateTrendHelp(
    @User() user: UserDto,
    @Body() dto: UpdateTrendHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.trendValidation.updateTrendHelp(user, dto);
    await this.trendService.updateTrendHelp(user, dto, help);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteTrendDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteTrend(
    @User() user: UserDto,
    @Body() dto: DeleteTrendDto,
  ): Promise<GeneralResponseDto> {
    await this.trendValidation.deleteTrend(user, dto);
    await this.trendService.deleteTrend(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteTrendReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteTrendReportDto,
  ): Promise<GeneralResponseDto> {
    await this.trendValidation.deleteReport(user, dto);
    await this.trendService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteTrendHelp(
    @User() user: UserDto,
    @Body() dto: DeleteTrendHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.trendValidation.deleteTrendHelp(user, dto);
    await this.trendService.deleteTrendHelp(user, dto);
    return new GeneralResponseDto();
  }
}
