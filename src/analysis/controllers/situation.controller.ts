import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { SituationService } from '../services/situation.service';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { SituationValidation } from '../validations/situation.validation';
import { UpdateSituationAnswerDto } from '../dtos/situation/update-situation-answer.dto';
import { GetSituationQuestionDto } from '../dtos/situation/get-situation-question.dto';
import { CreateSituationHelpDto } from '../dtos/situation/create-situation-help.dto';
import { UpdateSituationHelpDto } from '../dtos/situation/update-situation-help.dto';
import { DeleteSituationReportDto } from '../dtos/situation/delete-situation-report.dto';
import { GetSituationSuggestionDto } from '../dtos/situation/get-situation-suggestion.dto';
import { GetSituationReportDto } from '../dtos/situation/get-situation-report.dto';
import { GetSituationReportUserDto } from '../dtos/situation/get-situation-report-user.dto';
import { GetSituationHelpDto } from '../dtos/situation/get-situation-help.dto';
import { DeleteSituationHelpDto } from '../dtos/situation/delete-situation-help.dto';
import { UpdateSituationReportDto } from '../dtos/situation/update-situation-report.dto';

@ApiTags('Analysis/Situations')
@Controller('analysis/situations')
export class SituationController {
  constructor(
    private situationValidation: SituationValidation,
    private situationService: SituationService,
  ) {}

  @Get('suggestions')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSuggestions(
    @User() user: UserDto,
    @Query() dto: GetSituationSuggestionDto,
  ): Promise<GeneralResponseDto> {
    await this.situationValidation.getSuggestions(user, dto);
    const suggestions = await this.situationService.getSuggestions(user, dto);
    return new GeneralResponseDto().setData(suggestions);
  }

  @Get('questions')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getQuestions(
    @User() user: UserDto,
    @Query() dto: GetSituationQuestionDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.situationService.getQuestions(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetSituationReportDto,
  ): Promise<GeneralResponseDto> {
    await this.situationValidation.getReports(user, dto);
    const reports = await this.situationService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetSituationReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.situationValidation.getReportUsers(user, dto);
    const users = await this.situationService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getSituationHelps(
    @User() user: UserDto,
    @Query() dto: GetSituationHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.situationService.getSituationHelps(user, dto);
    return new GeneralResponseDto().setData(help);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.situationService.createReport(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.situationService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createSituationHelp(
    @User() user: UserDto,
    @Body() dto: CreateSituationHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.situationValidation.createSituationHelp(user, dto);
    await this.situationService.createSituationHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Put('answers')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateSituationAnswerDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateAnswer(
    @User() user: UserDto,
    @Body() dto: UpdateSituationAnswerDto,
  ): Promise<GeneralResponseDto> {
    await this.situationValidation.updateAnswer(user, dto);
    await this.situationService.updateAnswer(user, dto);
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateSituationReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateSituationReportDto,
  ): Promise<GeneralResponseDto> {
    await this.situationValidation.updateReport(user, dto);
    await this.situationService.updateReport(user, dto);
    return new GeneralResponseDto();
  }

  @Put('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateSituationHelp(
    @User() user: UserDto,
    @Body() dto: UpdateSituationHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.situationValidation.updateSituationHelp(user, dto);
    await this.situationService.updateSituationHelp(user, dto, help);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteSituationReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteSituationReportDto,
  ): Promise<GeneralResponseDto> {
    await this.situationValidation.deleteReport(user, dto);
    await this.situationService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteSituationHelp(
    @User() user: UserDto,
    @Body() dto: DeleteSituationHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.situationValidation.deleteSituationHelp(user, dto);
    await this.situationService.deleteSituationHelp(user, dto);
    return new GeneralResponseDto();
  }
}
