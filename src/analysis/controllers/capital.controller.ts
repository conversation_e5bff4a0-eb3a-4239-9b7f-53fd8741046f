import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { CapitalValidation } from '../validations/capital.validation';
import { CapitalService } from '../services/capital.service';
import { GetCapitalDto } from '../dtos/capital/get-capital.dto';
import { DeleteCapitalReportDto } from '../dtos/capital/delete-capital-report.dto';
import { UpdateCapitalDto } from '../dtos/capital/update-capital.dto';
import { DeleteCapitalDto } from '../dtos/capital/delete-capital.dto';
import { UpdateCapitalReportDto } from '../dtos/capital/update-capital-report.dto';
import { GetCapitalHelpDto } from '../dtos/capital/get-capital-help.dto';
import { CreateCapitalHelpDto } from '../dtos/capital/create-capital-help.dto';
import { UpdateCapitalHelpDto } from '../dtos/capital/update-capital-help.dto';
import { DeleteCapitalHelpDto } from '../dtos/capital/delete-capital-help.dto';
import { GetCurrentCapitalDto } from '../dtos/capital/get-current-capital.dto';
import { GetCapitalReportDto } from '../dtos/capital/get-capital-report.dto';
import { GetCapitalReportUserDto } from '../dtos/capital/get-capital-report-user.dto';

@ApiTags('Analysis/Capitals')
@Controller('analysis/capitals')
export class CapitalController {
  constructor(
    private capitalValidation: CapitalValidation,
    private capitalService: CapitalService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCapitals(
    @User() user: UserDto,
    @Query() dto: GetCapitalDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.capitalService.getCapitals(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentCapitals(
    @User() user: UserDto,
    @Query() dto: GetCurrentCapitalDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.getCurrentCapitals(user, dto);
    const result = await this.capitalService.getCurrentCapitals(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetCapitalReportDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.getReports(user, dto);
    const reports = await this.capitalService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetCapitalReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.getReportUsers(user, dto);
    const users = await this.capitalService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCapitalHelps(
    @User() user: UserDto,
    @Query() dto: GetCapitalHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.capitalService.getCapitalHelps(user, dto);
    return new GeneralResponseDto().setData(help);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.capitalService.createReport(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.capitalService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createCapitalHelp(
    @User() user: UserDto,
    @Body() dto: CreateCapitalHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.createCapitalHelp(user, dto);
    await this.capitalService.createCapitalHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateCapitalDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateCapital(
    @User() user: UserDto,
    @Body() dto: UpdateCapitalDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.updateCapital(user, dto);
    await this.capitalService.updateCapital(user, dto, {
      removeHelps: true,
    });
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateCapitalReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateCapitalReportDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.updateReport(user, dto);
    await this.capitalService.updateReport(user, dto);
    return new GeneralResponseDto();
  }

  @Put('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateCapitalHelp(
    @User() user: UserDto,
    @Body() dto: UpdateCapitalHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.capitalValidation.updateCapitalHelp(user, dto);
    await this.capitalService.updateCapitalHelp(user, dto, help);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteCapitalDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteCapital(
    @User() user: UserDto,
    @Body() dto: DeleteCapitalDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.deleteCapital(user, dto);
    await this.capitalService.deleteCapital(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteCapitalReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteCapitalReportDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.deleteReport(user, dto);
    await this.capitalService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteCapitalHelp(
    @User() user: UserDto,
    @Body() dto: DeleteCapitalHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.capitalValidation.deleteCapitalHelp(user, dto);
    await this.capitalService.deleteCapitalHelp(user, dto);
    return new GeneralResponseDto();
  }
}
