import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { ChainValidation } from '../validations/chain.validation';
import { ChainService } from '../services/chain.service';
import { GetChainDto } from '../dtos/chain/get-chain.dto';
import { DeleteChainReportDto } from '../dtos/chain/delete-chain-report.dto';
import { CreateChainDto } from '../dtos/chain/create-chain.dto';
import { UpdateChainDto } from '../dtos/chain/update-chain.dto';
import { DeleteChainDto } from '../dtos/chain/delete-chain.dto';
import { UpdateChainReportDto } from '../dtos/chain/update-chain-report.dto';
import { GetChainHelpDto } from '../dtos/chain/get-chain-help.dto';
import { CreateChainHelpDto } from '../dtos/chain/create-chain-help.dto';
import { UpdateChainHelpDto } from '../dtos/chain/update-chain-help.dto';
import { DeleteChainHelpDto } from '../dtos/chain/delete-chain-help.dto';
import { GetCurrentChainDto } from '../dtos/chain/get-current-chain.dto';
import { GetChainReportDto } from '../dtos/chain/get-chain-report.dto';
import { GetChainReportUserDto } from '../dtos/chain/get-chain-report-user.dto';

@ApiTags('Analysis/Chains')
@Controller('analysis/chains')
export class ChainController {
  constructor(
    private chainValidation: ChainValidation,
    private chainService: ChainService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getChains(
    @User() user: UserDto,
    @Query() dto: GetChainDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.chainService.getChains(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentChains(
    @User() user: UserDto,
    @Query() dto: GetCurrentChainDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.getCurrentChains(user, dto);
    const result = await this.chainService.getCurrentChains(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(
    @User() user: UserDto,
    @Query() dto: GetChainReportDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.getReports(user, dto);
    const reports = await this.chainService.getReports(user, dto);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(
    @User() user: UserDto,
    @Query() dto: GetChainReportUserDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.getReportUsers(user, dto);
    const users = await this.chainService.getReportUsers(user, dto);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getChainHelps(
    @User() user: UserDto,
    @Query() dto: GetChainHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.chainService.getChainHelps(user, dto);
    return new GeneralResponseDto().setData(help);
  }

  @Post()
  @ApiBearerAuth()
  @ApiBody({ type: CreateChainDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createChain(
    @User() user: UserDto,
    @Body() dto: CreateChainDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.createChain(user, dto);
    await this.chainService.createChain(user, dto);
    return new GeneralResponseDto();
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.chainService.createReport(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.chainService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createChainHelp(
    @User() user: UserDto,
    @Body() dto: CreateChainHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.createChainHelp(user, dto);
    await this.chainService.createChainHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateChainDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateChain(
    @User() user: UserDto,
    @Body() dto: UpdateChainDto,
  ): Promise<GeneralResponseDto> {
    const chain = await this.chainValidation.updateChain(user, dto);
    await this.chainService.updateChain(user, dto, {
      chain: chain,
      removeHelps: true,
    });
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateChainReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateChainReportDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.updateReport(user, dto);
    await this.chainService.updateReport(user, dto);
    return new GeneralResponseDto();
  }

  @Put('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateChainHelp(
    @User() user: UserDto,
    @Body() dto: UpdateChainHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.chainValidation.updateChainHelp(user, dto);
    await this.chainService.updateChainHelp(user, dto, help);
    return new GeneralResponseDto();
  }

  @Delete()
  @ApiBearerAuth()
  @ApiBody({ type: DeleteChainDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteChain(
    @User() user: UserDto,
    @Body() dto: DeleteChainDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.deleteChain(user, dto);
    await this.chainService.deleteChain(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteChainReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteChainReportDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.deleteReport(user, dto);
    await this.chainService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteChainHelp(
    @User() user: UserDto,
    @Body() dto: DeleteChainHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.chainValidation.deleteChainHelp(user, dto);
    await this.chainService.deleteChainHelp(user, dto);
    return new GeneralResponseDto();
  }
}
