import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { GetRiskDto } from '../dtos/risk/get-risk.dto';
import { DeleteRiskReportDto } from '../dtos/risk/delete-risk-report.dto';
import { UpdateRiskDto } from '../dtos/risk/update-risk.dto';
import { RiskValidation } from '../validations/risk.validation';
import { RiskService } from '../services/risk.service';
import { CreateRiskReportDto } from '../dtos/risk/create-risk-report.dto';
import { UpdateRiskReportDto } from '../dtos/risk/update-risk-report.dto';
import { GetRiskHelpDto } from '../dtos/risk/get-risk-help.dto';
import { CreateRiskHelpDto } from '../dtos/risk/create-risk-help.dto';
import { UpdateRiskHelpDto } from '../dtos/risk/update-risk-help.dto';
import { DeleteRiskHelpDto } from '../dtos/risk/delete-risk-help.dto';

@ApiTags('Analysis/Risks')
@Controller('analysis/risks')
export class RiskController {
  constructor(
    private riskValidation: RiskValidation,
    private riskService: RiskService,
  ) {}

  @Get()
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getRisks(
    @User() user: UserDto,
    @Query() dto: GetRiskDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.riskService.getRisks(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Get('current')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrentRisks(@User() user: UserDto): Promise<GeneralResponseDto> {
    const result = await this.riskService.getCurrentRisks(user);
    return new GeneralResponseDto().setData(result);
  }

  @Get('reports')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReports(@User() user: UserDto): Promise<GeneralResponseDto> {
    const reports = await this.riskService.getReports(user);
    return new GeneralResponseDto().setData(reports);
  }

  @Get('reports/users')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getReportUsers(@User() user: UserDto): Promise<GeneralResponseDto> {
    const users = await this.riskService.getReportUsers(user);
    return new GeneralResponseDto().setData(users);
  }

  @Get('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getRiskHelps(
    @User() user: UserDto,
    @Query() dto: GetRiskHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.riskService.getRiskHelps(user, dto);
    return new GeneralResponseDto().setData(help);
  }

  @Post('reports')
  @ApiBearerAuth()
  @ApiBody({ type: CreateRiskReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReport(
    @User() user: UserDto,
    @Body() dto: CreateRiskReportDto,
  ): Promise<GeneralResponseDto> {
    const trends = await this.riskValidation.createReport(user, dto);
    const result = await this.riskService.createReport(user, dto, trends);
    return new GeneralResponseDto().setData(result);
  }

  @Post('reports/sidebar')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createReportForSidebar(
    @User() user: UserDto,
  ): Promise<GeneralResponseDto> {
    const result = await this.riskService.createReportForSidebar(user);
    return new GeneralResponseDto().setData(result);
  }

  @Post('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createRiskHelp(
    @User() user: UserDto,
    @Body() dto: CreateRiskHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.riskValidation.createRiskHelp(user, dto);
    await this.riskService.createRiskHelp(user, dto);
    return new GeneralResponseDto();
  }

  @Put()
  @ApiBearerAuth()
  @ApiBody({ type: UpdateRiskDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateRisk(
    @User() user: UserDto,
    @Body() dto: UpdateRiskDto,
  ): Promise<GeneralResponseDto> {
    await this.riskValidation.updateRisk(user, dto);
    await this.riskService.updateRisk(user, dto);
    return new GeneralResponseDto();
  }

  @Put('reports')
  @ApiBearerAuth()
  @ApiBody({ type: UpdateRiskReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateReport(
    @User() user: UserDto,
    @Body() dto: UpdateRiskReportDto,
  ): Promise<GeneralResponseDto> {
    await this.riskValidation.updateReport(user, dto);
    const result = await this.riskService.updateReport(user, dto);
    return new GeneralResponseDto().setData(result);
  }

  @Put('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async updateRiskHelp(
    @User() user: UserDto,
    @Body() dto: UpdateRiskHelpDto,
  ): Promise<GeneralResponseDto> {
    const help = await this.riskValidation.updateRiskHelp(user, dto);
    await this.riskService.updateRiskHelp(user, dto, help);
    return new GeneralResponseDto();
  }

  @Delete('reports')
  @ApiBearerAuth()
  @ApiBody({ type: DeleteRiskReportDto })
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteReport(
    @User() user: UserDto,
    @Body() dto: DeleteRiskReportDto,
  ): Promise<GeneralResponseDto> {
    await this.riskValidation.deleteReport(user, dto);
    await this.riskService.deleteReport(user, dto);
    return new GeneralResponseDto();
  }

  @Delete('helps')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteRiskHelp(
    @User() user: UserDto,
    @Body() dto: DeleteRiskHelpDto,
  ): Promise<GeneralResponseDto> {
    await this.riskValidation.deleteRiskHelp(user, dto);
    await this.riskService.deleteRiskHelp(user, dto);
    return new GeneralResponseDto();
  }
}
