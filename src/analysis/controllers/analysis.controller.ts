import { Controller, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { User } from '../../common/decorators/auth.decorator';
import { GeneralResponseDto } from '../../common/dtos/general-response.dto';
import { UserDto } from '../../common/dtos/user.dto';
import { AnalysisService } from '../services/analysis.service';
import { GetCurrentAnalysisDto } from '../dtos/analysis/get-current-analysis.dto';
import { AnalysisValidation } from '../validations/analysis.validation';

@ApiTags('Analysis')
@Controller('analysis')
export class AnalysisController {
  constructor(
    private analysisService: AnalysisService,
    private analysisValidation: AnalysisValidation,
  ) {}

  @Get('stages')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCapitals(@User() user: UserDto): Promise<GeneralResponseDto> {
    const stages = await this.analysisService.getStages(user);
    return new GeneralResponseDto().setData(stages);
  }

  @Get('currents')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getCurrents(
    @User() user: UserDto,
    @Query() dto: GetCurrentAnalysisDto,
  ): Promise<GeneralResponseDto> {
    await this.analysisValidation.getCurrents(user, dto);
    const result = await this.analysisService.getCurrents(user, dto);
    return new GeneralResponseDto().setData(result);
  }
}
