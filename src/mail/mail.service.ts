import { BadRequestException, Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { OrganizationInviteMailDto } from './dtos/organization-invite-mail.dto';
import { ConfigService } from '@nestjs/config';
import { ActivationMailDto } from './dtos/activation-mail.dto';
import { createCanvas, loadImage } from 'canvas';
import { mainDirectory } from '../main';
import { ForgotPasswordMailDto } from './dtos/forgot-password-mail.dto';
import { PrismaService } from '../common/services/prisma.service';
import { GetOrganizationInviteContentDto } from './dtos/get-organization-invite-content.dto';
import { UserPending } from '@prisma/client';
import { UserPendingTypeEnum } from './enums/user-pending-type.enum';
import { OrganizationTypeEnum } from '../organization/enums/organization-type.enum';
import { OrganizationUtilService } from '../common/services/organization.util.service';
import { OpinionHelpMailDto } from './dtos/opinion-help-mail.dto';
import { UserDto } from '../common/dtos/user.dto';
import { GovernanceInviteMailDto } from './dtos/governance-invite-mail.dto';
import { OrganizationInviteCodeMailDto } from './dtos/organization-invite-code-mail.dto';
import { GovernanceInviteCodeMailDto } from './dtos/governance-invite-code-mail.dto';
import { MetricSupervisorInviteMailDto } from './dtos/metric-supervisor-invite-mail.dto';
import { NotificationMailDto } from './dtos/notification-mail.dto';

@Injectable()
export class MailService {
  private readonly APP_NAME: string;
  private readonly APP_PUBLIC_URL: string;
  private readonly FRONT_URL: string;

  constructor(
    private prismaService: PrismaService,
    private mailerService: MailerService,
    private configService: ConfigService,
    private organizationUtilService: OrganizationUtilService,
  ) {
    this.APP_NAME = this.configService.getOrThrow('APP_NAME');
    this.APP_PUBLIC_URL = this.configService.getOrThrow('APP_PUBLIC_URL');
    this.FRONT_URL = this.configService.getOrThrow('FRONT_URL');
  }

  async getImage(text: string): Promise<Buffer> {
    const width: number = 600;
    const height: number = 600;
    const canvas = createCanvas(width, height);
    const context = canvas.getContext('2d');

    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillStyle = '#000';
    context.font = "250px 'Arial'";

    return await loadImage(mainDirectory + '/public/mail-background.png').then(
      (image) => {
        context.drawImage(image, 0, 0, width, height);
        context.fillText(text, width / 2, height / 2);
        return canvas.toBuffer('image/png');
      },
    );
  }

  async getOrganizationInviteContent(
    dto: GetOrganizationInviteContentDto,
    pending: UserPending,
  ) {
    const organization = await this.prismaService.organization.findFirstOrThrow(
      {
        where: { id: pending.organizationId },
        include: { mainOrganizations: { include: { mainOrganization: true } } },
      },
    );
    let content = `${this.APP_NAME} platformunda `;
    if (pending.type == UserPendingTypeEnum.INVITE_TO_ORGANIZATION) {
      content += `“${organization.name}” Şirketinin “Üyesi” olarak`;
    } else if (
      [
        UserPendingTypeEnum.CREATE_SUBSIDIARY,
        UserPendingTypeEnum.INVITE_SUBSIDIARY_MANAGER,
      ].includes(pending.type)
    ) {
      const relation = organization.mainOrganizations[0];
      if (relation.relationType == OrganizationTypeEnum.AFFILIATE) {
        const affiliateText = this.organizationUtilService.getAffiliateText(
          organization.affiliateType,
        );
        content += `“${relation.mainOrganization.name}” Şirketinin “alt şirket tipi (${affiliateText})” olan “${organization.name}” adına`;
      } else if (relation.relationType == OrganizationTypeEnum.SUPPLIER) {
        content += `“${relation.mainOrganization.name}” Şirketinin “Tedarikçisi” olarak “${organization.name}” adına`;
      } else {
        throw new BadRequestException('Geçersiz alt şirket türü.');
      }
    } else {
      throw new BadRequestException('Geçeriz davet türü.');
    }
    content += ` devam edebilmeniz için lütfen şifrenizi belirleyiniz. (${pending.email})`;
    return { content: content };
  }

  async sendOrganizationInvite(dto: OrganizationInviteMailDto) {
    const greetings = ['Merhaba'];
    if (dto.pending.name) {
      dto.url += `&name=${dto.pending.name}`;
      greetings.push(dto.pending.name);
    }
    if (dto.pending.surname) {
      dto.url += `&surname=${dto.pending.surname}`;
      greetings.push(dto.pending.surname);
    }
    const mailContent = await this.getOrganizationInviteContent(
      { token: dto.pending.token },
      dto.pending,
    );
    await this.mailerService.sendMail({
      to: dto.pending.email,
      subject: `${this.APP_NAME} Şirket Daveti`,
      template: './organization-invite',
      context: {
        APP_PUBLIC_URL: this.APP_PUBLIC_URL,
        url: dto.url,
        greeting: greetings.join(' '),
        content: mailContent.content,
      },
    });
  }

  async sendOrganizationInviteCode(dto: OrganizationInviteCodeMailDto) {
    const greetings = ['Merhaba'];
    if (dto.pending.name) {
      greetings.push(dto.pending.name);
    }
    if (dto.pending.surname) {
      greetings.push(dto.pending.surname);
    }
    await this.mailerService.sendMail({
      to: dto.pending.email,
      subject: `${this.APP_NAME} Şirket Daveti`,
      template: './organization-invite-code',
      context: {
        APP_PUBLIC_URL: this.APP_PUBLIC_URL,
        code: dto.pending.code,
        greeting: greetings.join(' '),
      },
    });
  }

  async sendActivation(dto: ActivationMailDto) {
    await this.mailerService.sendMail({
      to: dto.email,
      subject: `${this.APP_NAME} Hesap Aktivasyonu`,
      template: './activation',
      context: {
        APP_PUBLIC_URL: this.APP_PUBLIC_URL,
        url: this.FRONT_URL + `/activation?token=${dto.token}`,
        fullName: dto.fullName,
      },
    });
  }

  async sendForgotPassword(dto: ForgotPasswordMailDto) {
    await this.mailerService.sendMail({
      to: dto.email,
      subject: `${this.APP_NAME} Şifremi Unuttum`,
      template: './forgot-password',
      context: {
        APP_PUBLIC_URL: this.APP_PUBLIC_URL,
        url:
          this.FRONT_URL +
          `/change-password?token=${dto.token}&email=${dto.email}`,
        fullName: dto.fullName,
      },
    });
  }

  async sendOpinionHelp(user: UserDto, dto: OpinionHelpMailDto) {
    const organization = await this.prismaService.organization.findFirstOrThrow(
      {
        where: { id: user.organizationId },
      },
    );
    for (const su of dto.stakeholderUsers) {
      const url =
        this.FRONT_URL +
        `/opinions/helps?token=${su.token}&name=${su.stakeholderUser.name}&surname=${su.stakeholderUser.surname}&email=${su.stakeholderUser.email}`;
      await this.mailerService.sendMail({
        to: su.stakeholderUser.email,
        subject: `${this.APP_NAME} Paydaş Görüşleri`,
        template: './opinion-help',
        context: {
          APP_PUBLIC_URL: this.APP_PUBLIC_URL,
          url: url,
          fullName: `${su.stakeholderUser.name} ${su.stakeholderUser.surname}`,
          organizationName: organization.name,
        },
      });
    }
  }

  async sendGovernanceInvite(dto: GovernanceInviteMailDto) {
    await this.mailerService.sendMail({
      to: dto.email,
      subject: `${this.APP_NAME} Yönetişim Daveti`,
      template: './governance-invite',
      context: {
        APP_PUBLIC_URL: this.APP_PUBLIC_URL,
        url: dto.url,
        fullName: dto.fullName,
        organizationName: dto.organizationName,
      },
    });
  }

  async sendGovernanceInviteCode(dto: GovernanceInviteCodeMailDto) {
    const greetings = ['Merhaba'];
    if (dto.memberProfile.name) {
      greetings.push(dto.memberProfile.name);
    }
    if (dto.memberProfile.surname) {
      greetings.push(dto.memberProfile.surname);
    }
    await this.mailerService.sendMail({
      to: dto.memberProfile.email,
      subject: `${this.APP_NAME} Yönetişim Davet Kodu`,
      template: './governance-invite-code',
      context: {
        APP_PUBLIC_URL: this.APP_PUBLIC_URL,
        code: dto.memberProfile.code,
        greeting: greetings.join(' '),
      },
    });
  }

  async sendMetricSupervisorInvite(
    user: UserDto,
    dto: MetricSupervisorInviteMailDto,
  ) {
    const organization = await this.prismaService.organization.findFirstOrThrow(
      {
        where: { id: user.organizationId },
      },
    );
    await this.mailerService.sendMail({
      to: dto.supervisorUser.email,
      subject: `${this.APP_NAME} Metrik Veri Sorumlusu Daveti`,
      template: './metric-supervisor-invite',
      context: {
        APP_PUBLIC_URL: this.APP_PUBLIC_URL,
        fullName: `${dto.supervisorUser.name} ${dto.supervisorUser.surname}`,
        organizationName: organization.name,
        email: dto.supervisorUser.email,
        password: dto.password,
        url: this.FRONT_URL,
      },
    });
  }

  async sendNotification(dto: NotificationMailDto) {
    await this.mailerService.sendMail({
      to: dto.email,
      subject: `${this.APP_NAME} Okunmamış Bildirimler`,
      template: './notification',
      context: {
        APP_PUBLIC_URL: this.APP_PUBLIC_URL,
        fullName: `${dto.name} ${dto.surname}`,
        unreadCount: dto.unreadCount,
        url: this.FRONT_URL,
      },
    });
  }
}
