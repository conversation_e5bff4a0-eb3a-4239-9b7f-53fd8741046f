import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { MailService } from './mail.service';
import { Inject } from '@nestjs/common';
import { NotificationMailDto } from './dtos/notification-mail.dto';

@Processor('mail')
export class MailProcessor extends WorkerHost {
  constructor(@Inject(MailService) private readonly mailService: MailService) {
    super();
  }

  async process(job: Job<NotificationMailDto, any, string>): Promise<void> {
    await this.mailService.sendNotification(job.data);
  }
}
