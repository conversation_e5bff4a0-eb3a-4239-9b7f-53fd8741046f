import { IsArray, IsObject, IsString, ValidateNested } from 'class-validator';
import { Request } from 'express';
import { Type } from 'class-transformer';
import { StakeholderUser } from '@prisma/client';

export class OpinionHelpStakeholderUserMailDto {
  @IsString()
  token: string;

  @IsObject()
  stakeholderUser: StakeholderUser;
}

export class OpinionHelpMailDto {
  @IsObject()
  request: Request;

  @ValidateNested({ each: true })
  @Type(() => OpinionHelpStakeholderUserMailDto)
  @IsArray()
  stakeholderUsers: OpinionHelpStakeholderUserMailDto[];
}
