import { Controller, Get, Query, StreamableFile } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { GeneralResponseDto } from '../common/dtos/general-response.dto';
import { MailService } from './mail.service';
import { Public } from '../common/decorators/auth.decorator';
import { GetOrganizationInviteContentDto } from './dtos/get-organization-invite-content.dto';
import { MailValidation } from './mail.validation';

@ApiTags('Mails')
@Controller('mails')
export class MailController {
  constructor(
    private mailValidation: MailValidation,
    private mailService: MailService,
  ) {}

  @Get('image')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getImage(@Query('text') text: string): Promise<StreamableFile> {
    const buffer = await this.mailService.getImage(text);
    return new StreamableFile(buffer, { type: 'image/png' });
  }

  @Get('invitation/contents')
  @Public()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getOrganizationInviteContent(
    @Query() dto: GetOrganizationInviteContentDto,
  ): Promise<GeneralResponseDto> {
    const pending = await this.mailValidation.getOrganizationInviteContent(dto);
    const result = await this.mailService.getOrganizationInviteContent(
      dto,
      pending,
    );
    return new GeneralResponseDto().setData(result);
  }
}
