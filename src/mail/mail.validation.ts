import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService } from '../common/services/prisma.service';
import { GetOrganizationInviteContentDto } from './dtos/get-organization-invite-content.dto';
import { UserPending } from '@prisma/client';

@Injectable()
export class MailValidation {
  constructor(private prismaService: PrismaService) {}

  async getOrganizationInviteContent(
    dto: GetOrganizationInviteContentDto,
  ): Promise<UserPending> {
    const pending = await this.prismaService.userPending.findFirst({
      where: {
        token: dto.token,
        OR: [{ tokenExpiredAt: null }, { tokenExpiredAt: { gt: new Date() } }],
      },
    });
    if (!pending) {
      throw new BadRequestException('Geçersiz işlem.');
    }
    return pending;
  }
}
