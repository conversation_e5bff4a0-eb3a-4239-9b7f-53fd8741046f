export enum ChartModelTypeEnum {
  METRIC = 1,
  // FINANCIAL = 2,
  // IMPACT = 3,
  // STRATEGY = 4,
}

export enum ChartXAxisEnum {
  METRIC_PERFORMANCE = 1,
  METRIC_PERFORMANCE_AND_TARGETS = 2,
  METRIC_FACILITIES = 3,
}

export enum ChartYAxisEnum {
  METRIC_YEAR_VALUE = 1,
}

export enum ChartGraphTypeEnum {
  LINE = 1,
  BAR = 2,
  PIE = 3,
}

export enum ChartFilterKeyEnum {
  FACILITY = 1,
  YEAR = 2,
}

export enum ChartFilterTypeEnum {
  SINGLE_SELECT = 1,
  MULTI_SELECT = 2,
}

export enum MetricChartColors {
  SHORT_GOAL = '#bbf7d0',
  MEDIUM_GOAL = '#86efac',
  LONG_GOAL = '#059669',
}
