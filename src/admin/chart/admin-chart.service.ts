import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common/services/prisma.service';
import { CreateChartTemplateDto } from './dto/create-chart-template.dto';
import { CreateChartDto } from './dto/create-chart.dto';
import { ChartModelTypeEnum } from './enums/chart.enum';
import { DeleteChartDto } from './dto/delete-chart.dto';
import { GetUniqueChartsDto } from './dto/get-unique-chart';

@Injectable()
export class AdminChartService {
  constructor(private prisma: PrismaService) {}

  async createTemplate(dto: CreateChartTemplateDto) {
    return this.prisma.chartTemplate.create({
      data: {
        modelType: ChartModelTypeEnum.METRIC,
        title: dto.title,
        xAxis: dto.xAxis,
        yAxis: dto.yAxis,
        xAxisLabel: dto.xAxisLabel,
        yAxisLabel: dto.yAxisLabel,
        graphType: dto.graphType,
        filterKey: dto.filterKey,
        filterType: dto.filterType,
      },
    });
  }

  async getTemplates(modelType?: ChartModelTypeEnum) {
    return this.prisma.chartTemplate.findMany({
      where: {
        deletedAt: null,
        ...(modelType && { modelType }),
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async createMetricChart(dto: CreateChartDto) {
    const charts = dto.metricIds.flatMap((metricId) => {
      return dto.templateIds.map((templateId) => {
        return this.prisma.chart.create({
          data: {
            templateId: templateId,
            defaultId: metricId,
          },
        });
      });
    });
    return this.prisma.$transaction(charts);
  }

  async getDefaultMetricsCharts() {
    const allMetrics = await this.prisma.metricDefault.findMany({
      where: { deletedAt: null },
      select: {
        id: true,
        metricName: true,
        categoryType: true,
        unitType: true,
        note: true,
      },
    });

    const metricIds = allMetrics.map((metric) => metric.id);
    const charts = await this.prisma.chart.findMany({
      where: {
        template: { modelType: ChartModelTypeEnum.METRIC },
        defaultId: { in: metricIds },
        deletedAt: null,
      },
      include: {
        template: true,
      },
      orderBy: { createdAt: 'desc' },
    });

    const result = allMetrics.map((metric) => {
      const metricCharts = charts
        .filter((chart) => chart.defaultId === metric.id)
        .map((chart) => ({
          id: chart.id,
          title: chart.template.title,
          xAxis: chart.template.xAxis,
          yAxis: chart.template.yAxis,
          xAxisLabel: chart.template.xAxisLabel,
          yAxisLabel: chart.template.yAxisLabel,
          graphType: chart.template.graphType,
          filterKey: chart.template.filterKey,
          filterType: chart.template.filterType,
        }));

      return {
        ...metric,
        charts: metricCharts,
      };
    });

    return result;
  }

  async getUniqueCharts(dto: GetUniqueChartsDto) {
    const templates = await this.prisma.chartTemplate.findMany({
      where: {
        modelType: dto.modelType,
        deletedAt: null,
      },
    });

    return templates;
  }

  async deleteChart(dto: DeleteChartDto) {
    await this.prisma.chart.delete({
      where: { id: dto.chartId },
    });
  }
}
