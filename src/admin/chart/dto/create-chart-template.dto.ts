import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateChartTemplateDto {
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsNumber()
  xAxis: number;

  @ApiProperty()
  @IsNumber()
  yAxis: number;

  @ApiProperty()
  @IsString()
  xAxisLabel: string;

  @ApiProperty()
  @IsString()
  yAxisLabel: string;

  @ApiProperty()
  @IsNumber()
  graphType: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  filterKey?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  filterType?: number;
}
