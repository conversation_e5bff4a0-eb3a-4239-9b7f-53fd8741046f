import { IsEnum, IsN<PERSON>ber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ChartModelTypeEnum } from '../enums/chart.enum';
import { Transform } from 'class-transformer';

export class GetUniqueChartsDto {
  @ApiProperty({ type: Number, enum: ChartModelTypeEnum })
  @Transform(({ value }) => +value)
  @IsNumber()
  @IsEnum(ChartModelTypeEnum)
  modelType: ChartModelTypeEnum;
}
