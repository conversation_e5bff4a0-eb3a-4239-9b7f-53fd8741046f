import { Body, Controller, Delete, Get, Post, Query } from '@nestjs/common';
import { AdminChartService } from './admin-chart.service';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { GeneralResponseDto } from 'src/common/dtos/general-response.dto';
import { Admin } from 'src/common/decorators/admin.decorator';
import { CreateChartTemplateDto } from './dto/create-chart-template.dto';
import { CreateChartDto } from './dto/create-chart.dto';
import { DeleteChartDto } from './dto/delete-chart.dto';
import { GetUniqueChartsDto } from './dto/get-unique-chart';
import { ChartModelTypeEnum } from './enums/chart.enum';

@Admin()
@ApiTags('Admin/Charts')
@Controller('admin/charts')
export class AdminChartController {
  constructor(private readonly chartService: AdminChartService) {}

  @Post('template')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createTemplate(
    @Body() dto: CreateChartTemplateDto,
  ): Promise<GeneralResponseDto> {
    const template = await this.chartService.createTemplate(dto);
    return new GeneralResponseDto().setData(template);
  }

  @Get('templates')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  @ApiQuery({ name: 'modelType', enum: ChartModelTypeEnum, required: false })
  async getTemplates(
    @Query('modelType') modelType?: ChartModelTypeEnum,
  ): Promise<GeneralResponseDto> {
    const templates = await this.chartService.getTemplates(modelType);
    return new GeneralResponseDto().setData(templates);
  }

  @Post('metric')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async createMetricChart(
    @Body() dto: CreateChartDto,
  ): Promise<GeneralResponseDto> {
    const createdCharts = await this.chartService.createMetricChart(dto);
    return new GeneralResponseDto().setData(createdCharts);
  }

  @Get('default-metric')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getMetricCharts(): Promise<GeneralResponseDto> {
    const charts = await this.chartService.getDefaultMetricsCharts();
    return new GeneralResponseDto().setData(charts);
  }

  @Get('unique')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getUniqueCharts(
    @Query() dto: GetUniqueChartsDto,
  ): Promise<GeneralResponseDto> {
    const charts = await this.chartService.getUniqueCharts(dto);
    return new GeneralResponseDto().setData(charts);
  }

  @Delete('metric')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async deleteChart(@Body() dto: DeleteChartDto): Promise<GeneralResponseDto> {
    await this.chartService.deleteChart(dto);
    return new GeneralResponseDto();
  }
}
