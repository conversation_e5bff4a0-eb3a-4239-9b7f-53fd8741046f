import { Controller, Get } from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { Admin } from 'src/common/decorators/admin.decorator';
import { GeneralResponseDto } from 'src/common/dtos/general-response.dto';
import { AdminMetricService } from './admin-metric.service';

@Admin()
@ApiTags('Admin/Metrics')
@Controller('admin/metrics')
export class AdminMetricController {
  constructor(private adminMetricService: AdminMetricService) {}

  @Get('defaults')
  @ApiBearerAuth()
  @ApiOkResponse({ type: GeneralResponseDto })
  async getDefaults() {
    const defaults = await this.adminMetricService.getDefaults();
    return new GeneralResponseDto().setData(defaults);
  }
}
