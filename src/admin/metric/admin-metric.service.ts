import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/common/services/prisma.service';

@Injectable()
export class AdminMetricService {
  constructor(private prismaService: PrismaService) {}

  async getDefaults() {
    const defaults = await this.prismaService.metricDefault.findMany({
      select: {
        id: true,
        metricName: true,
        categoryType: true,
        unitType: true,
        note: true,
      },
    });
    return defaults;
  }
}
