{"name": "esg-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.740.0", "@aws-sdk/credential-providers": "^3.738.0", "@aws-sdk/lib-storage": "^3.740.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.7", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.7", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.7", "@nestjs/platform-socket.io": "^11.0.7", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.1", "@nestjs/swagger": "^11.0.3", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.0.7", "@prisma/client": "^6.7.0", "axios": "1.8.2", "bcrypt": "^5.1.1", "bullmq": "^5.56.3", "canvas": "^3.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "date-fns": "^4.1.0", "file-type": "^20.4.1", "handlebars": "^4.7.8", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "prisma-soft-delete-middleware": "^1.3.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "devDependencies": {"@nestjs/cli": "^11.0.2", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.13.0", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^8.22.0", "@typescript-eslint/parser": "^8.22.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.7.0", "prettier": "^3.4.2", "prisma": "^6.7.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}