name: <PERSON><PERSON> Pipeline

on:
  push:
    branches:
      - test

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: trigger jenkins pipeline
        uses: appleboy/jenkins-action@v0.0.2
        with:
          url: "https://jenkins.25proje.tech"
          user: ${{ secrets.JENKINS_USER }}
          token: ${{ secrets.JENKINS_TOKEN }}
          job: "esg/test-back"