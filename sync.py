import mysql.connector
from datetime import datetime

# Kaynak ve hedef veritabanı bilgileri
source_config = {
    "host": "127.0.0.1",
    "user": "root",
    "password": "password",
    "database": "greentim-main"
}

target_config = {
    "host": "127.0.0.1",
    "user": "root",
    "password": "password",
    "database": "carbon-test"
}

# Senkronize edilecek tablolar
tables = [
    "form_categories",
    "forms",
    "form_inputs",
    "form_groups",
    "form_group_values",
    "form_input_to_groups",
    "form_commons",
    "form_common_groups",
    "form_operations",
    "form_multipliers",
    "form_results",
    "form_result_values",
    "iso_standard_sources",
    "iso_standard_classifications",
    "languages",
]

def backup_database(target_cursor):
    # Yeni yedek veritabanı oluştur
    backup_db_name = f"{target_config['database']}-{datetime.now().strftime('%Y-%m-%d-%H-%M-%S')}"
    target_cursor.execute(f"CREATE DATABASE `{backup_db_name}`")
    print(f"{backup_db_name} veritabanı oluşturuldu.")

    # Kaynak veritabanındaki tabloları yedek veritabanına kopyala
    target_cursor.execute(f"USE `{target_config['database']}`")
    target_cursor.execute("SHOW TABLES")
    tables = [row[0] for row in target_cursor.fetchall()]

    for table in tables:
        target_cursor.execute(f"CREATE TABLE `{backup_db_name}`.`{table}` LIKE `{target_config['database']}`.`{table}`")
        target_cursor.execute(f"INSERT INTO `{backup_db_name}`.`{table}` SELECT * FROM `{target_config['database']}`.`{table}`")

    print(f"{target_config['database']} veritabanının yedeği {backup_db_name} adıyla tamamlandı.")

def sync_table(source_cursor, target_cursor, table_name):
    print(f"Senkronize ediliyor: {table_name}")

    # Kaynak tablodaki tüm veriyi seç
    source_cursor.execute(f"SELECT * FROM `{table_name}`")
    rows = source_cursor.fetchall()

    # Eğer tablo boşsa işlemi atla
    if not rows:
        print(f"{table_name} tablosu boş, atlanıyor.")
        return

    # Tablonun sütun adlarını al
    columns = [f"`{desc[0]}`" for desc in source_cursor.description]
    column_names = ", ".join(columns)
    placeholders = ", ".join(["%s"] * len(columns))

    # Verileri hedef tabloya ekle veya güncelle
    insert_query = f"""
    INSERT INTO `{table_name}` ({column_names}) VALUES ({placeholders})
    ON DUPLICATE KEY UPDATE {", ".join([f"{col} = VALUES({col})" for col in columns])};
    """
    #print(insert_query)
    target_cursor.executemany(insert_query, rows)
    print(f"{table_name} tablosu senkronize edildi.")

def main():
    source_cursor = None
    target_cursor = None
    source_conn = None
    target_conn = None

    try:
        # Kaynak ve hedef veritabanlarına bağlan
        source_conn = mysql.connector.connect(**source_config)
        target_conn = mysql.connector.connect(**target_config)

        source_cursor = source_conn.cursor()
        target_cursor = target_conn.cursor()

        # Hedef veritabanını yedekleme
        backup_database(target_cursor)

        for table in tables:
            sync_table(source_cursor, target_cursor, table)

        # Değişiklikleri kaydet
        target_conn.commit()
        print("Tüm tablolar senkronize edildi.")

    except mysql.connector.Error as err:
        print(f"Bir hata oluştu: {err}")

    finally:
        # Bağlantıları kapat
        if source_cursor:
            source_cursor.close()
        if target_cursor:
            target_cursor.close()
        if source_conn:
            source_conn.close()
        if target_conn:
            target_conn.close()

if __name__ == "__main__":
    main()
